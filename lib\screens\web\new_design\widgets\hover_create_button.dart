import 'package:flutter/material.dart';

/// A custom button that changes the icon color to blue when hovered
class HoverCreateButton extends StatefulWidget {
  final String text;
  final VoidCallback onPressed;

  const HoverCreateButton({
    super.key,
    required this.text,
    required this.onPressed,
  });

  @override
  State<HoverCreateButton> createState() => _HoverCreateButtonState();
}

class _HoverCreateButtonState extends State<HoverCreateButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: InkWell(
        onTap: widget.onPressed,
        splashColor: Colors.transparent,
        hoverColor: Colors.transparent,
        highlightColor: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: isHovered ? Colors.white : Colors.transparent,
            border: Border.all(
              color: isHovered ? const Color(0xff0058FF) : Colors.transparent,
              width: 1.0,
            ),
            borderRadius: BorderRadius.circular(2),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Icon(
                Icons.add,
                size: 16,
                color: isHovered ? const Color(0xff0058FF) : Colors.black,
              ),
              const SizedBox(width: 4),
              Text(
                widget.text,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  fontFamily: "TiemposText",
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
