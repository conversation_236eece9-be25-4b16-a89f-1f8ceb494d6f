import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'dart:async';
import 'dart:convert';
import 'dart:math';
import '../utils/callback_interpreter.dart';

/// Extension on Color to provide hex string conversion
extension SchedulerColorExtension on Color {
  /// Converts a Color to a hex string (without the # prefix)
  String toHexString() {
    return '${r.round().toRadixString(16).padLeft(2, '0')}${g.round().toRadixString(16).padLeft(2, '0')}${b.round().toRadixString(16).padLeft(2, '0')}';
  }
}

/// Enum defining the different view modes for the scheduler
enum SchedulerViewMode {
  day,
  week,
  month,
  agenda,
}

/// Enum defining the different event display modes
enum EventDisplayMode {
  block,
  line,
  minimal,
  detailed,
}

/// Class representing a scheduled event
class SchedulerEvent {
  final String id;
  final String title;
  final String? description;
  final DateTime start;
  final DateTime end;
  final Color color;
  final bool isAllDay;
  final Map<String, dynamic>? metadata;

  SchedulerEvent({
    required this.id,
    required this.title,
    this.description,
    required this.start,
    required this.end,
    this.color = Colors.blue,
    this.isAllDay = false,
    this.metadata,
  });

  /// Creates a SchedulerEvent from a JSON map
  factory SchedulerEvent.fromJson(Map<String, dynamic> json) {
    return SchedulerEvent(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      start: DateTime.parse(json['start'] as String),
      end: DateTime.parse(json['end'] as String),
      color: json['color'] != null ? _parseColor(json['color']) : Colors.blue,
      isAllDay: json['isAllDay'] as bool? ?? false,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Converts the event to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'start': start.toIso8601String(),
      'end': end.toIso8601String(),
      'color': '#${color.toHexString()}',
      'isAllDay': isAllDay,
      'metadata': metadata,
    };
  }

  /// Parses a color from a string or map
  static Color _parseColor(dynamic colorValue) {
    if (colorValue is String) {
      if (colorValue.startsWith('#')) {
        // Parse hex color
        String hex = colorValue.replaceFirst('#', '');
        if (hex.length == 3) {
          // Convert 3-digit hex to 6-digit
          hex = hex.split('').map((char) => char + char).join('');
        }
        if (hex.length == 6) {
          hex = 'FF$hex'; // Add alpha channel if not present
        }
        return Color(int.parse(hex, radix: 16));
      } else {
        // Parse named color
        switch (colorValue.toLowerCase()) {
          case 'red': return Colors.red;
          case 'blue': return Colors.blue;
          case 'green': return Colors.green;
          case 'yellow': return Colors.yellow;
          case 'orange': return Colors.orange;
          case 'purple': return Colors.purple;
          case 'pink': return Colors.pink;
          case 'brown': return Colors.brown;
          case 'grey':
          case 'gray': return Colors.grey;
          case 'black': return Colors.black;
          case 'white': return Colors.white;
          case 'transparent': return Colors.transparent;
          default: return Colors.blue;
        }
      }
    } else if (colorValue is Map) {
      // Parse RGBA color
      final int r = colorValue['r'] as int? ?? 0;
      final int g = colorValue['g'] as int? ?? 0;
      final int b = colorValue['b'] as int? ?? 0;
      final double a = colorValue['a'] != null ? (colorValue['a'] as num).toDouble() : 1.0;
      return Color.fromRGBO(r, g, b, a);
    }
    return Colors.blue; // Default color
  }

  /// Create a copy of this event with some fields replaced
  SchedulerEvent copyWith({
    String? id,
    String? title,
    String? description,
    DateTime? start,
    DateTime? end,
    Color? color,
    bool? isAllDay,
    Map<String, dynamic>? metadata,
  }) {
    return SchedulerEvent(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      start: start ?? this.start,
      end: end ?? this.end,
      color: color ?? this.color,
      isAllDay: isAllDay ?? this.isAllDay,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// A comprehensive scheduler widget that can be configured in various ways
class SchedulerWidget extends StatefulWidget {
  // View configuration
  final SchedulerViewMode initialViewMode;
  final bool allowViewModeChange;
  final List<SchedulerViewMode> availableViewModes;
  final DateTime? initialDate;
  final bool showNavigationArrows;
  final bool showViewModeSelector;
  final bool showDateTitle;
  final bool showToolbar;

  // Time configuration
  final TimeOfDay dayStartTime;
  final TimeOfDay dayEndTime;
  final int timeSlotMinutes;
  final bool showWeekends;
  final List<int> workDays; // 1 = Monday, 7 = Sunday
  final bool highlightWorkingHours;
  final TimeOfDay workingHoursStart;
  final TimeOfDay workingHoursEnd;

  // Event configuration
  final List<SchedulerEvent> events;
  final EventDisplayMode eventDisplayMode;
  final bool allowEventCreation;
  final bool allowEventEditing;
  final bool allowEventDeletion;
  final bool allowEventDragging;
  final bool allowEventResizing;

  // Appearance
  final Color backgroundColor;
  final Color timelineColor;
  final Color gridLineColor;
  final Color todayHighlightColor;
  final Color selectedDateColor;
  final double timelineWidth;
  final double eventBorderRadius;
  final bool showEventTime;
  final bool showEventDuration;
  final bool showAllDayEvents;
  final bool showCurrentTimeIndicator;
  final Color currentTimeIndicatorColor;
  final double fontSize;
  final FontWeight fontWeight;
  final String locale;
  final bool isDarkTheme;
  final bool hasBorder;
  final double borderRadius;
  final Color borderColor;
  final double borderWidth;
  final bool hasShadow;
  final double elevation;
  final double headerHeight;
  final double timeSlotHeight;
  final String? headerDateFormat;
  final String? timeFormat;

  // Callbacks
  final Function(SchedulerEvent)? onEventTap;
  final Function(SchedulerEvent)? onEventLongPress;
  final Function(DateTime, TimeOfDay)? onTimeSlotTap;
  final Function(SchedulerEvent)? onEventCreated;
  final Function(SchedulerEvent)? onEventUpdated;
  final Function(SchedulerEvent)? onEventDeleted;
  final Function(SchedulerViewMode)? onViewModeChanged;
  final Function(DateTime)? onDateChanged;

  // Advanced interaction properties
  /// Callback for when the widget is hovered
  final void Function(bool)? onHover;

  /// Callback for when the widget is focused
  final void Function(bool)? onFocus;

  /// Focus node for the widget
  final FocusNode? focusNode;

  /// Whether the widget should autofocus
  final bool autofocus;

  /// Color to use when the widget is focused
  final Color? focusColor;

  /// Whether to enable feedback when the widget is interacted with
  final bool enableFeedback;

  /// Callback for when the widget is double-tapped
  final VoidCallback? onDoubleTap;

  /// Callback for when the widget is long-pressed
  final VoidCallback? onLongPress;

  // Accessibility properties
  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Whether to exclude the widget from semantics
  final bool excludeFromSemantics;

  // JSON configuration properties
  /// Callbacks defined in JSON
  final Map<String, dynamic>? jsonCallbacks;

  /// Whether to use JSON callbacks
  final bool useJsonCallbacks;

  /// State to pass to the callback interpreter
  final Map<String, dynamic>? callbackState;

  /// Custom callback handlers
  final Map<String, Function>? customCallbackHandlers;

  /// JSON configuration
  final Map<String, dynamic>? jsonConfig;

  /// Whether to use JSON styling
  final bool useJsonStyling;

  /// Whether to use JSON formatting
  final bool useJsonFormatting;

  // Scheduler-specific JSON configuration
  /// Whether to use JSON scheduler configuration
  final bool useJsonSchedulerConfig;

  /// Scheduler-specific JSON configuration
  final Map<String, dynamic>? schedulerConfig;

  const SchedulerWidget({
    super.key,
    this.initialViewMode = SchedulerViewMode.week,
    this.allowViewModeChange = true,
    this.availableViewModes = const [
      SchedulerViewMode.day,
      SchedulerViewMode.week,
      SchedulerViewMode.month,
      SchedulerViewMode.agenda
    ],
    this.initialDate,
    this.showNavigationArrows = true,
    this.showViewModeSelector = true,
    this.showDateTitle = true,
    this.showToolbar = true,
    this.dayStartTime = const TimeOfDay(hour: 8, minute: 0),
    this.dayEndTime = const TimeOfDay(hour: 18, minute: 0),
    this.timeSlotMinutes = 30,
    this.showWeekends = true,
    this.workDays = const [1, 2, 3, 4, 5], // Monday to Friday
    this.highlightWorkingHours = true,
    this.workingHoursStart = const TimeOfDay(hour: 9, minute: 0),
    this.workingHoursEnd = const TimeOfDay(hour: 17, minute: 0),
    this.events = const [],
    this.eventDisplayMode = EventDisplayMode.block,
    this.allowEventCreation = true,
    this.allowEventEditing = true,
    this.allowEventDeletion = true,
    this.allowEventDragging = true,
    this.allowEventResizing = true,
    this.backgroundColor = Colors.white,
    this.timelineColor = Colors.grey,
    this.gridLineColor = Colors.grey,
    this.todayHighlightColor = Colors.blue,
    this.selectedDateColor = Colors.blue,
    this.timelineWidth = 60,
    this.eventBorderRadius = 4.0,
    this.showEventTime = true,
    this.showEventDuration = false,
    this.showAllDayEvents = true,
    this.showCurrentTimeIndicator = true,
    this.currentTimeIndicatorColor = Colors.red,
    this.fontSize = 14.0,
    this.fontWeight = FontWeight.normal,
    this.locale = 'en_US',
    this.isDarkTheme = false,
    this.hasBorder = true,
    this.borderRadius = 8.0,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.headerHeight = 50.0,
    this.timeSlotHeight = 60.0,
    this.headerDateFormat,
    this.timeFormat,
    this.onEventTap,
    this.onEventLongPress,
    this.onTimeSlotTap,
    this.onEventCreated,
    this.onEventUpdated,
    this.onEventDeleted,
    this.onViewModeChanged,
    this.onDateChanged,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
    this.focusColor,
    this.enableFeedback = true,
    this.onDoubleTap,
    this.onLongPress,
    // Accessibility properties
    this.semanticsLabel,
    this.excludeFromSemantics = false,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
    // Scheduler-specific JSON configuration
    this.useJsonSchedulerConfig = false,
    this.schedulerConfig,
  });

  /// Creates a SchedulerWidget from a JSON map
  ///
  /// This factory constructor allows for creating a fully configured scheduler widget
  /// from a JSON object, making it easy to load configurations from external sources.
  factory SchedulerWidget.fromJson(dynamic jsonData) {
    // Handle string JSON input
    Map<String, dynamic> json;
    if (jsonData is String) {
      json = jsonDecode(jsonData) as Map<String, dynamic>;
    } else if (jsonData is Map<String, dynamic>) {
      json = jsonData;
    } else {
      throw ArgumentError('Invalid JSON data: $jsonData');
    }

    // Parse view configuration
    final initialViewMode = json['initialViewMode'] != null
        ? SchedulerWidgetJsonHelpers._parseViewMode(json['initialViewMode'])
        : SchedulerViewMode.week;

    final allowViewModeChange = json['allowViewModeChange'] as bool? ?? true;

    List<SchedulerViewMode> availableViewModes = [
      SchedulerViewMode.day,
      SchedulerViewMode.week,
      SchedulerViewMode.month,
      SchedulerViewMode.agenda
    ];

    if (json['availableViewModes'] != null && json['availableViewModes'] is List) {
      final viewModesList = json['availableViewModes'] as List;
      availableViewModes = viewModesList
          .map((mode) => SchedulerWidgetJsonHelpers._parseViewMode(mode))
          .toList();
    }

    final initialDate = json['initialDate'] != null
        ? SchedulerWidgetJsonHelpers._parseDateTime(json['initialDate'])
        : null;

    final showNavigationArrows = json['showNavigationArrows'] as bool? ?? true;
    final showViewModeSelector = json['showViewModeSelector'] as bool? ?? true;
    final showDateTitle = json['showDateTitle'] as bool? ?? true;
    final showToolbar = json['showToolbar'] as bool? ?? true;

    // Parse time configuration
    final dayStartTime = json['dayStartTime'] != null
        ? SchedulerWidgetJsonHelpers._parseTimeOfDay(json['dayStartTime'])
        : const TimeOfDay(hour: 8, minute: 0);

    final dayEndTime = json['dayEndTime'] != null
        ? SchedulerWidgetJsonHelpers._parseTimeOfDay(json['dayEndTime'])
        : const TimeOfDay(hour: 18, minute: 0);

    final timeSlotMinutes = json['timeSlotMinutes'] as int? ?? 30;
    final showWeekends = json['showWeekends'] as bool? ?? true;

    List<int> workDays = [1, 2, 3, 4, 5]; // Monday to Friday by default
    if (json['workDays'] != null && json['workDays'] is List) {
      workDays = List<int>.from(json['workDays'] as List);
    }

    final highlightWorkingHours = json['highlightWorkingHours'] as bool? ?? true;

    final workingHoursStart = json['workingHoursStart'] != null
        ? SchedulerWidgetJsonHelpers._parseTimeOfDay(json['workingHoursStart'])
        : const TimeOfDay(hour: 9, minute: 0);

    final workingHoursEnd = json['workingHoursEnd'] != null
        ? SchedulerWidgetJsonHelpers._parseTimeOfDay(json['workingHoursEnd'])
        : const TimeOfDay(hour: 17, minute: 0);

    // Parse events
    List<SchedulerEvent> events = [];
    if (json['events'] != null && json['events'] is List) {
      final eventsList = json['events'] as List;
      events = eventsList
          .map((eventJson) => SchedulerEvent.fromJson(eventJson as Map<String, dynamic>))
          .toList();
    }

    // Parse event configuration
    final eventDisplayMode = json['eventDisplayMode'] != null
        ? SchedulerWidgetJsonHelpers._parseEventDisplayMode(json['eventDisplayMode'])
        : EventDisplayMode.block;

    final allowEventCreation = json['allowEventCreation'] as bool? ?? true;
    final allowEventEditing = json['allowEventEditing'] as bool? ?? true;
    final allowEventDeletion = json['allowEventDeletion'] as bool? ?? true;
    final allowEventDragging = json['allowEventDragging'] as bool? ?? true;
    final allowEventResizing = json['allowEventResizing'] as bool? ?? true;

    // Parse appearance
    final backgroundColor = json['backgroundColor'] != null
        ? SchedulerWidgetJsonHelpers._parseColor(json['backgroundColor'])
        : Colors.white;

    final timelineColor = json['timelineColor'] != null
        ? SchedulerWidgetJsonHelpers._parseColor(json['timelineColor'])
        : Colors.grey;

    final gridLineColor = json['gridLineColor'] != null
        ? SchedulerWidgetJsonHelpers._parseColor(json['gridLineColor'])
        : Colors.grey;

    final todayHighlightColor = json['todayHighlightColor'] != null
        ? SchedulerWidgetJsonHelpers._parseColor(json['todayHighlightColor'])
        : Colors.blue;

    final selectedDateColor = json['selectedDateColor'] != null
        ? SchedulerWidgetJsonHelpers._parseColor(json['selectedDateColor'])
        : Colors.blue;

    final timelineWidth = json['timelineWidth'] != null
        ? (json['timelineWidth'] as num).toDouble()
        : 60.0;

    final eventBorderRadius = json['eventBorderRadius'] != null
        ? (json['eventBorderRadius'] as num).toDouble()
        : 4.0;

    final showEventTime = json['showEventTime'] as bool? ?? true;
    final showEventDuration = json['showEventDuration'] as bool? ?? false;
    final showAllDayEvents = json['showAllDayEvents'] as bool? ?? true;
    final showCurrentTimeIndicator = json['showCurrentTimeIndicator'] as bool? ?? true;

    final currentTimeIndicatorColor = json['currentTimeIndicatorColor'] != null
        ? SchedulerWidgetJsonHelpers._parseColor(json['currentTimeIndicatorColor'])
        : Colors.red;

    final fontSize = json['fontSize'] != null
        ? (json['fontSize'] as num).toDouble()
        : 14.0;

    final fontWeight = json['fontWeight'] != null
        ? SchedulerWidgetJsonHelpers._parseFontWeight(json['fontWeight'])
        : FontWeight.normal;

    final locale = json['locale'] as String? ?? 'en_US';
    final isDarkTheme = json['isDarkTheme'] as bool? ?? false;
    final hasBorder = json['hasBorder'] as bool? ?? true;

    final borderRadius = json['borderRadius'] != null
        ? (json['borderRadius'] as num).toDouble()
        : 8.0;

    final borderColor = json['borderColor'] != null
        ? SchedulerWidgetJsonHelpers._parseColor(json['borderColor'])
        : Colors.grey;

    final borderWidth = json['borderWidth'] != null
        ? (json['borderWidth'] as num).toDouble()
        : 1.0;

    final hasShadow = json['hasShadow'] as bool? ?? false;

    final elevation = json['elevation'] != null
        ? (json['elevation'] as num).toDouble()
        : 2.0;

    final headerHeight = json['headerHeight'] != null
        ? (json['headerHeight'] as num).toDouble()
        : 50.0;

    final timeSlotHeight = json['timeSlotHeight'] != null
        ? (json['timeSlotHeight'] as num).toDouble()
        : 60.0;

    final headerDateFormat = json['headerDateFormat'] as String?;
    final timeFormat = json['timeFormat'] as String?;

    // Parse JSON configuration properties
    final jsonCallbacks = json['jsonCallbacks'] as Map<String, dynamic>?;
    final useJsonCallbacks = json['useJsonCallbacks'] as bool? ?? false;
    final callbackState = json['callbackState'] as Map<String, dynamic>?;
    final jsonConfig = json['jsonConfig'] as Map<String, dynamic>?;
    final useJsonStyling = json['useJsonStyling'] as bool? ?? false;
    final useJsonFormatting = json['useJsonFormatting'] as bool? ?? false;
    final useJsonSchedulerConfig = json['useJsonSchedulerConfig'] as bool? ?? false;
    final schedulerConfig = json['schedulerConfig'] as Map<String, dynamic>?;

    // Create and return the widget
    return SchedulerWidget(
      initialViewMode: initialViewMode,
      allowViewModeChange: allowViewModeChange,
      availableViewModes: availableViewModes,
      initialDate: initialDate,
      showNavigationArrows: showNavigationArrows,
      showViewModeSelector: showViewModeSelector,
      showDateTitle: showDateTitle,
      showToolbar: showToolbar,
      dayStartTime: dayStartTime,
      dayEndTime: dayEndTime,
      timeSlotMinutes: timeSlotMinutes,
      showWeekends: showWeekends,
      workDays: workDays,
      highlightWorkingHours: highlightWorkingHours,
      workingHoursStart: workingHoursStart,
      workingHoursEnd: workingHoursEnd,
      events: events,
      eventDisplayMode: eventDisplayMode,
      allowEventCreation: allowEventCreation,
      allowEventEditing: allowEventEditing,
      allowEventDeletion: allowEventDeletion,
      allowEventDragging: allowEventDragging,
      allowEventResizing: allowEventResizing,
      backgroundColor: backgroundColor,
      timelineColor: timelineColor,
      gridLineColor: gridLineColor,
      todayHighlightColor: todayHighlightColor,
      selectedDateColor: selectedDateColor,
      timelineWidth: timelineWidth,
      eventBorderRadius: eventBorderRadius,
      showEventTime: showEventTime,
      showEventDuration: showEventDuration,
      showAllDayEvents: showAllDayEvents,
      showCurrentTimeIndicator: showCurrentTimeIndicator,
      currentTimeIndicatorColor: currentTimeIndicatorColor,
      fontSize: fontSize,
      fontWeight: fontWeight,
      locale: locale,
      isDarkTheme: isDarkTheme,
      hasBorder: hasBorder,
      borderRadius: borderRadius,
      borderColor: borderColor,
      borderWidth: borderWidth,
      hasShadow: hasShadow,
      elevation: elevation,
      headerHeight: headerHeight,
      timeSlotHeight: timeSlotHeight,
      headerDateFormat: headerDateFormat,
      timeFormat: timeFormat,
      jsonCallbacks: jsonCallbacks,
      useJsonCallbacks: useJsonCallbacks,
      callbackState: callbackState,
      jsonConfig: jsonConfig,
      useJsonStyling: useJsonStyling,
      useJsonFormatting: useJsonFormatting,
      useJsonSchedulerConfig: useJsonSchedulerConfig,
      schedulerConfig: schedulerConfig,
    );
  }

  /// Converts the SchedulerWidget to a JSON map
  ///
  /// This method allows for serializing the widget configuration to JSON,
  /// which can be useful for saving configurations or sharing them.
  Map<String, dynamic> toJson() {
    // Create a map with all the widget's properties
    return {
      // View configuration
      'initialViewMode': SchedulerWidgetJsonHelpers._viewModeToString(initialViewMode),
      'allowViewModeChange': allowViewModeChange,
      'availableViewModes': availableViewModes
          .map((mode) => SchedulerWidgetJsonHelpers._viewModeToString(mode))
          .toList(),
      'initialDate': initialDate?.toIso8601String(),
      'showNavigationArrows': showNavigationArrows,
      'showViewModeSelector': showViewModeSelector,
      'showDateTitle': showDateTitle,
      'showToolbar': showToolbar,

      // Time configuration
      'dayStartTime': SchedulerWidgetJsonHelpers._timeOfDayToString(dayStartTime),
      'dayEndTime': SchedulerWidgetJsonHelpers._timeOfDayToString(dayEndTime),
      'timeSlotMinutes': timeSlotMinutes,
      'showWeekends': showWeekends,
      'workDays': workDays,
      'highlightWorkingHours': highlightWorkingHours,
      'workingHoursStart': SchedulerWidgetJsonHelpers._timeOfDayToString(workingHoursStart),
      'workingHoursEnd': SchedulerWidgetJsonHelpers._timeOfDayToString(workingHoursEnd),

      // Event configuration
      'events': events.map((event) => event.toJson()).toList(),
      'eventDisplayMode': SchedulerWidgetJsonHelpers._eventDisplayModeToString(eventDisplayMode),
      'allowEventCreation': allowEventCreation,
      'allowEventEditing': allowEventEditing,
      'allowEventDeletion': allowEventDeletion,
      'allowEventDragging': allowEventDragging,
      'allowEventResizing': allowEventResizing,

      // Appearance
      'backgroundColor': SchedulerWidgetJsonHelpers._colorToString(backgroundColor),
      'timelineColor': SchedulerWidgetJsonHelpers._colorToString(timelineColor),
      'gridLineColor': SchedulerWidgetJsonHelpers._colorToString(gridLineColor),
      'todayHighlightColor': SchedulerWidgetJsonHelpers._colorToString(todayHighlightColor),
      'selectedDateColor': SchedulerWidgetJsonHelpers._colorToString(selectedDateColor),
      'timelineWidth': timelineWidth,
      'eventBorderRadius': eventBorderRadius,
      'showEventTime': showEventTime,
      'showEventDuration': showEventDuration,
      'showAllDayEvents': showAllDayEvents,
      'showCurrentTimeIndicator': showCurrentTimeIndicator,
      'currentTimeIndicatorColor': SchedulerWidgetJsonHelpers._colorToString(currentTimeIndicatorColor),
      'fontSize': fontSize,
      'fontWeight': SchedulerWidgetJsonHelpers._fontWeightToString(fontWeight),
      'locale': locale,
      'isDarkTheme': isDarkTheme,
      'hasBorder': hasBorder,
      'borderRadius': borderRadius,
      'borderColor': SchedulerWidgetJsonHelpers._colorToString(borderColor),
      'borderWidth': borderWidth,
      'hasShadow': hasShadow,
      'elevation': elevation,
      'headerHeight': headerHeight,
      'timeSlotHeight': timeSlotHeight,
      'headerDateFormat': headerDateFormat,
      'timeFormat': timeFormat,

      // JSON configuration properties
      'useJsonCallbacks': useJsonCallbacks,
      'useJsonStyling': useJsonStyling,
      'useJsonFormatting': useJsonFormatting,
      'useJsonSchedulerConfig': useJsonSchedulerConfig,
    };
  }

  @override
  State<SchedulerWidget> createState() => _SchedulerWidgetState();
}

/// Helper methods for JSON parsing and serialization
extension SchedulerWidgetJsonHelpers on SchedulerWidget {
  /// Parses a TimeOfDay from a string or map
  static TimeOfDay _parseTimeOfDay(dynamic timeValue) {
    if (timeValue is String) {
      // Parse from string format "HH:mm"
      final parts = timeValue.split(':');
      if (parts.length == 2) {
        final hour = int.tryParse(parts[0]) ?? 0;
        final minute = int.tryParse(parts[1]) ?? 0;
        return TimeOfDay(hour: hour, minute: minute);
      }
    } else if (timeValue is Map) {
      // Parse from map with hour and minute keys
      final hour = timeValue['hour'] as int? ?? 0;
      final minute = timeValue['minute'] as int? ?? 0;
      return TimeOfDay(hour: hour, minute: minute);
    }
    return const TimeOfDay(hour: 0, minute: 0); // Default
  }

  /// Converts a TimeOfDay to a string representation
  static String _timeOfDayToString(TimeOfDay time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  /// Parses a DateTime from a string or map
  static DateTime _parseDateTime(dynamic dateValue) {
    if (dateValue is String) {
      return DateTime.parse(dateValue);
    } else if (dateValue is Map) {
      return DateTime(
        dateValue['year'] as int? ?? DateTime.now().year,
        dateValue['month'] as int? ?? DateTime.now().month,
        dateValue['day'] as int? ?? DateTime.now().day,
        dateValue['hour'] as int? ?? 0,
        dateValue['minute'] as int? ?? 0,
        dateValue['second'] as int? ?? 0,
      );
    }
    return DateTime.now(); // Default
  }

  /// Parses a Color from a string or map
  static Color _parseColor(dynamic colorValue) {
    if (colorValue is String) {
      if (colorValue.startsWith('#')) {
        // Parse hex color
        String hex = colorValue.replaceFirst('#', '');
        if (hex.length == 3) {
          // Convert 3-digit hex to 6-digit
          hex = hex.split('').map((char) => char + char).join('');
        }
        if (hex.length == 6) {
          hex = 'FF$hex'; // Add alpha channel if not present
        }
        return Color(int.parse(hex, radix: 16));
      } else {
        // Parse named color
        switch (colorValue.toLowerCase()) {
          case 'red': return Colors.red;
          case 'blue': return Colors.blue;
          case 'green': return Colors.green;
          case 'yellow': return Colors.yellow;
          case 'orange': return Colors.orange;
          case 'purple': return Colors.purple;
          case 'pink': return Colors.pink;
          case 'brown': return Colors.brown;
          case 'grey':
          case 'gray': return Colors.grey;
          case 'black': return Colors.black;
          case 'white': return Colors.white;
          case 'transparent': return Colors.transparent;
          default: return Colors.blue;
        }
      }
    } else if (colorValue is Map) {
      // Parse RGBA color
      final int r = colorValue['r'] as int? ?? 0;
      final int g = colorValue['g'] as int? ?? 0;
      final int b = colorValue['b'] as int? ?? 0;
      final double a = colorValue['a'] != null ? (colorValue['a'] as num).toDouble() : 1.0;
      return Color.fromRGBO(r, g, b, a);
    }
    return Colors.blue; // Default color
  }

  /// Converts a Color to a string representation
  static String _colorToString(Color color) {
    final r = color.toString().split('(0x')[1].substring(2, 4);
    final g = color.toString().split('(0x')[1].substring(4, 6);
    final b = color.toString().split('(0x')[1].substring(6, 8);
    return '#$r$g$b';
  }

  /// Parses a SchedulerViewMode from a string
  static SchedulerViewMode _parseViewMode(dynamic viewModeValue) {
    if (viewModeValue is String) {
      switch (viewModeValue.toLowerCase()) {
        case 'day': return SchedulerViewMode.day;
        case 'week': return SchedulerViewMode.week;
        case 'month': return SchedulerViewMode.month;
        case 'agenda': return SchedulerViewMode.agenda;
        default: return SchedulerViewMode.week;
      }
    }
    return SchedulerViewMode.week; // Default
  }

  /// Converts a SchedulerViewMode to a string representation
  static String _viewModeToString(SchedulerViewMode viewMode) {
    return viewMode.toString().split('.').last;
  }

  /// Parses an EventDisplayMode from a string
  static EventDisplayMode _parseEventDisplayMode(dynamic displayModeValue) {
    if (displayModeValue is String) {
      switch (displayModeValue.toLowerCase()) {
        case 'block': return EventDisplayMode.block;
        case 'line': return EventDisplayMode.line;
        case 'minimal': return EventDisplayMode.minimal;
        case 'detailed': return EventDisplayMode.detailed;
        default: return EventDisplayMode.block;
      }
    }
    return EventDisplayMode.block; // Default
  }

  /// Converts an EventDisplayMode to a string representation
  static String _eventDisplayModeToString(EventDisplayMode displayMode) {
    return displayMode.toString().split('.').last;
  }

  /// Parses a FontWeight from a string or number
  static FontWeight _parseFontWeight(dynamic weightValue) {
    if (weightValue is String) {
      switch (weightValue.toLowerCase()) {
        case 'thin': return FontWeight.w100;
        case 'extralight': return FontWeight.w200;
        case 'light': return FontWeight.w300;
        case 'regular': return FontWeight.w400;
        case 'medium': return FontWeight.w500;
        case 'semibold': return FontWeight.w600;
        case 'bold': return FontWeight.w700;
        case 'extrabold': return FontWeight.w800;
        case 'black': return FontWeight.w900;
        default: return FontWeight.normal;
      }
    } else if (weightValue is int) {
      switch (weightValue) {
        case 100: return FontWeight.w100;
        case 200: return FontWeight.w200;
        case 300: return FontWeight.w300;
        case 400: return FontWeight.w400;
        case 500: return FontWeight.w500;
        case 600: return FontWeight.w600;
        case 700: return FontWeight.w700;
        case 800: return FontWeight.w800;
        case 900: return FontWeight.w900;
        default: return FontWeight.normal;
      }
    }
    return FontWeight.normal; // Default
  }

  /// Converts a FontWeight to a string representation
  static String _fontWeightToString(FontWeight weight) {
    if (weight == FontWeight.w100) return 'thin';
    if (weight == FontWeight.w200) return 'extralight';
    if (weight == FontWeight.w300) return 'light';
    if (weight == FontWeight.w400) return 'regular';
    if (weight == FontWeight.w500) return 'medium';
    if (weight == FontWeight.w600) return 'semibold';
    if (weight == FontWeight.w700) return 'bold';
    if (weight == FontWeight.w800) return 'extrabold';
    if (weight == FontWeight.w900) return 'black';
    return 'normal';
  }
}

class _SchedulerWidgetState extends State<SchedulerWidget> {
  late SchedulerViewMode _currentViewMode;
  late DateTime _currentDate;
  // These fields will be used in the full implementation
  // ignore: unused_field
  late DateTime _selectedDate; // For highlighting selected date
  // ignore: unused_field
  late List<SchedulerEvent> _events; // For displaying events
  // ignore: unused_field
  SchedulerEvent? _selectedEvent; // For tracking selected event
  Timer? _refreshTimer;

  // Interaction state
  bool _isFocused = false;
  late FocusNode _focusNode;

  // Callback state
  Map<String, dynamic> _callbackState = {};

  @override
  void initState() {
    super.initState();

    // Apply JSON configuration if available
    if (widget.useJsonSchedulerConfig && widget.schedulerConfig != null) {
      final jsonWidget = SchedulerWidget.fromJson(widget.schedulerConfig!);
      _currentViewMode = jsonWidget.initialViewMode;
      _currentDate = jsonWidget.initialDate ?? DateTime.now();
      _selectedDate = jsonWidget.initialDate ?? DateTime.now();
      _events = List.from(jsonWidget.events);

      // Set up timer to refresh current time indicator
      if (jsonWidget.showCurrentTimeIndicator) {
        _refreshTimer = Timer.periodic(
          const Duration(minutes: 1),
          (timer) {
            if (mounted) {
              setState(() {
                // Just trigger a rebuild to update the current time indicator
              });
            }
          },
        );
      }
    } else {
      _currentViewMode = widget.initialViewMode;
      _currentDate = widget.initialDate ?? DateTime.now();
      _selectedDate = widget.initialDate ?? DateTime.now();
      _events = List.from(widget.events);

      // Set up timer to refresh current time indicator
      if (widget.showCurrentTimeIndicator) {
        _refreshTimer = Timer.periodic(
          const Duration(minutes: 1),
          (timer) {
            if (mounted) {
              setState(() {
                // Just trigger a rebuild to update the current time indicator
              });
            }
          },
        );
      }
    }

    // Initialize focus node
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);

    // Initialize callback state
    if (widget.callbackState != null) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
      if (_isFocused && widget.onFocus != null) {
        widget.onFocus!(_isFocused);
      }
    });
  }

  @override
  void didUpdateWidget(SchedulerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Handle JSON configuration changes
    if (widget.useJsonSchedulerConfig && widget.schedulerConfig != null) {
      if (!oldWidget.useJsonSchedulerConfig ||
          oldWidget.schedulerConfig != widget.schedulerConfig) {
        // JSON configuration has changed, update state
        final jsonWidget = SchedulerWidget.fromJson(widget.schedulerConfig!);

        // Only update events if they've changed
        if (jsonWidget.events != _events) {
          _events = List.from(jsonWidget.events);
        }
      }
    } else if (oldWidget.events != widget.events) {
      _events = List.from(widget.events);
    }

    // Update focus node if changed
    if (widget.focusNode != oldWidget.focusNode) {
      _focusNode.removeListener(_onFocusChange);
      _focusNode = widget.focusNode ?? _focusNode;
      _focusNode.addListener(_onFocusChange);
    }

    // Update callback state if provided
    if (widget.callbackState != null && widget.callbackState != oldWidget.callbackState) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    _focusNode.removeListener(_onFocusChange);
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  /// Executes a callback defined in JSON
  void _executeJsonCallback(String callbackName, [dynamic value]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    final callback = widget.jsonCallbacks![callbackName];
    if (callback == null) return;

    CallbackInterpreter.executeCallback(
      callback,
      context,
      value: value,
      state: _callbackState,
      customHandlers: widget.customCallbackHandlers,
    );
  }



  void _changeViewMode(SchedulerViewMode mode) {
    setState(() {
      _currentViewMode = mode;
    });
    // Call standard callback
    if (widget.onViewModeChanged != null) {
      widget.onViewModeChanged!(mode);
    }

    // Execute JSON callback if defined
    if (widget.useJsonCallbacks && widget.jsonCallbacks != null &&
        widget.jsonCallbacks!.containsKey('onViewModeChanged')) {
      _executeJsonCallback('onViewModeChanged', mode.toString().split('.').last);
    }
  }

  void _navigateToPrevious() {
    setState(() {
      switch (_currentViewMode) {
        case SchedulerViewMode.day:
          _currentDate = _currentDate.subtract(const Duration(days: 1));
          break;
        case SchedulerViewMode.week:
          _currentDate = _currentDate.subtract(const Duration(days: 7));
          break;
        case SchedulerViewMode.month:
          // Handle month boundary correctly
          if (_currentDate.month == 1) {
            _currentDate = DateTime(
              _currentDate.year - 1,
              12,
              min(_currentDate.day, 31), // Use the last valid day of December
            );
          } else {
            // Get the number of days in the previous month
            final daysInPrevMonth = DateTime(_currentDate.year, _currentDate.month, 0).day;
            _currentDate = DateTime(
              _currentDate.year,
              _currentDate.month - 1,
              min(_currentDate.day, daysInPrevMonth), // Ensure day is valid for the month
            );
          }
          break;
        case SchedulerViewMode.agenda:
          _currentDate = _currentDate.subtract(const Duration(days: 14));
          break;
      }
    });
    // Call standard callback
    if (widget.onDateChanged != null) {
      widget.onDateChanged!(_currentDate);
    }

    // Execute JSON callback if defined
    if (widget.useJsonCallbacks && widget.jsonCallbacks != null &&
        widget.jsonCallbacks!.containsKey('onDateChanged')) {
      _executeJsonCallback('onDateChanged', _currentDate.toIso8601String());
    }
  }

  void _navigateToNext() {
    setState(() {
      switch (_currentViewMode) {
        case SchedulerViewMode.day:
          _currentDate = _currentDate.add(const Duration(days: 1));
          break;
        case SchedulerViewMode.week:
          _currentDate = _currentDate.add(const Duration(days: 7));
          break;
        case SchedulerViewMode.month:
          // Handle month boundary correctly
          if (_currentDate.month == 12) {
            _currentDate = DateTime(
              _currentDate.year + 1,
              1,
              min(_currentDate.day, 31), // Use the last valid day of January
            );
          } else {
            // Get the number of days in the next month
            final daysInNextMonth = DateTime(_currentDate.year, _currentDate.month + 2, 0).day;
            _currentDate = DateTime(
              _currentDate.year,
              _currentDate.month + 1,
              min(_currentDate.day, daysInNextMonth), // Ensure day is valid for the month
            );
          }
          break;
        case SchedulerViewMode.agenda:
          _currentDate = _currentDate.add(const Duration(days: 14));
          break;
      }
    });
    if (widget.onDateChanged != null) {
      widget.onDateChanged!(_currentDate);
    }
  }

  void _navigateToToday() {
    setState(() {
      _currentDate = DateTime.now();
    });
    if (widget.onDateChanged != null) {
      widget.onDateChanged!(_currentDate);
    }
  }

  // These methods will be used in the full implementation of the scheduler

  // Method to handle date selection in calendar views
  // ignore: unused_element
  void _selectDate(DateTime date) {
    setState(() {
      _selectedDate = date;
    });
    if (widget.onDateChanged != null) {
      widget.onDateChanged!(date);
    }
  }

  // Method to handle tapping on a time slot in day/week views
  // ignore: unused_element
  void _handleTimeSlotTap(DateTime date, TimeOfDay time) {
    if (widget.onTimeSlotTap != null) {
      widget.onTimeSlotTap!(date, time);
    }

    if (widget.allowEventCreation) {
      _showEventDialog(
        SchedulerEvent(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          title: '',
          start: DateTime(
            date.year,
            date.month,
            date.day,
            time.hour,
            time.minute,
          ),
          end: DateTime(
            date.year,
            date.month,
            date.day,
            time.hour + 1,
            time.minute,
          ),
        ),
        isNew: true,
      );
    }
  }

  // Method to handle tapping on an event in any view
  // ignore: unused_element
  void _handleEventTap(SchedulerEvent event) {
    setState(() {
      _selectedEvent = event;
    });

    if (widget.onEventTap != null) {
      widget.onEventTap!(event);
    }

    if (widget.allowEventEditing) {
      _showEventDialog(event, isNew: false);
    }
  }

  void _showEventDialog(SchedulerEvent event, {required bool isNew}) {
    // This would be implemented to show a dialog for creating/editing events
    // For simplicity, we're just showing a placeholder implementation
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isNew ? 'Create Event' : 'Edit Event'),
        content: const Text('Event dialog would be implemented here'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              if (isNew) {
                if (widget.onEventCreated != null) {
                  widget.onEventCreated!(event);
                }
              } else {
                if (widget.onEventUpdated != null) {
                  widget.onEventUpdated!(event);
                }
              }
            },
            child: Text(isNew ? 'Create' : 'Update'),
          ),
        ],
      ),
    );
  }

  String _getHeaderTitle([SchedulerWidget? config]) {
    final cfg = config ?? widget;
    final format = cfg.headerDateFormat ?? _getDefaultHeaderFormat(cfg);
    return DateFormat(format, cfg.locale).format(_currentDate);
  }

  String _getDefaultHeaderFormat([SchedulerWidget? config]) {
    final cfg = config ?? widget;
    // Handle each view mode with a specific format
    return switch (_currentViewMode) {
      SchedulerViewMode.day => 'EEEE, MMMM d, y',
      SchedulerViewMode.week => _getWeekHeaderFormat(),
      SchedulerViewMode.month => 'MMMM y',
      SchedulerViewMode.agenda => 'MMMM d, y - ${DateFormat('MMMM d, y', cfg.locale).format(_currentDate.add(const Duration(days: 13)))}'
    };
  }

  String _getWeekHeaderFormat() {
    final weekStart = _getWeekStartDate(_currentDate);
    final weekEnd = weekStart.add(const Duration(days: 6));

    if (weekStart.month == weekEnd.month) {
      return 'MMMM d-${weekEnd.day}, y';
    } else if (weekStart.year == weekEnd.year) {
      return 'MMM d - MMM d, y';
    } else {
      return 'MMM d, y - MMM d, y';
    }
  }

  DateTime _getWeekStartDate(DateTime date) {
    // Get the start of the week (assuming Monday is the first day)
    int weekday = date.weekday;
    return date.subtract(Duration(days: weekday - 1));
  }

  Widget _buildHeader([SchedulerWidget? config]) {
    final cfg = config ?? widget;

    return Container(
      height: cfg.headerHeight,
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      decoration: BoxDecoration(
        color: cfg.isDarkTheme ? Colors.grey.shade800 : cfg.backgroundColor,
        border: cfg.hasBorder
            ? Border(
                bottom: BorderSide(
                  color: cfg.borderColor,
                  width: cfg.borderWidth,
                ),
              )
            : null,
        boxShadow: cfg.hasShadow
            ? [
                BoxShadow(
                  color: Colors.black.withAlpha(26), // 0.1 opacity = 26 alpha
                  blurRadius: cfg.elevation,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              if (cfg.showNavigationArrows)
                IconButton(
                  icon: const Icon(Icons.chevron_left),
                  onPressed: _navigateToPrevious,
                  tooltip: 'Previous',
                ),
              if (cfg.showDateTitle)
                GestureDetector(
                  onTap: () => _navigateToToday(),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: Text(
                      _getHeaderTitle(cfg),
                      style: TextStyle(
                        fontSize: cfg.fontSize + 4,
                        fontWeight: FontWeight.bold,
                        color: cfg.isDarkTheme ? Colors.white : Colors.black,
                      ),
                    ),
                  ),
                ),
              if (cfg.showNavigationArrows)
                IconButton(
                  icon: const Icon(Icons.chevron_right),
                  onPressed: _navigateToNext,
                  tooltip: 'Next',
                ),
              TextButton(
                onPressed: _navigateToToday,
                child: const Text('Today'),
              ),
            ],
          ),
          if (cfg.showViewModeSelector && cfg.allowViewModeChange)
            DropdownButton<SchedulerViewMode>(
              value: _currentViewMode,
              onChanged: (value) {
                if (value != null) {
                  _changeViewMode(value);
                }
              },
              items: cfg.availableViewModes
                  .map((mode) => DropdownMenuItem<SchedulerViewMode>(
                        value: mode,
                        child: Text(_getViewModeLabel(mode)),
                      ))
                  .toList(),
            ),
        ],
      ),
    );
  }

  String _getViewModeLabel(SchedulerViewMode mode) {
    // Use switch expression to ensure all cases are handled
    return switch (mode) {
      SchedulerViewMode.day => 'Day',
      SchedulerViewMode.week => 'Week',
      SchedulerViewMode.month => 'Month',
      SchedulerViewMode.agenda => 'Agenda'
    };
  }

  Widget _buildSchedulerContent([SchedulerWidget? config]) {
    final cfg = config ?? widget;

    // Use switch expression to ensure all cases are handled
    return switch (_currentViewMode) {
      SchedulerViewMode.day => _buildDayView(cfg),
      SchedulerViewMode.week => _buildWeekView(cfg),
      SchedulerViewMode.month => _buildMonthView(cfg),
      SchedulerViewMode.agenda => _buildAgendaView(cfg)
    };
  }

  Widget _buildDayView(SchedulerWidget cfg) {
    // Placeholder implementation for day view
    return Center(
      child: Text(
        'Day View for ${DateFormat('yyyy-MM-dd', cfg.locale).format(_currentDate)}',
        style: TextStyle(fontSize: cfg.fontSize),
      ),
    );
  }

  Widget _buildWeekView(SchedulerWidget cfg) {
    // Placeholder implementation for week view
    return Center(
      child: Text(
        'Week View starting ${DateFormat('yyyy-MM-dd', cfg.locale).format(_getWeekStartDate(_currentDate))}',
        style: TextStyle(fontSize: cfg.fontSize),
      ),
    );
  }

  Widget _buildMonthView(SchedulerWidget cfg) {
    // Placeholder implementation for month view
    return Center(
      child: Text(
        'Month View for ${DateFormat('yyyy-MM', cfg.locale).format(_currentDate)}',
        style: TextStyle(fontSize: cfg.fontSize),
      ),
    );
  }

  Widget _buildAgendaView(SchedulerWidget cfg) {
    // Placeholder implementation for agenda view
    return Center(
      child: Text(
        'Agenda View',
        style: TextStyle(fontSize: cfg.fontSize),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Apply JSON configuration if available
    if (widget.useJsonSchedulerConfig && widget.schedulerConfig != null) {
      // Create a new widget with the JSON configuration applied
      final jsonWidget = SchedulerWidget.fromJson(widget.schedulerConfig!);

      // Use the JSON widget's properties for rendering, but keep the current state
      return _buildWidgetWithConfig(jsonWidget);
    }

    return _buildWidgetWithConfig(widget);
  }

  /// Builds the widget with the given configuration
  Widget _buildWidgetWithConfig(SchedulerWidget config) {
    return Container(
      decoration: BoxDecoration(
        color: config.backgroundColor,
        borderRadius: config.hasBorder ? BorderRadius.circular(config.borderRadius) : null,
        border: config.hasBorder
            ? Border.all(
                color: config.borderColor,
                width: config.borderWidth,
              )
            : null,
        boxShadow: config.hasShadow
            ? [
                BoxShadow(
                  color: Colors.black.withAlpha(26), // 0.1 opacity = 26 alpha
                  blurRadius: config.elevation,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: Column(
        children: [
          _buildHeader(config),
          Expanded(
            child: _buildSchedulerContent(config),
          ),
        ],
      ),
    );
  }
}
