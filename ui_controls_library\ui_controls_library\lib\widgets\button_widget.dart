import 'package:flutter/material.dart';

/// A customizable button widget that can be used for calls to action (CTA) and various button types.
///
/// This widget provides a variety of customization options including styles, colors, shapes, icons,
/// and interaction capabilities.
class ButtonWidget extends StatefulWidget {
  /// The text to display on the button.
  final String text;

  /// The background color of the button.
  final Color backgroundColor;

  /// The text color of the button.
  final Color textColor;

  /// The border color of the button. If null, no border is drawn.
  final Color? borderColor;

  /// The width of the border. Only applicable if borderColor is not null.
  final double borderWidth;

  /// The border radius of the button.
  final double borderRadius;

  /// The icon to display before the text. If null, no icon is shown.
  final IconData? icon;

  /// The color of the icon. If null, uses textColor.
  final Color? iconColor;

  /// The size of the icon.
  final double iconSize;

  /// The spacing between the icon and the text.
  final double iconSpacing;

  /// The padding inside the button.
  final EdgeInsetsGeometry padding;

  /// The callback when the button is tapped.
  final VoidCallback? onPressed;

  /// The elevation of the button (shadow).
  final double elevation;

  /// The font size of the text.
  final double fontSize;

  /// The font weight of the text.
  final FontWeight fontWeight;

  /// Whether the button is outlined (transparent background with border).
  final bool isOutlined;

  /// Whether the button is filled (solid background).
  final bool isFilled;

  /// Whether the button has a gradient background.
  final bool hasGradient;

  /// The gradient colors if hasGradient is true.
  final List<Color>? gradientColors;

  /// The gradient begin alignment.
  final Alignment gradientBegin;

  /// The gradient end alignment.
  final Alignment gradientEnd;

  /// Whether the button is disabled.
  final bool isDisabled;

  /// The width of the button. If null, the button will size itself to fit its content.
  final double? width;

  /// The height of the button. If null, the button will size itself to fit its content.
  final double? height;

  /// Whether the button should expand to fill its parent's width.
  final bool isFullWidth;

  /// Whether the button should have a loading indicator.
  final bool isLoading;

  /// The color of the loading indicator.
  final Color? loadingColor;

  /// The size of the loading indicator.
  final double loadingSize;

  /// Whether the button should have a ripple effect when tapped.
  final bool hasRippleEffect;

  /// The color of the ripple effect.
  final Color? rippleColor;

  // Additional Text Styling Properties

  /// The text decoration (underline, line-through, etc.)
  final TextDecoration textDecoration;

  /// How to handle text overflow
  final TextOverflow textOverflow;

  /// Maximum number of lines for the text
  final int? maxLines;

  /// Alignment of the text within the button
  final TextAlign textAlign;

  /// Spacing between letters in the text
  final double letterSpacing;

  /// Spacing between words in the text
  final double wordSpacing;

  /// Direction of the text (LTR, RTL)
  final TextDirection? textDirection;

  // Additional Visual Enhancement Properties

  /// Color of the shadow
  final Color shadowColor;

  /// Offset of the shadow
  final Offset shadowOffset;

  /// Opacity of the entire button (0.0 to 1.0)
  final double opacity;

  /// Brightness adjustment for light/dark themes
  final Brightness? brightness;

  /// Blur radius for the shadow
  final double blurRadius;

  /// Spread radius for the shadow
  final double spreadRadius;

  // Additional Layout Properties

  /// Margin around the button
  final EdgeInsetsGeometry margin;

  /// Alignment within the parent
  final Alignment? alignment;

  /// Transform for rotation, scaling, etc.
  final Matrix4? transform;

  // Additional Animation Properties

  /// Duration for animations
  final Duration animationDuration;

  /// Curve for animations
  final Curve animationCurve;

  /// Type of animation (fade, scale, slide)
  final String animationType;

  // Additional Interaction Properties

  /// Whether long press is enabled
  final bool longPressEnabled;

  /// Callback for long press
  final VoidCallback? onLongPress;

  /// Whether double tap is enabled
  final bool doubleTapEnabled;

  /// Callback for double tap
  final VoidCallback? onDoubleTap;

  /// Color when hovered
  final Color? hoverColor;

  /// Color of the splash effect when tapped
  final Color? splashColor;

  /// Color of the highlight effect when tapped
  final Color? highlightColor;

  /// Color when focused
  final Color? focusColor;

  // Icon Position Properties

  /// Position of the icon (left, right, top, bottom)
  final String iconPosition;

  /// Alignment of the icon
  final Alignment iconAlignment;

  // Additional Loading Properties

  /// Type of loading indicator (circular, linear, custom)
  final String loadingType;

  /// Text to show during loading state
  final String? loadingText;

  // Accessibility Properties

  /// Label for screen readers
  final String? semanticsLabel;

  /// Tooltip for hover
  final String? tooltip;

  /// Whether to enable haptic/audio feedback
  final bool enableFeedback;

  // Advanced Interaction Properties

  /// Callback for mouse hover
  ///
  /// This function is called when the mouse pointer enters or exits the button.
  /// The parameter is true when the mouse enters and false when it exits.
  final void Function(bool)? onHover;

  /// Callback for keyboard focus
  ///
  /// This function is called when the button gains or loses keyboard focus.
  /// The parameter is true when focus is gained and false when it is lost.
  final void Function(bool)? onFocus;

  /// Focus node for manual focus control
  ///
  /// This allows for programmatic control of the button's focus state.
  /// If null, the button will create and manage its own focus node.
  final FocusNode? focusNode;

  /// Whether the button should be focused automatically when it appears
  ///
  /// If true, the button will request focus when it is first built.
  final bool autofocus;

  /// Creates a button widget.
  const ButtonWidget({
    super.key,
    required this.text,
    this.backgroundColor = Colors.blue,
    this.textColor = Colors.white,
    this.borderColor,
    this.borderWidth = 1.0,
    this.borderRadius = 8.0,
    this.icon,
    this.iconColor,
    this.iconSize = 18.0,
    this.iconSpacing = 8.0,
    this.padding = const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
    this.onPressed,
    this.elevation = 0.0,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.isOutlined = false,
    this.isFilled = true,
    this.hasGradient = false,
    this.gradientColors,
    this.gradientBegin = Alignment.centerLeft,
    this.gradientEnd = Alignment.centerRight,
    this.isDisabled = false,
    this.width,
    this.height,
    this.isFullWidth = false,
    this.isLoading = false,
    this.loadingColor,
    this.loadingSize = 20.0,
    this.hasRippleEffect = true,
    this.rippleColor,
    // Additional Text Styling Properties
    this.textDecoration = TextDecoration.none,
    this.textOverflow = TextOverflow.clip,
    this.maxLines,
    this.textAlign = TextAlign.center,
    this.letterSpacing = 0.0,
    this.wordSpacing = 0.0,
    this.textDirection,
    // Additional Visual Enhancement Properties
    this.shadowColor = Colors.black,
    this.shadowOffset = const Offset(0, 2),
    this.opacity = 1.0,
    this.brightness,
    this.blurRadius = 3.0,
    this.spreadRadius = 0.0,
    // Additional Layout Properties
    this.margin = EdgeInsets.zero,
    this.alignment,
    this.transform,
    // Additional Animation Properties
    this.animationDuration = const Duration(milliseconds: 300),
    this.animationCurve = Curves.easeInOut,
    this.animationType = 'fade',
    // Additional Interaction Properties
    this.longPressEnabled = false,
    this.onLongPress,
    this.doubleTapEnabled = false,
    this.onDoubleTap,
    this.hoverColor,
    this.splashColor,
    this.highlightColor,
    this.focusColor,
    // Icon Position Properties
    this.iconPosition = 'left',
    this.iconAlignment = Alignment.center,
    // Additional Loading Properties
    this.loadingType = 'circular',
    this.loadingText,
    // Accessibility Properties
    this.semanticsLabel,
    this.tooltip,
    this.enableFeedback = true,
    // Advanced Interaction Properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
  });

  /// Creates a ButtonWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the ButtonWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "text": "Submit",
  ///   "backgroundColor": "blue",
  ///   "textColor": "white",
  ///   "icon": "send",
  ///   "isOutlined": true
  /// }
  /// ```
  factory ButtonWidget.fromJson(Map<String, dynamic> json) {
    // Handle padding
    EdgeInsetsGeometry padding = const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0);
    if (json['padding'] != null) {
      if (json['padding'] is double) {
        double paddingValue = json['padding'] as double;
        padding = EdgeInsets.all(paddingValue);
      } else if (json['padding'] is Map) {
        Map<String, dynamic> paddingMap = json['padding'] as Map<String, dynamic>;
        padding = EdgeInsets.fromLTRB(
          paddingMap['left'] as double? ?? 16.0,
          paddingMap['top'] as double? ?? 12.0,
          paddingMap['right'] as double? ?? 16.0,
          paddingMap['bottom'] as double? ?? 12.0,
        );
      }
    }

    // Handle font weight
    FontWeight fontWeight = FontWeight.normal;
    if (json['fontWeight'] != null) {
      if (json['fontWeight'] == 'bold' || json['fontWeight'] == true) {
        fontWeight = FontWeight.bold;
      } else if (json['fontWeight'] == 'light') {
        fontWeight = FontWeight.w300;
      } else if (json['fontWeight'] is int) {
        final weight = json['fontWeight'] as int;
        switch (weight) {
          case 100: fontWeight = FontWeight.w100; break;
          case 200: fontWeight = FontWeight.w200; break;
          case 300: fontWeight = FontWeight.w300; break;
          case 400: fontWeight = FontWeight.w400; break;
          case 500: fontWeight = FontWeight.w500; break;
          case 600: fontWeight = FontWeight.w600; break;
          case 700: fontWeight = FontWeight.w700; break;
          case 800: fontWeight = FontWeight.w800; break;
          case 900: fontWeight = FontWeight.w900; break;
        }
      }
    }

    // Handle icon
    IconData? icon;
    if (json['icon'] != null) {
      icon = _getIconData(json['icon'].toString());
    }

    // Handle gradient colors
    List<Color>? gradientColors;
    if (json['gradientColors'] != null && json['gradientColors'] is List) {
      gradientColors = (json['gradientColors'] as List)
          .map((colorValue) => _colorFromJson(colorValue) ?? Colors.blue)
          .toList();
    }

    // Handle gradient begin/end
    Alignment gradientBegin = Alignment.centerLeft;
    if (json['gradientBegin'] != null) {
      gradientBegin = _alignmentFromString(json['gradientBegin'].toString());
    }

    Alignment gradientEnd = Alignment.centerRight;
    if (json['gradientEnd'] != null) {
      gradientEnd = _alignmentFromString(json['gradientEnd'].toString());
    }

    // Handle text decoration
    TextDecoration textDecoration = TextDecoration.none;
    if (json['textDecoration'] != null) {
      switch (json['textDecoration'].toString().toLowerCase()) {
        case 'underline':
          textDecoration = TextDecoration.underline;
          break;
        case 'overline':
          textDecoration = TextDecoration.overline;
          break;
        case 'line-through':
        case 'linethrough':
        case 'strikethrough':
          textDecoration = TextDecoration.lineThrough;
          break;
        case 'none':
        default:
          textDecoration = TextDecoration.none;
      }
    }

    // Handle text overflow
    TextOverflow textOverflow = TextOverflow.clip;
    if (json['textOverflow'] != null) {
      switch (json['textOverflow'].toString().toLowerCase()) {
        case 'ellipsis':
          textOverflow = TextOverflow.ellipsis;
          break;
        case 'fade':
          textOverflow = TextOverflow.fade;
          break;
        case 'visible':
          textOverflow = TextOverflow.visible;
          break;
        case 'clip':
        default:
          textOverflow = TextOverflow.clip;
      }
    }

    // Handle text align
    TextAlign textAlign = TextAlign.center;
    if (json['textAlign'] != null) {
      switch (json['textAlign'].toString().toLowerCase()) {
        case 'left':
          textAlign = TextAlign.left;
          break;
        case 'right':
          textAlign = TextAlign.right;
          break;
        case 'start':
          textAlign = TextAlign.start;
          break;
        case 'end':
          textAlign = TextAlign.end;
          break;
        case 'justify':
          textAlign = TextAlign.justify;
          break;
        case 'center':
        default:
          textAlign = TextAlign.center;
      }
    }

    // Handle text direction
    TextDirection? textDirection;
    if (json['textDirection'] != null) {
      switch (json['textDirection'].toString().toLowerCase()) {
        case 'ltr':
          textDirection = TextDirection.ltr;
          break;
        case 'rtl':
          textDirection = TextDirection.rtl;
          break;
      }
    }

    // Handle margin
    EdgeInsetsGeometry margin = EdgeInsets.zero;
    if (json['margin'] != null) {
      if (json['margin'] is double) {
        double marginValue = json['margin'] as double;
        margin = EdgeInsets.all(marginValue);
      } else if (json['margin'] is Map) {
        Map<String, dynamic> marginMap = json['margin'] as Map<String, dynamic>;
        margin = EdgeInsets.fromLTRB(
          marginMap['left'] as double? ?? 0.0,
          marginMap['top'] as double? ?? 0.0,
          marginMap['right'] as double? ?? 0.0,
          marginMap['bottom'] as double? ?? 0.0,
        );
      }
    }

    // Handle alignment
    Alignment? alignment;
    if (json['alignment'] != null) {
      alignment = _alignmentFromString(json['alignment'].toString());
    }

    // Handle icon alignment
    Alignment iconAlignment = Alignment.center;
    if (json['iconAlignment'] != null) {
      iconAlignment = _alignmentFromString(json['iconAlignment'].toString());
    }

    // Handle transform
    Matrix4? transform;
    if (json['transform'] != null && json['transform'] is Map) {
      Map<String, dynamic> transformMap = json['transform'] as Map<String, dynamic>;

      // Create a base identity matrix
      transform = Matrix4.identity();

      // Apply rotation if specified (in degrees)
      if (transformMap['rotation'] != null) {
        double rotation = (transformMap['rotation'] as num).toDouble();
        transform.rotateZ(rotation * 3.1415926535 / 180); // Convert degrees to radians
      }

      // Apply scale if specified
      if (transformMap['scaleX'] != null || transformMap['scaleY'] != null) {
        double scaleX = (transformMap['scaleX'] as num?)?.toDouble() ?? 1.0;
        double scaleY = (transformMap['scaleY'] as num?)?.toDouble() ?? 1.0;
        transform.scale(scaleX, scaleY);
      }

      // Apply translation if specified
      if (transformMap['translateX'] != null || transformMap['translateY'] != null) {
        double translateX = (transformMap['translateX'] as num?)?.toDouble() ?? 0.0;
        double translateY = (transformMap['translateY'] as num?)?.toDouble() ?? 0.0;
        transform.translate(translateX, translateY);
      }
    }

    // Handle animation duration
    Duration animationDuration = const Duration(milliseconds: 300);
    if (json['animationDuration'] != null) {
      int milliseconds = json['animationDuration'] as int? ?? 300;
      animationDuration = Duration(milliseconds: milliseconds);
    }

    // Handle animation curve
    Curve animationCurve = Curves.easeInOut;
    if (json['animationCurve'] != null) {
      switch (json['animationCurve'].toString().toLowerCase()) {
        case 'linear':
          animationCurve = Curves.linear;
          break;
        case 'decelerate':
          animationCurve = Curves.decelerate;
          break;
        case 'ease':
          animationCurve = Curves.ease;
          break;
        case 'easein':
        case 'ease_in':
          animationCurve = Curves.easeIn;
          break;
        case 'easeout':
        case 'ease_out':
          animationCurve = Curves.easeOut;
          break;
        case 'easeinout':
        case 'ease_in_out':
          animationCurve = Curves.easeInOut;
          break;
        case 'elasticin':
        case 'elastic_in':
          animationCurve = Curves.elasticIn;
          break;
        case 'elasticout':
        case 'elastic_out':
          animationCurve = Curves.elasticOut;
          break;
        case 'elasticinout':
        case 'elastic_in_out':
          animationCurve = Curves.elasticInOut;
          break;
        case 'bouncein':
        case 'bounce_in':
          animationCurve = Curves.bounceIn;
          break;
        case 'bounceout':
        case 'bounce_out':
          animationCurve = Curves.bounceOut;
          break;
        case 'bounceinout':
        case 'bounce_in_out':
          animationCurve = Curves.bounceInOut;
          break;
        default:
          animationCurve = Curves.easeInOut;
          break;
      }
    }

    // Handle brightness
    Brightness? brightness;
    if (json['brightness'] != null) {
      switch (json['brightness'].toString().toLowerCase()) {
        case 'dark':
          brightness = Brightness.dark;
          break;
        case 'light':
          brightness = Brightness.light;
          break;
      }
    }

    // Handle shadow offset
    Offset shadowOffset = const Offset(0, 2);
    if (json['shadowOffset'] != null && json['shadowOffset'] is Map) {
      Map<String, dynamic> offsetMap = json['shadowOffset'] as Map<String, dynamic>;
      double dx = (offsetMap['dx'] as num?)?.toDouble() ?? 0.0;
      double dy = (offsetMap['dy'] as num?)?.toDouble() ?? 2.0;
      shadowOffset = Offset(dx, dy);
    }

    return ButtonWidget(
      text: json['text'] as String? ?? 'Button',
      backgroundColor: _colorFromJson(json['backgroundColor']) ?? Colors.blue,
      textColor: _colorFromJson(json['textColor']) ?? Colors.white,
      borderColor: _colorFromJson(json['borderColor']),
      borderWidth: (json['borderWidth'] as num?)?.toDouble() ?? 1.0,
      borderRadius: (json['borderRadius'] as num?)?.toDouble() ?? 8.0,
      icon: icon,
      iconColor: _colorFromJson(json['iconColor']),
      iconSize: (json['iconSize'] as num?)?.toDouble() ?? 18.0,
      iconSpacing: (json['iconSpacing'] as num?)?.toDouble() ?? 8.0,
      padding: padding,
      elevation: (json['elevation'] as num?)?.toDouble() ?? 0.0,
      fontSize: (json['fontSize'] as num?)?.toDouble() ?? 16.0,
      fontWeight: fontWeight,
      isOutlined: json['isOutlined'] as bool? ?? false,
      isFilled: json['isFilled'] as bool? ?? true,
      hasGradient: json['hasGradient'] as bool? ?? false,
      gradientColors: gradientColors,
      gradientBegin: gradientBegin,
      gradientEnd: gradientEnd,
      isDisabled: json['isDisabled'] as bool? ?? false,
      width: (json['width'] as num?)?.toDouble(),
      height: (json['height'] as num?)?.toDouble(),
      isFullWidth: json['isFullWidth'] as bool? ?? false,
      isLoading: json['isLoading'] as bool? ?? false,
      loadingColor: _colorFromJson(json['loadingColor']),
      loadingSize: (json['loadingSize'] as num?)?.toDouble() ?? 20.0,
      hasRippleEffect: json['hasRippleEffect'] as bool? ?? true,
      rippleColor: _colorFromJson(json['rippleColor']),
      // Additional Text Styling Properties
      textDecoration: textDecoration,
      textOverflow: textOverflow,
      maxLines: json['maxLines'] as int?,
      textAlign: textAlign,
      letterSpacing: (json['letterSpacing'] as num?)?.toDouble() ?? 0.0,
      wordSpacing: (json['wordSpacing'] as num?)?.toDouble() ?? 0.0,
      textDirection: textDirection,
      // Additional Visual Enhancement Properties
      shadowColor: _colorFromJson(json['shadowColor']) ?? Colors.black,
      shadowOffset: shadowOffset,
      opacity: (json['opacity'] as num?)?.toDouble() ?? 1.0,
      brightness: brightness,
      blurRadius: (json['blurRadius'] as num?)?.toDouble() ?? 3.0,
      spreadRadius: (json['spreadRadius'] as num?)?.toDouble() ?? 0.0,
      // Additional Layout Properties
      margin: margin,
      alignment: alignment,
      transform: transform,
      // Additional Animation Properties
      animationDuration: animationDuration,
      animationCurve: animationCurve,
      animationType: json['animationType'] as String? ?? 'fade',
      // Additional Interaction Properties
      longPressEnabled: json['longPressEnabled'] as bool? ?? false,
      onLongPress: json['onLongPress'] == true ? () {
        debugPrint('Button long pressed: ${json['text'] ?? 'Button'}');
      } : null,
      doubleTapEnabled: json['doubleTapEnabled'] as bool? ?? false,
      onDoubleTap: json['onDoubleTap'] == true ? () {
        debugPrint('Button double tapped: ${json['text'] ?? 'Button'}');
      } : null,
      hoverColor: _colorFromJson(json['hoverColor']),
      splashColor: _colorFromJson(json['splashColor']),
      highlightColor: _colorFromJson(json['highlightColor']),
      focusColor: _colorFromJson(json['focusColor']),
      // Icon Position Properties
      iconPosition: json['iconPosition'] as String? ?? 'left',
      iconAlignment: iconAlignment,
      // Additional Loading Properties
      loadingType: json['loadingType'] as String? ?? 'circular',
      loadingText: json['loadingText'] as String?,
      // Accessibility Properties
      semanticsLabel: json['semanticsLabel'] as String?,
      tooltip: json['tooltip'] as String?,
      enableFeedback: json['enableFeedback'] as bool? ?? true,
      // Advanced Interaction Properties
      autofocus: json['autofocus'] as bool? ?? false,
      onHover: json['onHover'] == true ? (isHovered) {
        debugPrint('Button hover state changed: ${json['text'] ?? 'Button'}, isHovered: $isHovered');
      } : null,
      onFocus: json['onFocus'] == true ? (isFocused) {
        debugPrint('Button focus state changed: ${json['text'] ?? 'Button'}, isFocused: $isFocused');
      } : null,
      // Note: focusNode is not created from JSON as it requires a live object reference
      // Original callback
      onPressed: json['isClickable'] == true || json['onPressed'] == true ? () {
        // This would be handled by the app in a real implementation
        debugPrint('Button pressed: ${json['text'] ?? 'Button'}');
      } : null,
    );
  }

  /// Converts a JSON color value to a Flutter Color
  ///
  /// Accepts hex strings (e.g., "#FF0000"), color names (e.g., "red"),
  /// or integer values (e.g., 0xFFFF0000)
  static Color? _colorFromJson(dynamic colorValue) {
    if (colorValue == null) return null;

    if (colorValue is String) {
      // Handle hex strings like "#FF0000"
      if (colorValue.startsWith('#')) {
        String hexColor = colorValue.substring(1);

        // Handle shorthand hex like #RGB
        if (hexColor.length == 3) {
          hexColor = hexColor.split('').map((c) => '$c$c').join('');
        }

        // Add alpha channel if missing
        if (hexColor.length == 6) {
          hexColor = 'FF$hexColor';
        }

        // Parse the hex value
        try {
          return Color(int.parse('0x$hexColor'));
        } catch (e) {
          // Silently handle the error and return null
          return null;
        }
      }

      // Handle named colors
      switch (colorValue.toLowerCase()) {
        case 'red': return Colors.red;
        case 'blue': return Colors.blue;
        case 'green': return Colors.green;
        case 'yellow': return Colors.yellow;
        case 'orange': return Colors.orange;
        case 'purple': return Colors.purple;
        case 'pink': return Colors.pink;
        case 'brown': return Colors.brown;
        case 'grey':
        case 'gray': return Colors.grey;
        case 'black': return Colors.black;
        case 'white': return Colors.white;
        case 'amber': return Colors.amber;
        case 'cyan': return Colors.cyan;
        case 'indigo': return Colors.indigo;
        case 'lime': return Colors.lime;
        case 'teal': return Colors.teal;
        default: return null;
      }
    } else if (colorValue is int) {
      // Handle integer color values
      return Color(colorValue);
    }

    return null;
  }

  /// Gets an IconData from a string name
  static IconData? _getIconData(String iconName) {
    switch (iconName.toLowerCase()) {
      case 'add': return Icons.add;
      case 'remove': return Icons.remove;
      case 'close': return Icons.close;
      case 'check': return Icons.check;
      case 'star': return Icons.star;
      case 'favorite': return Icons.favorite;
      case 'heart': return Icons.favorite;
      case 'info': return Icons.info;
      case 'warning': return Icons.warning;
      case 'error': return Icons.error;
      case 'help': return Icons.help;
      case 'settings': return Icons.settings;
      case 'person': return Icons.person;
      case 'people': return Icons.people;
      case 'home': return Icons.home;
      case 'search': return Icons.search;
      case 'send': return Icons.send;
      case 'mail': return Icons.mail;
      case 'email': return Icons.email;
      case 'phone': return Icons.phone;
      case 'message': return Icons.message;
      case 'chat': return Icons.chat;
      case 'comment': return Icons.comment;
      case 'notifications': return Icons.notifications;
      case 'calendar': return Icons.calendar_today;
      case 'event': return Icons.event;
      case 'schedule': return Icons.schedule;
      case 'clock': return Icons.access_time;
      case 'location': return Icons.location_on;
      case 'place': return Icons.place;
      case 'map': return Icons.map;
      case 'directions': return Icons.directions;
      case 'camera': return Icons.camera_alt;
      case 'photo': return Icons.photo;
      case 'image': return Icons.image;
      case 'video': return Icons.videocam;
      case 'music': return Icons.music_note;
      case 'audio': return Icons.audiotrack;
      case 'volume': return Icons.volume_up;
      case 'mic': return Icons.mic;
      case 'play': return Icons.play_arrow;
      case 'pause': return Icons.pause;
      case 'stop': return Icons.stop;
      case 'skip_next': return Icons.skip_next;
      case 'skip_previous': return Icons.skip_previous;
      case 'fast_forward': return Icons.fast_forward;
      case 'fast_rewind': return Icons.fast_rewind;
      case 'repeat': return Icons.repeat;
      case 'shuffle': return Icons.shuffle;
      case 'playlist': return Icons.playlist_play;
      case 'album': return Icons.album;
      case 'artist': return Icons.person;
      case 'genre': return Icons.category;
      case 'file': return Icons.insert_drive_file;
      case 'folder': return Icons.folder;
      case 'cloud': return Icons.cloud;
      case 'wifi': return Icons.wifi;
      case 'bluetooth': return Icons.bluetooth;
      case 'battery': return Icons.battery_full;
      case 'power': return Icons.power_settings_new;
      case 'refresh': return Icons.refresh;
      case 'sync': return Icons.sync;
      case 'update': return Icons.update;
      case 'more': return Icons.more_horiz;
      case 'menu': return Icons.menu;
      case 'list': return Icons.list;
      case 'grid': return Icons.grid_view;
      case 'dashboard': return Icons.dashboard;
      case 'filter': return Icons.filter_list;
      case 'sort': return Icons.sort;
      case 'shopping_cart': return Icons.shopping_cart;
      case 'shop': return Icons.shop;
      case 'store': return Icons.store;
      case 'payment': return Icons.payment;
      case 'credit_card': return Icons.credit_card;
      case 'money': return Icons.attach_money;
      case 'wallet': return Icons.account_balance_wallet;
      case 'account': return Icons.account_circle;
      case 'login': return Icons.login;
      case 'logout': return Icons.logout;
      case 'security': return Icons.security;
      case 'lock': return Icons.lock;
      case 'unlock': return Icons.lock_open;
      case 'visibility': return Icons.visibility;
      case 'visibility_off': return Icons.visibility_off;
      case 'flash_on': return Icons.flash_on;
      case 'flash_off': return Icons.flash_off;
      case 'lightbulb': return Icons.lightbulb;
      case 'dark_mode': return Icons.dark_mode;
      case 'light_mode': return Icons.light_mode;
      case 'color_lens': return Icons.color_lens;
      case 'brush': return Icons.brush;
      case 'format': return Icons.format_paint;
      case 'text': return Icons.text_fields;
      case 'font': return Icons.font_download;
      case 'bold': return Icons.format_bold;
      case 'italic': return Icons.format_italic;
      case 'underline': return Icons.format_underlined;
      case 'align_left': return Icons.format_align_left;
      case 'align_center': return Icons.format_align_center;
      case 'align_right': return Icons.format_align_right;
      case 'align_justify': return Icons.format_align_justify;
      case 'indent': return Icons.format_indent_increase;
      case 'outdent': return Icons.format_indent_decrease;
      case 'list_bulleted': return Icons.format_list_bulleted;
      case 'list_numbered': return Icons.format_list_numbered;
      case 'clear': return Icons.clear;
      case 'undo': return Icons.undo;
      case 'redo': return Icons.redo;
      case 'copy': return Icons.content_copy;
      case 'paste': return Icons.content_paste;
      case 'cut': return Icons.content_cut;
      case 'select_all': return Icons.select_all;
      case 'print': return Icons.print;
      case 'scanner': return Icons.scanner;
      case 'arrow_forward': return Icons.arrow_forward;
      case 'arrow_back': return Icons.arrow_back;
      case 'arrow_upward': return Icons.arrow_upward;
      case 'arrow_downward': return Icons.arrow_downward;
      case 'chevron_right': return Icons.chevron_right;
      case 'chevron_left': return Icons.chevron_left;
      case 'expand_more': return Icons.expand_more;
      case 'expand_less': return Icons.expand_less;
      case 'fullscreen': return Icons.fullscreen;
      case 'fullscreen_exit': return Icons.fullscreen_exit;
      case 'zoom_in': return Icons.zoom_in;
      case 'zoom_out': return Icons.zoom_out;
      case 'delete': return Icons.delete;
      case 'edit': return Icons.edit;
      case 'save': return Icons.save;
      case 'share': return Icons.share;
      case 'download': return Icons.download;
      case 'upload': return Icons.upload;
      case 'attach': return Icons.attach_file;
      case 'link': return Icons.link;
      default: return null;
    }
  }

  /// Converts a string to an Alignment
  static Alignment _alignmentFromString(String alignmentStr) {
    switch (alignmentStr.toLowerCase()) {
      case 'topleft':
      case 'top_left':
      case 'top left':
        return Alignment.topLeft;
      case 'topcenter':
      case 'top_center':
      case 'top center':
        return Alignment.topCenter;
      case 'topright':
      case 'top_right':
      case 'top right':
        return Alignment.topRight;
      case 'centerleft':
      case 'center_left':
      case 'center left':
        return Alignment.centerLeft;
      case 'center':
        return Alignment.center;
      case 'centerright':
      case 'center_right':
      case 'center right':
        return Alignment.centerRight;
      case 'bottomleft':
      case 'bottom_left':
      case 'bottom left':
        return Alignment.bottomLeft;
      case 'bottomcenter':
      case 'bottom_center':
      case 'bottom center':
        return Alignment.bottomCenter;
      case 'bottomright':
      case 'bottom_right':
      case 'bottom right':
        return Alignment.bottomRight;
      default:
        return Alignment.center;
    }
  }

  /// Converts an Alignment to a string
  static String _alignmentToString(Alignment alignment) {
    if (alignment == Alignment.topLeft) return 'top_left';
    if (alignment == Alignment.topCenter) return 'top_center';
    if (alignment == Alignment.topRight) return 'top_right';
    if (alignment == Alignment.centerLeft) return 'center_left';
    if (alignment == Alignment.center) return 'center';
    if (alignment == Alignment.centerRight) return 'center_right';
    if (alignment == Alignment.bottomLeft) return 'bottom_left';
    if (alignment == Alignment.bottomCenter) return 'bottom_center';
    if (alignment == Alignment.bottomRight) return 'bottom_right';
    return 'center';
  }

  /// Gets a string name from an IconData
  static String? _getIconName(IconData? icon) {
    if (icon == null) return null;

    if (icon == Icons.add) return 'add';
    if (icon == Icons.remove) return 'remove';
    if (icon == Icons.close) return 'close';
    if (icon == Icons.check) return 'check';
    if (icon == Icons.star) return 'star';
    if (icon == Icons.favorite) return 'favorite';
    if (icon == Icons.info) return 'info';
    if (icon == Icons.warning) return 'warning';
    if (icon == Icons.error) return 'error';
    if (icon == Icons.help) return 'help';
    if (icon == Icons.settings) return 'settings';
    if (icon == Icons.person) return 'person';
    if (icon == Icons.people) return 'people';
    if (icon == Icons.home) return 'home';
    if (icon == Icons.search) return 'search';
    if (icon == Icons.send) return 'send';
    if (icon == Icons.mail) return 'mail';
    if (icon == Icons.email) return 'email';
    if (icon == Icons.phone) return 'phone';
    if (icon == Icons.message) return 'message';
    if (icon == Icons.chat) return 'chat';
    if (icon == Icons.comment) return 'comment';
    if (icon == Icons.notifications) return 'notifications';
    if (icon == Icons.calendar_today) return 'calendar';
    if (icon == Icons.event) return 'event';
    if (icon == Icons.schedule) return 'schedule';
    if (icon == Icons.access_time) return 'clock';
    if (icon == Icons.location_on) return 'location';
    if (icon == Icons.place) return 'place';
    if (icon == Icons.map) return 'map';
    if (icon == Icons.directions) return 'directions';
    if (icon == Icons.camera_alt) return 'camera';
    if (icon == Icons.photo) return 'photo';
    if (icon == Icons.image) return 'image';
    if (icon == Icons.videocam) return 'video';
    if (icon == Icons.music_note) return 'music';
    if (icon == Icons.audiotrack) return 'audio';
    if (icon == Icons.volume_up) return 'volume';
    if (icon == Icons.mic) return 'mic';
    if (icon == Icons.play_arrow) return 'play';
    if (icon == Icons.pause) return 'pause';
    if (icon == Icons.stop) return 'stop';
    if (icon == Icons.skip_next) return 'skip_next';
    if (icon == Icons.skip_previous) return 'skip_previous';
    if (icon == Icons.fast_forward) return 'fast_forward';
    if (icon == Icons.fast_rewind) return 'fast_rewind';
    if (icon == Icons.repeat) return 'repeat';
    if (icon == Icons.shuffle) return 'shuffle';
    if (icon == Icons.playlist_play) return 'playlist';
    if (icon == Icons.album) return 'album';
    if (icon == Icons.category) return 'genre';
    if (icon == Icons.insert_drive_file) return 'file';
    if (icon == Icons.folder) return 'folder';
    if (icon == Icons.cloud) return 'cloud';
    if (icon == Icons.wifi) return 'wifi';
    if (icon == Icons.bluetooth) return 'bluetooth';
    if (icon == Icons.battery_full) return 'battery';
    if (icon == Icons.power_settings_new) return 'power';
    if (icon == Icons.refresh) return 'refresh';
    if (icon == Icons.sync) return 'sync';
    if (icon == Icons.update) return 'update';
    if (icon == Icons.more_horiz) return 'more';
    if (icon == Icons.menu) return 'menu';
    if (icon == Icons.list) return 'list';
    if (icon == Icons.grid_view) return 'grid';
    if (icon == Icons.dashboard) return 'dashboard';
    if (icon == Icons.filter_list) return 'filter';
    if (icon == Icons.sort) return 'sort';
    if (icon == Icons.shopping_cart) return 'shopping_cart';
    if (icon == Icons.shop) return 'shop';
    if (icon == Icons.store) return 'store';
    if (icon == Icons.payment) return 'payment';
    if (icon == Icons.credit_card) return 'credit_card';
    if (icon == Icons.attach_money) return 'money';
    if (icon == Icons.account_balance_wallet) return 'wallet';
    if (icon == Icons.account_circle) return 'account';
    if (icon == Icons.login) return 'login';
    if (icon == Icons.logout) return 'logout';
    if (icon == Icons.security) return 'security';
    if (icon == Icons.lock) return 'lock';
    if (icon == Icons.lock_open) return 'unlock';
    if (icon == Icons.visibility) return 'visibility';
    if (icon == Icons.visibility_off) return 'visibility_off';
    if (icon == Icons.flash_on) return 'flash_on';
    if (icon == Icons.flash_off) return 'flash_off';
    if (icon == Icons.lightbulb) return 'lightbulb';
    if (icon == Icons.dark_mode) return 'dark_mode';
    if (icon == Icons.light_mode) return 'light_mode';
    if (icon == Icons.color_lens) return 'color_lens';
    if (icon == Icons.brush) return 'brush';
    if (icon == Icons.format_paint) return 'format';
    if (icon == Icons.text_fields) return 'text';
    if (icon == Icons.font_download) return 'font';
    if (icon == Icons.format_bold) return 'bold';
    if (icon == Icons.format_italic) return 'italic';
    if (icon == Icons.format_underlined) return 'underline';
    if (icon == Icons.format_align_left) return 'align_left';
    if (icon == Icons.format_align_center) return 'align_center';
    if (icon == Icons.format_align_right) return 'align_right';
    if (icon == Icons.format_align_justify) return 'align_justify';
    if (icon == Icons.format_indent_increase) return 'indent';
    if (icon == Icons.format_indent_decrease) return 'outdent';
    if (icon == Icons.format_list_bulleted) return 'list_bulleted';
    if (icon == Icons.format_list_numbered) return 'list_numbered';
    if (icon == Icons.clear) return 'clear';
    if (icon == Icons.undo) return 'undo';
    if (icon == Icons.redo) return 'redo';
    if (icon == Icons.content_copy) return 'copy';
    if (icon == Icons.content_paste) return 'paste';
    if (icon == Icons.content_cut) return 'cut';
    if (icon == Icons.select_all) return 'select_all';
    if (icon == Icons.print) return 'print';
    if (icon == Icons.scanner) return 'scanner';
    if (icon == Icons.arrow_forward) return 'arrow_forward';
    if (icon == Icons.arrow_back) return 'arrow_back';
    if (icon == Icons.arrow_upward) return 'arrow_upward';
    if (icon == Icons.arrow_downward) return 'arrow_downward';
    if (icon == Icons.chevron_right) return 'chevron_right';
    if (icon == Icons.chevron_left) return 'chevron_left';
    if (icon == Icons.expand_more) return 'expand_more';
    if (icon == Icons.expand_less) return 'expand_less';
    if (icon == Icons.fullscreen) return 'fullscreen';
    if (icon == Icons.fullscreen_exit) return 'fullscreen_exit';
    if (icon == Icons.zoom_in) return 'zoom_in';
    if (icon == Icons.zoom_out) return 'zoom_out';
    if (icon == Icons.delete) return 'delete';
    if (icon == Icons.edit) return 'edit';
    if (icon == Icons.save) return 'save';
    if (icon == Icons.share) return 'share';
    if (icon == Icons.download) return 'download';
    if (icon == Icons.upload) return 'upload';
    if (icon == Icons.attach_file) return 'attach';
    if (icon == Icons.link) return 'link';

    return null;
  }

  /// Converts a Flutter Color to a JSON representation
  ///
  /// Returns a hex string (e.g., "#FF0000") or a color name for standard colors
  static String _colorToJson(Color color) {
    // Handle standard colors by name for better readability
    if (color == Colors.red) return 'red';
    if (color == Colors.blue) return 'blue';
    if (color == Colors.green) return 'green';
    if (color == Colors.yellow) return 'yellow';
    if (color == Colors.orange) return 'orange';
    if (color == Colors.purple) return 'purple';
    if (color == Colors.pink) return 'pink';
    if (color == Colors.brown) return 'brown';
    if (color == Colors.grey) return 'grey';
    if (color == Colors.black) return 'black';
    if (color == Colors.white) return 'white';
    if (color == Colors.amber) return 'amber';
    if (color == Colors.cyan) return 'cyan';
    if (color == Colors.indigo) return 'indigo';
    if (color == Colors.lime) return 'lime';
    if (color == Colors.teal) return 'teal';

    // For MaterialColor, preserve the original color name if possible
    if (color is MaterialColor) {
      if (color == Colors.red) return 'red';
      if (color == Colors.blue) return 'blue';
      if (color == Colors.green) return 'green';
      if (color == Colors.yellow) return 'yellow';
      if (color == Colors.orange) return 'orange';
      if (color == Colors.purple) return 'purple';
      if (color == Colors.pink) return 'pink';
      if (color == Colors.brown) return 'brown';
      if (color == Colors.grey) return 'grey';
      if (color == Colors.amber) return 'amber';
      if (color == Colors.cyan) return 'cyan';
      if (color == Colors.indigo) return 'indigo';
      if (color == Colors.lime) return 'lime';
      if (color == Colors.teal) return 'teal';

      // If it's a MaterialColor but not one of the standard ones,
      // fall back to the hex representation of the primary value
      color = color.shade500;
    }

    // Convert to RGB format and create a hex string for other colors
    // Use the color value directly to avoid deprecated properties
    final hex = color.toString().replaceAll('Color(0xff', '').replaceAll(')', '');
    return '#$hex';
  }

  /// Converts the ButtonWidget to a JSON map
  ///
  /// This method allows for serializing the widget configuration to JSON,
  /// which can be useful for saving configurations or sharing them.
  Map<String, dynamic> toJson() {
    // Convert padding to a map
    Map<String, dynamic> paddingMap = {};
    if (padding is EdgeInsets) {
      EdgeInsets p = padding as EdgeInsets;
      paddingMap = {
        'left': p.left,
        'top': p.top,
        'right': p.right,
        'bottom': p.bottom,
      };
    }

    // Convert margin to a map
    Map<String, dynamic> marginMap = {};
    if (margin is EdgeInsets) {
      EdgeInsets m = margin as EdgeInsets;
      marginMap = {
        'left': m.left,
        'top': m.top,
        'right': m.right,
        'bottom': m.bottom,
      };
    }

    // Convert font weight to string
    String fontWeightString = 'normal';
    if (fontWeight == FontWeight.bold) {
      fontWeightString = 'bold';
    } else if (fontWeight == FontWeight.w100) {
      fontWeightString = '100';
    } else if (fontWeight == FontWeight.w200) {
      fontWeightString = '200';
    } else if (fontWeight == FontWeight.w300) {
      fontWeightString = '300';
    } else if (fontWeight == FontWeight.w400) {
      fontWeightString = '400';
    } else if (fontWeight == FontWeight.w500) {
      fontWeightString = '500';
    } else if (fontWeight == FontWeight.w600) {
      fontWeightString = '600';
    } else if (fontWeight == FontWeight.w700) {
      fontWeightString = '700';
    } else if (fontWeight == FontWeight.w800) {
      fontWeightString = '800';
    } else if (fontWeight == FontWeight.w900) {
      fontWeightString = '900';
    }

    // Convert text decoration to string
    String textDecorationString = 'none';
    if (textDecoration == TextDecoration.underline) {
      textDecorationString = 'underline';
    } else if (textDecoration == TextDecoration.overline) {
      textDecorationString = 'overline';
    } else if (textDecoration == TextDecoration.lineThrough) {
      textDecorationString = 'line-through';
    }

    // Convert text overflow to string
    String textOverflowString = 'clip';
    if (textOverflow == TextOverflow.ellipsis) {
      textOverflowString = 'ellipsis';
    } else if (textOverflow == TextOverflow.fade) {
      textOverflowString = 'fade';
    } else if (textOverflow == TextOverflow.visible) {
      textOverflowString = 'visible';
    }

    // Convert text align to string
    String textAlignString = 'center';
    if (textAlign == TextAlign.left) {
      textAlignString = 'left';
    } else if (textAlign == TextAlign.right) {
      textAlignString = 'right';
    } else if (textAlign == TextAlign.start) {
      textAlignString = 'start';
    } else if (textAlign == TextAlign.end) {
      textAlignString = 'end';
    } else if (textAlign == TextAlign.justify) {
      textAlignString = 'justify';
    }

    // Convert text direction to string
    String? textDirectionString;
    if (textDirection == TextDirection.ltr) {
      textDirectionString = 'ltr';
    } else if (textDirection == TextDirection.rtl) {
      textDirectionString = 'rtl';
    }

    // Convert brightness to string
    String? brightnessString;
    if (brightness == Brightness.dark) {
      brightnessString = 'dark';
    } else if (brightness == Brightness.light) {
      brightnessString = 'light';
    }

    // Convert shadow offset to map
    Map<String, dynamic> shadowOffsetMap = {
      'dx': shadowOffset.dx,
      'dy': shadowOffset.dy,
    };

    // Convert transform to map if not null
    Map<String, dynamic>? transformMap;
    if (transform != null) {
      // For simplicity, we'll just include a placeholder for the transform
      // since extracting values from Matrix4 is complex
      transformMap = {
        'rotation': 0.0,
        'scaleX': 1.0,
        'scaleY': 1.0,
        'translateX': 0.0,
        'translateY': 0.0,
      };
    }

    // Convert animation duration to milliseconds
    int animationDurationMs = animationDuration.inMilliseconds;

    // Convert animation curve to string
    String animationCurveString = 'ease_in_out';
    if (animationCurve == Curves.linear) {
      animationCurveString = 'linear';
    } else if (animationCurve == Curves.decelerate) {
      animationCurveString = 'decelerate';
    } else if (animationCurve == Curves.ease) {
      animationCurveString = 'ease';
    } else if (animationCurve == Curves.easeIn) {
      animationCurveString = 'ease_in';
    } else if (animationCurve == Curves.easeOut) {
      animationCurveString = 'ease_out';
    } else if (animationCurve == Curves.elasticIn) {
      animationCurveString = 'elastic_in';
    } else if (animationCurve == Curves.elasticOut) {
      animationCurveString = 'elastic_out';
    } else if (animationCurve == Curves.elasticInOut) {
      animationCurveString = 'elastic_in_out';
    } else if (animationCurve == Curves.bounceIn) {
      animationCurveString = 'bounce_in';
    } else if (animationCurve == Curves.bounceOut) {
      animationCurveString = 'bounce_out';
    } else if (animationCurve == Curves.bounceInOut) {
      animationCurveString = 'bounce_in_out';
    }

    // Convert gradient colors to list of strings
    List<String>? gradientColorStrings;
    if (gradientColors != null) {
      gradientColorStrings = gradientColors!.map((color) => _colorToJson(color)).toList();
    }

    // Build the JSON map with all properties
    final Map<String, dynamic> json = {
      // Basic properties
      'text': text,
      'backgroundColor': _colorToJson(backgroundColor),
      'textColor': _colorToJson(textColor),
      'borderWidth': borderWidth,
      'borderRadius': borderRadius,
      'iconSize': iconSize,
      'iconSpacing': iconSpacing,
      'padding': paddingMap,
      'elevation': elevation,
      'fontSize': fontSize,
      'fontWeight': fontWeightString,
      'isOutlined': isOutlined,
      'isFilled': isFilled,
      'hasGradient': hasGradient,
      'gradientBegin': _alignmentToString(gradientBegin),
      'gradientEnd': _alignmentToString(gradientEnd),
      'isDisabled': isDisabled,
      'isFullWidth': isFullWidth,
      'isLoading': isLoading,
      'loadingSize': loadingSize,
      'hasRippleEffect': hasRippleEffect,
      'isClickable': onPressed != null,

      // Additional Text Styling Properties
      'textDecoration': textDecorationString,
      'textOverflow': textOverflowString,
      'textAlign': textAlignString,
      'letterSpacing': letterSpacing,
      'wordSpacing': wordSpacing,

      // Additional Visual Enhancement Properties
      'shadowColor': _colorToJson(shadowColor),
      'shadowOffset': shadowOffsetMap,
      'opacity': opacity,
      'blurRadius': blurRadius,
      'spreadRadius': spreadRadius,

      // Additional Layout Properties
      'margin': marginMap,

      // Additional Animation Properties
      'animationDuration': animationDurationMs,
      'animationCurve': animationCurveString,
      'animationType': animationType,

      // Additional Interaction Properties
      'longPressEnabled': longPressEnabled,
      'doubleTapEnabled': doubleTapEnabled,

      // Icon Position Properties
      'iconPosition': iconPosition,
      'iconAlignment': _alignmentToString(iconAlignment),

      // Additional Loading Properties
      'loadingType': loadingType,

      // Accessibility Properties
      'enableFeedback': enableFeedback,

      // Advanced Interaction Properties
      'autofocus': autofocus,
    };

    // Add optional properties only if they are not null
    if (borderColor != null) {
      json['borderColor'] = _colorToJson(borderColor!);
    }

    if (icon != null) {
      json['icon'] = _getIconName(icon);
    }

    if (iconColor != null) {
      json['iconColor'] = _colorToJson(iconColor!);
    }

    if (loadingColor != null) {
      json['loadingColor'] = _colorToJson(loadingColor!);
    }

    if (rippleColor != null) {
      json['rippleColor'] = _colorToJson(rippleColor!);
    }

    if (width != null) {
      json['width'] = width;
    }

    if (height != null) {
      json['height'] = height;
    }

    if (gradientColorStrings != null) {
      json['gradientColors'] = gradientColorStrings;
    }

    if (maxLines != null) {
      json['maxLines'] = maxLines;
    }

    if (textDirectionString != null) {
      json['textDirection'] = textDirectionString;
    }

    if (brightnessString != null) {
      json['brightness'] = brightnessString;
    }

    if (alignment != null) {
      json['alignment'] = _alignmentToString(alignment!);
    }

    if (transformMap != null) {
      json['transform'] = transformMap;
    }

    if (onLongPress != null) {
      json['onLongPress'] = true;
    }

    if (onDoubleTap != null) {
      json['onDoubleTap'] = true;
    }

    if (onHover != null) {
      json['onHover'] = true;
    }

    if (onFocus != null) {
      json['onFocus'] = true;
    }

    if (hoverColor != null) {
      json['hoverColor'] = _colorToJson(hoverColor!);
    }

    if (splashColor != null) {
      json['splashColor'] = _colorToJson(splashColor!);
    }

    if (highlightColor != null) {
      json['highlightColor'] = _colorToJson(highlightColor!);
    }

    if (focusColor != null) {
      json['focusColor'] = _colorToJson(focusColor!);
    }

    if (loadingText != null) {
      json['loadingText'] = loadingText;
    }

    if (semanticsLabel != null) {
      json['semanticsLabel'] = semanticsLabel;
    }

    if (tooltip != null) {
      json['tooltip'] = tooltip;
    }

    return json;
  }

  @override
  State<ButtonWidget> createState() => _ButtonWidgetState();
}

class _ButtonWidgetState extends State<ButtonWidget> {
  late FocusNode _focusNode;
  bool _isFocused = false;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_handleFocusChange);

    if (widget.autofocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      // Only dispose the focus node if we created it
      _focusNode.removeListener(_handleFocusChange);
      _focusNode.dispose();
    }
    super.dispose();
  }

  void _handleFocusChange() {
    if (_focusNode.hasFocus != _isFocused) {
      setState(() {
        _isFocused = _focusNode.hasFocus;
      });

      if (widget.onFocus != null) {
        widget.onFocus!(_isFocused);
      }
    }
  }

  void _handleHoverChange(bool isHovered) {
    if (isHovered != _isHovered) {
      setState(() {
        _isHovered = isHovered;
      });

      if (widget.onHover != null) {
        widget.onHover!(isHovered);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Determine the background decoration based on configuration
    BoxDecoration decoration;
    if (widget.hasGradient && widget.gradientColors != null && widget.gradientColors!.length >= 2) {
      // Gradient background
      decoration = BoxDecoration(
        gradient: LinearGradient(
          colors: widget.gradientColors!,
          begin: widget.gradientBegin,
          end: widget.gradientEnd,
        ),
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.borderColor != null || widget.isOutlined
            ? Border.all(
                color: widget.borderColor ?? widget.backgroundColor,
                width: widget.borderWidth,
              )
            : null,
      );
    } else if (widget.isOutlined) {
      // Outlined style (transparent background with border)
      decoration = BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: Border.all(
          color: widget.borderColor ?? widget.backgroundColor,
          width: widget.borderWidth,
        ),
      );
    } else if (widget.isFilled) {
      // Filled style (solid background)
      decoration = BoxDecoration(
        color: widget.isDisabled ?
          widget.backgroundColor.withAlpha((255 * 0.6).round()) : widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.borderColor != null
            ? Border.all(
                color: widget.borderColor!,
                width: widget.borderWidth,
              )
            : null,
      );
    } else {
      // Default style
      decoration = BoxDecoration(
        color: widget.isDisabled ?
          widget.backgroundColor.withAlpha((255 * 0.6).round()) : widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.borderColor != null
            ? Border.all(
                color: widget.borderColor!,
                width: widget.borderWidth,
              )
            : null,
      );
    }

    // Create the button content
    Widget buttonContent = Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (widget.isLoading)
          SizedBox(
            width: widget.loadingSize,
            height: widget.loadingSize,
            child: CircularProgressIndicator(
              strokeWidth: 2.0,
              valueColor: AlwaysStoppedAnimation<Color>(
                widget.loadingColor ?? widget.textColor,
              ),
            ),
          )
        else if (widget.icon != null) ...[
          Icon(
            widget.icon,
            size: widget.iconSize,
            color: widget.iconColor ?? (widget.isOutlined ? widget.backgroundColor : widget.textColor),
          ),
          SizedBox(width: widget.iconSpacing),
        ],
        Text(
          widget.text,
          style: TextStyle(
            color: widget.isOutlined ? widget.backgroundColor : widget.textColor,
            fontSize: widget.fontSize,
            fontWeight: widget.fontWeight,
          ),
        ),
      ],
    );

    // Wrap in a container with the appropriate size
    if (widget.isFullWidth || widget.width != null || widget.height != null) {
      buttonContent = Container(
        width: widget.isFullWidth ? double.infinity : widget.width,
        height: widget.height,
        alignment: Alignment.center,
        child: buttonContent,
      );
    }

    // Create the final button with Material for elevation and ink effects
    Widget buttonWidget = Material(
      color: Colors.transparent,
      elevation: widget.isDisabled ? 0 : widget.elevation,
      borderRadius: BorderRadius.circular(widget.borderRadius),
      child: MouseRegion(
        onEnter: (_) => _handleHoverChange(true),
        onExit: (_) => _handleHoverChange(false),
        child: InkWell(
          onTap: widget.isDisabled || widget.isLoading ? null : widget.onPressed,
          onLongPress: widget.longPressEnabled ? widget.onLongPress : null,
          onDoubleTap: widget.doubleTapEnabled ? widget.onDoubleTap : null,
          focusNode: _focusNode,
          focusColor: widget.focusColor,
          hoverColor: widget.hoverColor,
          borderRadius: BorderRadius.circular(widget.borderRadius),
          splashColor: widget.hasRippleEffect ?
            (widget.rippleColor ?? widget.backgroundColor.withAlpha((255 * 0.3).round())) :
            Colors.transparent,
          highlightColor: widget.hasRippleEffect ?
            (widget.rippleColor ?? widget.backgroundColor.withAlpha((255 * 0.1).round())) :
            Colors.transparent,
          enableFeedback: widget.enableFeedback,
          child: Ink(
            decoration: decoration,
            child: Padding(
              padding: widget.padding,
              child: buttonContent,
            ),
          ),
        ),
      ),
    );

    // Wrap with tooltip if provided
    if (widget.tooltip != null) {
      buttonWidget = Tooltip(
        message: widget.tooltip!,
        child: buttonWidget,
      );
    }

    // Wrap with Semantics for accessibility if semanticsLabel is provided
    if (widget.semanticsLabel != null) {
      buttonWidget = Semantics(
        label: widget.semanticsLabel,
        button: true,
        enabled: !widget.isDisabled,
        child: buttonWidget,
      );
    }

    return buttonWidget;
  }
}
