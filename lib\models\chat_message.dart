import 'package:flutter/material.dart';
import 'package:nsl/models/multimedia/file_upload_ocr_response.dart';

/// A simple message model for chat
// Define a simple message model for our chat
class ChatMessage {
  String content;
  final bool isUser;
  final DateTime timestamp;
  final Widget? customContent;
  List<String>? reasoningData; // Added reasoningData property
  FileUploadOcrResponse?
      fileData; // Added fileData property for file uploads (non-final to allow modification)
  Widget? customText;

  ChatMessage({
    required this.content,
    required this.isUser,
    DateTime? timestamp,
    this.customContent,
    this.reasoningData,
    this.fileData,
    this.customText,
  }) : timestamp = timestamp ?? DateTime.now();
}
