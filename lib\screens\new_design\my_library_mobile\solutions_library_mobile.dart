import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:nsl/l10n/app_localizations.dart';

import 'package:nsl/screens/new_design/my_library_mobile/create_book_mobile.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';
import 'package:nsl/widgets/mobile/library_navbar_mobile.dart';
import 'package:nsl/widgets/common/nsl_knowledge_loader.dart';
import 'package:nsl/providers/library_counts_provider.dart';

class SolutionMobile {
  final String title;
  final String subtitle;
  final String imageUrl;
  final bool isDraft;
  final double imageWidth;
  final double imageHeight;
  final String versionNumber;
  final DateTime lastUpdated;

  SolutionMobile({
    required this.title,
    required this.subtitle,
    required this.imageUrl,
    required this.lastUpdated,
    this.isDraft = false,
    this.imageWidth = 105.0,
    this.imageHeight = 140.0,
    this.versionNumber = "V00172",
  });

  factory SolutionMobile.fromJson(Map<String, dynamic> json) {
    return SolutionMobile(
      title: json['title'] as String,
      subtitle: json['subtitle'] as String,
      imageUrl: json['imageUrl'] as String,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      isDraft: json['isDraft'] as bool? ?? false,
      imageWidth: (json['imageWidth'] as num?)?.toDouble() ?? 105.0,
      imageHeight: (json['imageHeight'] as num?)?.toDouble() ?? 140.0,
      versionNumber: json['versionNumber'] as String? ?? "V00172",
    );
  }
}

class SolutionsLibraryMobile extends StatefulWidget {
  const SolutionsLibraryMobile({super.key, this.showNavigationBar = true});

  final bool showNavigationBar;

  @override
  State<SolutionsLibraryMobile> createState() => _SolutionsLibraryMobileState();
}

class _SolutionsLibraryMobileState extends State<SolutionsLibraryMobile>
    with TickerProviderStateMixin {
  // Constants
  static const double _solutionsPerViewNormal = 2.25;
  static const double _solutionsPerViewCompact = 3.0;
  static const double _solutionAspectRatio = 105.0 / 140.0;
  static const double _titleHeight = 32.0;
  static const double _subtitleHeight = 16.0;
  static const double _verticalSpacing = 12.0;
  static const double _solutionSpacing = 30.0;
  static const double _horizontalPadding = 24.0;
  static const int _recentSolutionsLimit = 10;

  // Text Styles
  static const TextStyle _sectionHeadingStyle = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: Colors.black,
    fontFamily: "TiemposText",
  );

  static const TextStyle _solutionTitleStyle = TextStyle(
    fontWeight: FontWeight.w500,
    fontSize: 12,
    height: 1.334,
    color: Colors.black,
    fontFamily: "TiemposText",
  );

  static const TextStyle _solutionSubtitleStyle = TextStyle(
    fontWeight: FontWeight.w400,
    fontSize: 11,
    color: Colors.black,
    fontFamily: "TiemposText",
  );

  static const TextStyle _emptyStateStyle = TextStyle(
    fontSize: 16,
    color: Colors.grey,
    fontFamily: "TiemposText",
  );

  // Data
  List<SolutionMobile> solutions = [];
  List<SolutionMobile> recentSolutions = [];
  List<SolutionMobile> allSolutions = [];
  List<SolutionMobile> filteredRecentSolutions = [];
  List<SolutionMobile> filteredAllSolutions = [];
  bool isLoading = true;

  // Navigation
  int selectedTabIndex = 1;

  // Controllers
  late CarouselController _recentSolutionsController;
  late CarouselController _allSolutionsController;
  final FocusNode _searchFocusNode = FocusNode();
  final TextEditingController _searchController = TextEditingController();
  late AnimationController _loadingAnimationController;

  // Animations
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // UI state
  bool _isKeyboardVisible = false;
  String _searchQuery = '';

  /// Get solutions per view based on keyboard visibility
  double _getSolutionsPerView() {
    return _isKeyboardVisible
        ? _solutionsPerViewCompact
        : _solutionsPerViewNormal;
  }

  // JSON string containing solution data with lastUpdated dates
  static const String solutionsJsonString = '''
{
  "solutions": [
    {
      "title": "Ecommerce Platform Solution",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-20T14:30:00Z"
    },
    {
      "title": "Fashion & Apparel Solution",
      "subtitle": "(B2C)",
      "versionNumber": "V00173",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-22T16:45:00Z"
    },
    {
      "title": "Financial Advisory Solution",
      "subtitle": "(B2C)",
      "versionNumber": "V00174",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-18T11:20:00Z"
    },
    {
      "title": "Home Rentals Solution",
      "subtitle": "(B2C)",
      "versionNumber": "V00175",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": true,
      "lastUpdated": "2024-12-25T09:15:00Z"
    },
    {
      "title": "Online Grocery Solution",
      "subtitle": "(B2C)",
      "versionNumber": "V00176",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-15T13:40:00Z"
    },
    {
      "title": "Courier & Logistics Solution",
      "subtitle": "(B2C)",
      "versionNumber": "V00177",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-21T17:25:00Z"
    },
    {
      "title": "Automotive Solution",
      "subtitle": "(B2C)",
      "versionNumber": "V00178",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": true,
      "lastUpdated": "2024-12-19T08:50:00Z"
    },
    {
      "title": "Fitness & Wellness Solution",
      "subtitle": "(B2C)",
      "versionNumber": "V00179",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-23T12:10:00Z"
    },
    {
      "title": "Real Estate Solution",
      "subtitle": "(B2C)",
      "versionNumber": "V00180",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-17T15:35:00Z"
    },
    {
      "title": "Restaurant & Cafe Solution",
      "subtitle": "(B2C)",
      "versionNumber": "V00181",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-24T10:20:00Z"
    }
  ]
}
''';

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeAnimations();
    _loadSolutions();
  }

  void _initializeControllers() {
    _recentSolutionsController = CarouselController();
    _allSolutionsController = CarouselController();
    _searchFocusNode.addListener(_onSearchFocusChange);
    _searchController.addListener(_onSearchChanged);
  }

  void _initializeAnimations() {
    _loadingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _loadingAnimationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _loadingAnimationController,
      curve: Curves.easeOutCubic,
    ));
  }

  void _onSearchFocusChange() {
    setState(() {});
  }

  void _onSearchChanged() {
    final query = _searchController.text.toLowerCase().trim();
    setState(() {
      _searchQuery = query;
      _filterSolutions();
    });
  }

  void _filterSolutions() {
    if (_searchQuery.isEmpty) {
      filteredRecentSolutions = recentSolutions;
      filteredAllSolutions = allSolutions;
    } else {
      filteredRecentSolutions = recentSolutions.where((solution) {
        return solution.title.toLowerCase().contains(_searchQuery) ||
               solution.subtitle.toLowerCase().contains(_searchQuery) ||
               solution.versionNumber.toLowerCase().contains(_searchQuery);
      }).toList();
      
      filteredAllSolutions = allSolutions.where((solution) {
        return solution.title.toLowerCase().contains(_searchQuery) ||
               solution.subtitle.toLowerCase().contains(_searchQuery) ||
               solution.versionNumber.toLowerCase().contains(_searchQuery);
      }).toList();
    }
  }

  void _performSearch() {
    final query = _searchController.text.toLowerCase().trim();
    setState(() {
      _searchQuery = query;
      _filterSolutions();
    });
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _searchQuery = '';
      _filterSolutions();
    });
  }

  void _loadSolutions() {
    try {
      final data = json.decode(solutionsJsonString);
      final loadedSolutions = (data['solutions'] as List<dynamic>)
          .map((solutionJson) =>
              SolutionMobile.fromJson(solutionJson as Map<String, dynamic>))
          .toList();

      final originalOrderSolutions = List<SolutionMobile>.from(loadedSolutions);
      loadedSolutions.sort((a, b) => b.lastUpdated.compareTo(a.lastUpdated));

      setState(() {
        solutions = originalOrderSolutions;
        recentSolutions = loadedSolutions
            .take(_recentSolutionsLimit)
            .toList();
        allSolutions = originalOrderSolutions;
        filteredRecentSolutions = recentSolutions;
        filteredAllSolutions = allSolutions;
        isLoading = false;
      });

      if (mounted) {
        Provider.of<LibraryCountsProvider>(context, listen: false)
            .updateSolutionsCount(solutions.length);
      }

      _loadingAnimationController.forward();
    } catch (e) {
      setState(() {
        solutions = <SolutionMobile>[];
        recentSolutions = <SolutionMobile>[];
        allSolutions = <SolutionMobile>[];
        isLoading = false;
      });
      debugPrint('Error loading solutions: $e');
    }
  }

  @override
  void dispose() {
    _recentSolutionsController.dispose();
    _allSolutionsController.dispose();
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _searchFocusNode.removeListener(_onSearchFocusChange);
    _searchFocusNode.dispose();
    _loadingAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _updateKeyboardVisibility();
    return _buildSolutionsLibraryView();
  }

  void _updateKeyboardVisibility() {
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = keyboardHeight > 0;

    if (_isKeyboardVisible != isKeyboardVisible) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            _isKeyboardVisible = isKeyboardVisible;
          });
        }
      });
    }
  }

  Widget _buildSolutionsLibraryView() {
    return Scaffold(
      backgroundColor: widget.showNavigationBar
          ? const Color(0xfff6f6f6)
          : Colors.transparent,
      drawer: widget.showNavigationBar ? const CustomDrawer() : null,
      appBar: widget.showNavigationBar ? _buildAppBar() : null,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.showNavigationBar) _buildTopNavigation(),
          if (widget.showNavigationBar) _buildSearchAndCreateSection(),
          _buildSolutionsContent(),
        ],
      ),
      floatingActionButton:
          widget.showNavigationBar ? _buildFloatingActionButton() : null,
    );
  }

  Widget _buildFloatingActionButton() {
    return SizedBox(
      width: 46,
      height: 46,
      child: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const CreateBookMobile(),
            ),
          );
        },
        backgroundColor: const Color(0xff0058FF),
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30),
        ),
        child: const Icon(Icons.add),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: const Color(0xfff6f6f6),
      surfaceTintColor: Colors.transparent,
      foregroundColor: Colors.black,
      elevation: 0,
      automaticallyImplyLeading: false,
      titleSpacing: 0,
      title: Row(
        children: [
          Builder(
            builder: (context) => IconButton(
              icon: const Icon(Icons.menu, color: Colors.black, size: 24),
              onPressed: () => Scaffold.of(context).openDrawer(),
              padding: const EdgeInsets.symmetric(horizontal: 16),
            ),
          ),
          Expanded(
            child: Text(
              AppLocalizations.of(context).translate('websolution.pageTitle'),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                fontFamily: 'TiemposText',
                color: Colors.black,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(width: 56),
        ],
      ),
    );
  }

  Widget _buildTopNavigation() {
    return Consumer<LibraryCountsProvider>(
      builder: (context, countsProvider, child) {
        return LibraryNavbarMobile(
          selectedTabIndex: selectedTabIndex,
          booksCount: countsProvider.booksCount,
          solutionsCount: countsProvider.solutionsCount,
          objectsCount: countsProvider.objectsCount,
          agentsCount: countsProvider.agentsCount,
        );
      },
    );
  }

  Widget _buildSolutionsContent() {
    return Expanded(
      child: NSLKnowledgeLoaderWrapper(
        isLoading: isLoading,
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16, 8, 0, 16),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionHeading("Recent Solutions"),
                const SizedBox(height: 12),
                SizedBox(
                  height: _calculateCarouselHeight(),
                  child: _buildRecentSolutionsCarousel(),
                ),
                const SizedBox(height: 24),
                _buildSectionHeading("All Solutions"),
                const SizedBox(height: 12),
                SizedBox(
                  height: _calculateCarouselHeight(),
                  child: _buildAllSolutionsCarousel(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeading(String title) {
    return Text(title, style: _sectionHeadingStyle);
  }

  Widget _buildRecentSolutionsCarousel() {
    final solutionsToShow = _searchQuery.isEmpty ? recentSolutions : filteredRecentSolutions;
    return _buildCarousel(
      solutions: solutionsToShow,
      controller: _recentSolutionsController,
      emptyMessage: _searchQuery.isEmpty ? 'No recent solutions found' : 'No recent solutions match your search',
    );
  }

  Widget _buildAllSolutionsCarousel() {
    final solutionsToShow = _searchQuery.isEmpty ? allSolutions : filteredAllSolutions;
    return _buildCarousel(
      solutions: solutionsToShow,
      controller: _allSolutionsController,
      emptyMessage: _searchQuery.isEmpty ? 'No solutions found' : 'No solutions match your search',
    );
  }

  Widget _buildCarousel({
    required List<SolutionMobile> solutions,
    required CarouselController controller,
    required String emptyMessage,
  }) {
    if (solutions.isEmpty) {
      return Center(
        child: Text(emptyMessage, style: _emptyStateStyle),
      );
    }

    final itemExtent = _calculateItemExtent();
    return CarouselView(
      padding: EdgeInsets.zero,
      backgroundColor: Colors.transparent,
      controller: controller,
      itemExtent: itemExtent,
      enableSplash: false,
      shape: const RoundedRectangleBorder(borderRadius: BorderRadius.zero),
      shrinkExtent: itemExtent,
      children: solutions.asMap().entries.map((entry) {
        return _buildSolutionItem(entry.value, entry.key);
      }).toList(),
    );
  }

  double _calculateItemExtent() {
    final screenWidth = MediaQuery.of(context).size.width;
    final solutionsPerView = _getSolutionsPerView();
    final availableWidth = screenWidth - (_horizontalPadding * 2);
    return availableWidth / solutionsPerView;
  }

  Widget _buildSolutionItem(SolutionMobile solution, int solutionIndex) {
    return GestureDetector(
      onTap: () => _navigateToSolutionDetails(solutionIndex),
      child: AnimatedBuilder(
        animation: _loadingAnimationController,
        builder: (context, child) => FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: _buildSolutionContent(solution),
          ),
        ),
      ),
    );
  }

  Widget _buildSolutionContent(SolutionMobile solution) {
    final solutionDimensions = _calculateSolutionDimensions(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSolutionCover(solution, solutionDimensions),
        const SizedBox(height: 8),
        _buildSolutionTitle(solution.title, solutionDimensions['width']!),
        const SizedBox(height: 4),
        _buildSolutionSubtitle(
            solution.versionNumber, solutionDimensions['width']!),
      ],
    );
  }

  Map<String, double> _calculateSolutionDimensions(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final solutionsPerView = _getSolutionsPerView();
    final availableWidth = screenWidth - (_horizontalPadding * 2);
    final itemExtent = availableWidth / solutionsPerView;

    double solutionWidth = itemExtent - _solutionSpacing;

    if (_isKeyboardVisible) {
      solutionWidth = solutionWidth.clamp(85.0, 140.0);
    } else {
      solutionWidth = solutionWidth.clamp(110.0, 170.0);
    }

    final solutionHeight = solutionWidth / _solutionAspectRatio;

    return {
      'width': solutionWidth,
      'height': solutionHeight,
      'spacing': _solutionSpacing,
    };
  }

  double _calculateCarouselHeight() {
    final solutionDimensions = _calculateSolutionDimensions(context);
    final solutionHeight = solutionDimensions['height']!;
    return solutionHeight +
        _verticalSpacing +
        _titleHeight +
        _subtitleHeight +
        6;
  }

  void _navigateToSolutionDetails(int solutionIndex) {
    debugPrint('Navigate to solution at index: $solutionIndex');
  }

  Widget _buildSearchAndCreateSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Container(
        height: 40,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: Row(
          children: [
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(left: 16.0),
                child: TextField(
                  controller: _searchController,
                  focusNode: _searchFocusNode,
                  decoration: InputDecoration(
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    hintText: 'Search solutions...',
                    border: InputBorder.none,
                    hintStyle: TextStyle(fontSize: 14, color: Colors.grey[500]),
                    isDense: true,
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ),
            ),
            _MobileSolutionSvgButton(
              iconPath: 'assets/images/search.svg',
              onPressed: _performSearch,
              size: 20,
            ),
            Container(
              height: 24,
              width: 1,
              color: Colors.grey.shade200,
              margin: const EdgeInsets.symmetric(horizontal: 4),
            ),
            _MobileSolutionSvgButton(
              iconPath: 'assets/images/filter-icon.svg',
              onPressed: () {},
              size: 24,
            ),
            const SizedBox(width: 8),
          ],
        ),
      ),
    );
  }

  Widget _buildSolutionCover(
      SolutionMobile solution, Map<String, double> dimensions) {
    final solutionWidth = dimensions['width']!;
    final solutionHeight = dimensions['height']!;

    return Stack(
      children: [
        Container(
          width: solutionWidth,
          height: solutionHeight,
          decoration: BoxDecoration(
            image: DecorationImage(
              image: AssetImage(solution.imageUrl),
              fit: BoxFit.fill,
            ),
          ),
        ),
        if (solution.isDraft)
          Positioned(
            top: solutionHeight * 0.10,
            right: solutionWidth * 0.14,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.amber,
                borderRadius: BorderRadius.circular(4),
              ),
              child: const Text(
                'Draft',
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                  fontFamily: "TiemposText",
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildSolutionTitle(String title, double solutionWidth) {
    return SizedBox(
      width: solutionWidth,
      child: Text(
        title,
        style: _solutionTitleStyle.copyWith(
      height: 1.2,
    ),
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildSolutionSubtitle(String versionNumber, double solutionWidth) {
    return SizedBox(
      width: solutionWidth,
      child: Text(
        versionNumber,
        style: _solutionSubtitleStyle,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }
}

class _MobileSolutionSvgButton extends StatefulWidget {
  final String iconPath;
  final VoidCallback onPressed;
  final double size;

  const _MobileSolutionSvgButton({
    required this.iconPath,
    required this.onPressed,
    this.size = 18,
  });

  @override
  State<_MobileSolutionSvgButton> createState() =>
      _MobileSolutionSvgButtonState();
}

class _MobileSolutionSvgButtonState extends State<_MobileSolutionSvgButton> {
  bool isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => isPressed = true),
      onTapUp: (_) => setState(() => isPressed = false),
      onTapCancel: () => setState(() => isPressed = false),
      onTap: widget.onPressed,
      child: Container(
        height: 32,
        width: 32,
        decoration: BoxDecoration(
          border: Border.all(
            color: isPressed ? const Color(0xff0058FF) : Colors.transparent,
            width: 1.0,
          ),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Center(
          child: SvgPicture.asset(
            widget.iconPath,
            width: widget.size,
            height: widget.size,
          ),
        ),
      ),
    );
  }
}
