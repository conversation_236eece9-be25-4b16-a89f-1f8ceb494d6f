import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';

/// A widget that provides video playback functionality.
///
/// This widget allows users to play videos from various sources (URL, asset, file).
/// It supports various configuration options such as autoplay, looping, custom controls,
/// playlists, and custom styling.
class VideoWidget extends StatefulWidget {
  /// URL of the video file to play
  final String? url;

  /// Asset path of the video file to play
  final String? assetPath;

  /// File path of the video file to play
  final String? filePath;

  /// List of video URLs for playlist functionality
  final List<String>? playlist;

  /// Whether to autoplay the video when the widget is loaded
  final bool autoplay;

  /// Whether to loop the video
  final bool loop;

  /// Whether to show video controls
  final bool showControls;

  /// Whether to show fullscreen button
  final bool showFullscreenButton;

  /// Whether to show playback speed controls
  final bool showPlaybackSpeedButton;

  /// Whether to show volume control
  final bool showVolumeButton;

  /// Whether to show progress bar
  final bool showProgressBar;

  /// Whether to show duration
  final bool showDuration;

  /// Whether to show title
  final bool showTitle;

  /// Title of the video
  final String? title;

  /// Subtitle or description of the video
  final String? subtitle;

  /// Initial volume (0.0 to 1.0)
  final double initialVolume;

  /// Theme color for controls
  final Color? themeColor;

  /// Background color
  final Color? backgroundColor;

  /// Text color
  final Color? textColor;

  /// Icon color
  final Color? iconColor;

  /// Progress bar color
  final Color? progressBarColor;

  /// Buffer bar color
  final Color? bufferBarColor;

  /// Custom play icon
  final IconData playIcon;

  /// Custom pause icon
  final IconData pauseIcon;

  /// Custom fullscreen icon
  final IconData fullscreenIcon;

  /// Custom exit fullscreen icon
  final IconData exitFullscreenIcon;

  /// Custom volume icon
  final IconData volumeIcon;

  /// Custom mute icon
  final IconData muteIcon;

  /// Whether to use a compact layout
  final bool compact;

  /// Whether to show a thumbnail before playing
  final bool showThumbnail;

  /// URL or asset path for the thumbnail image
  final String? thumbnailUrl;

  /// Whether to allow picture-in-picture mode
  final bool allowPip;

  /// Aspect ratio of the video (null for original aspect ratio)
  final double? aspectRatio;

  /// Initial playback position
  final Duration? startPosition;

  /// Whether to mute the video initially
  final bool initiallyMuted;

  /// Whether to show closed captions
  final bool showClosedCaptions;

  /// URL for closed captions file
  final String? closedCaptionsUrl;

  /// Whether to allow seeking by tapping on the progress bar
  final bool allowSeeking;

  /// Whether to show overlay controls on tap
  final bool showOverlayOnTap;

  /// Whether to hide controls after a period of inactivity
  final bool hideControlsOnInactivity;

  /// Duration after which controls should hide
  final Duration controlsHideDuration;

  /// Whether to show a replay button when video ends
  final bool showReplayButton;

  /// Whether to show a play/pause button in the center
  final bool showCenterPlayButton;

  /// Whether to show a loading indicator
  final bool showLoadingIndicator;

  /// Color of the loading indicator
  final Color? loadingIndicatorColor;

  /// Whether to show error messages
  final bool showErrorMessages;

  /// Custom error message
  final String? errorMessage;

  /// Callback when video starts playing
  final VoidCallback? onPlay;

  /// Callback when video is paused
  final VoidCallback? onPause;

  /// Callback when video ends
  final VoidCallback? onComplete;

  /// Callback when video is seeked
  final Function(Duration)? onSeek;

  /// Callback when volume changes
  final Function(double)? onVolumeChange;

  /// Callback when fullscreen mode changes
  final Function(bool)? onFullscreenChange;

  /// Callback when an error occurs
  final Function(String)? onError;

  /// Callback when video is loaded
  final VoidCallback? onLoaded;

  /// Callback when video buffer changes
  final Function(double)? onBufferChange;

  // Advanced Interaction Properties

  /// Callback for mouse hover
  ///
  /// This function is called when the mouse pointer enters or exits the widget.
  /// The parameter is true when the mouse enters and false when it exits.
  final void Function(bool)? onHover;

  /// Callback for keyboard focus
  ///
  /// This function is called when the widget gains or loses keyboard focus.
  /// The parameter is true when focus is gained and false when it is lost.
  final void Function(bool)? onFocus;

  /// Focus node for manual focus control
  ///
  /// This allows for programmatic control of the widget's focus state.
  /// If null, the widget will create and manage its own focus node.
  final FocusNode? focusNode;

  /// Whether the widget should be focused automatically when it appears
  ///
  /// If true, the widget will request focus when it is first built.
  final bool autofocus;

  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Creates a video widget.
  const VideoWidget({
    super.key,
    this.url,
    this.assetPath,
    this.filePath,
    this.playlist,
    this.autoplay = false,
    this.loop = false,
    this.showControls = true,
    this.showFullscreenButton = true,
    this.showPlaybackSpeedButton = false,
    this.showVolumeButton = true,
    this.showProgressBar = true,
    this.showDuration = true,
    this.showTitle = true,
    this.title,
    this.subtitle,
    this.initialVolume = 0.7,
    this.themeColor,
    this.backgroundColor,
    this.textColor,
    this.iconColor,
    this.progressBarColor,
    this.bufferBarColor,
    this.playIcon = Icons.play_arrow,
    this.pauseIcon = Icons.pause,
    this.fullscreenIcon = Icons.fullscreen,
    this.exitFullscreenIcon = Icons.fullscreen_exit,
    this.volumeIcon = Icons.volume_up,
    this.muteIcon = Icons.volume_off,
    this.compact = false,
    this.showThumbnail = false,
    this.thumbnailUrl,
    this.allowPip = false,
    this.aspectRatio,
    this.startPosition,
    this.initiallyMuted = false,
    this.showClosedCaptions = false,
    this.closedCaptionsUrl,
    this.allowSeeking = true,
    this.showOverlayOnTap = true,
    this.hideControlsOnInactivity = true,
    this.controlsHideDuration = const Duration(seconds: 3),
    this.showReplayButton = true,
    this.showCenterPlayButton = true,
    this.showLoadingIndicator = true,
    this.loadingIndicatorColor,
    this.showErrorMessages = true,
    this.errorMessage,
    this.onPlay,
    this.onPause,
    this.onComplete,
    this.onSeek,
    this.onVolumeChange,
    this.onFullscreenChange,
    this.onError,
    this.onLoaded,
    this.onBufferChange,
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
    this.semanticsLabel,
  });

  /// Creates a VideoWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the VideoWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "url": "https://example.com/video.mp4",
  ///   "autoplay": true,
  ///   "loop": false,
  ///   "showControls": true,
  ///   "title": "Sample Video",
  ///   "themeColor": "red"
  /// }
  /// ```
  factory VideoWidget.fromJson(Map<String, dynamic> json) {
    // Parse colors
    Color? parseColor(dynamic colorValue) {
      if (colorValue == null) return null;

      if (colorValue is String) {
        // Handle named colors
        switch (colorValue.toLowerCase()) {
          case 'red': return Colors.red;
          case 'blue': return Colors.blue;
          case 'green': return Colors.green;
          case 'yellow': return Colors.yellow;
          case 'orange': return Colors.orange;
          case 'purple': return Colors.purple;
          case 'pink': return Colors.pink;
          case 'brown': return Colors.brown;
          case 'grey':
          case 'gray': return Colors.grey;
          case 'black': return Colors.black;
          case 'white': return Colors.white;
          case 'transparent': return Colors.transparent;
          case 'teal': return Colors.teal;
          case 'cyan': return Colors.cyan;
          case 'amber': return Colors.amber;
          case 'indigo': return Colors.indigo;
          case 'lime': return Colors.lime;
          default:
            // Handle hex colors
            if (colorValue.startsWith('#')) {
              try {
                final hexColor = colorValue.substring(1);
                final hexValue = int.parse('0xFF${hexColor.padRight(8, 'F').substring(0, 8)}');
                return Color(hexValue);
              } catch (e) {
                return null;
              }
            }
            return null;
        }
      } else if (colorValue is int) {
        return Color(colorValue);
      }

      return null;
    }

    // Parse icon data
    IconData parseIconData(String? iconName, IconData defaultIcon) {
      if (iconName == null) return defaultIcon;

      switch (iconName.toLowerCase()) {
        case 'play': return Icons.play_arrow;
        case 'pause': return Icons.pause;
        case 'fullscreen': return Icons.fullscreen;
        case 'exit_fullscreen': return Icons.fullscreen_exit;
        case 'volume': return Icons.volume_up;
        case 'mute': return Icons.volume_off;
        case 'skip_next': return Icons.skip_next;
        case 'skip_previous': return Icons.skip_previous;
        case 'replay': return Icons.replay;
        case 'settings': return Icons.settings;
        case 'closed_caption': return Icons.closed_caption;
        case 'hd': return Icons.hd;
        case 'speed': return Icons.speed;
        case 'forward': return Icons.forward_10;
        case 'rewind': return Icons.replay_10;
        default: return defaultIcon;
      }
    }

    // Parse duration
    Duration? parseDuration(dynamic durationValue) {
      if (durationValue == null) return null;

      if (durationValue is int) {
        return Duration(seconds: durationValue);
      } else if (durationValue is String) {
        final seconds = int.tryParse(durationValue);
        if (seconds != null) {
          return Duration(seconds: seconds);
        }
      } else if (durationValue is Map<String, dynamic>) {
        final hours = durationValue['hours'] as int? ?? 0;
        final minutes = durationValue['minutes'] as int? ?? 0;
        final seconds = durationValue['seconds'] as int? ?? 0;
        final milliseconds = durationValue['milliseconds'] as int? ?? 0;

        return Duration(
          hours: hours,
          minutes: minutes,
          seconds: seconds,
          milliseconds: milliseconds,
        );
      }

      return null;
    }

    // Parse playlist
    List<String>? parsePlaylist(dynamic playlistValue) {
      if (playlistValue == null) return null;

      if (playlistValue is List) {
        return playlistValue.map((item) => item.toString()).toList();
      } else if (playlistValue is String) {
        return playlistValue.split(',').map((item) => item.trim()).toList();
      }

      return null;
    }

    return VideoWidget(
      url: json['url'] as String?,
      assetPath: json['assetPath'] as String?,
      filePath: json['filePath'] as String?,
      playlist: parsePlaylist(json['playlist']),
      autoplay: json['autoplay'] as bool? ?? false,
      loop: json['loop'] as bool? ?? false,
      showControls: json['showControls'] as bool? ?? true,
      showFullscreenButton: json['showFullscreenButton'] as bool? ?? true,
      showPlaybackSpeedButton: json['showPlaybackSpeedButton'] as bool? ?? false,
      showVolumeButton: json['showVolumeButton'] as bool? ?? true,
      showProgressBar: json['showProgressBar'] as bool? ?? true,
      showDuration: json['showDuration'] as bool? ?? true,
      showTitle: json['showTitle'] as bool? ?? true,
      title: json['title'] as String?,
      subtitle: json['subtitle'] as String?,
      initialVolume: (json['initialVolume'] as num?)?.toDouble() ?? 0.7,
      themeColor: parseColor(json['themeColor']),
      backgroundColor: parseColor(json['backgroundColor']),
      textColor: parseColor(json['textColor']),
      iconColor: parseColor(json['iconColor']),
      progressBarColor: parseColor(json['progressBarColor']),
      bufferBarColor: parseColor(json['bufferBarColor']),
      playIcon: parseIconData(json['playIcon'] as String?, Icons.play_arrow),
      pauseIcon: parseIconData(json['pauseIcon'] as String?, Icons.pause),
      fullscreenIcon: parseIconData(json['fullscreenIcon'] as String?, Icons.fullscreen),
      exitFullscreenIcon: parseIconData(json['exitFullscreenIcon'] as String?, Icons.fullscreen_exit),
      volumeIcon: parseIconData(json['volumeIcon'] as String?, Icons.volume_up),
      muteIcon: parseIconData(json['muteIcon'] as String?, Icons.volume_off),
      compact: json['compact'] as bool? ?? false,
      showThumbnail: json['showThumbnail'] as bool? ?? false,
      thumbnailUrl: json['thumbnailUrl'] as String?,
      allowPip: json['allowPip'] as bool? ?? false,
      aspectRatio: (json['aspectRatio'] as num?)?.toDouble(),
      startPosition: parseDuration(json['startPosition']),
      initiallyMuted: json['initiallyMuted'] as bool? ?? false,
      showClosedCaptions: json['showClosedCaptions'] as bool? ?? false,
      closedCaptionsUrl: json['closedCaptionsUrl'] as String?,
      allowSeeking: json['allowSeeking'] as bool? ?? true,
      showOverlayOnTap: json['showOverlayOnTap'] as bool? ?? true,
      hideControlsOnInactivity: json['hideControlsOnInactivity'] as bool? ?? true,
      controlsHideDuration: parseDuration(json['controlsHideDuration']) ?? const Duration(seconds: 3),
      showReplayButton: json['showReplayButton'] as bool? ?? true,
      showCenterPlayButton: json['showCenterPlayButton'] as bool? ?? true,
      showLoadingIndicator: json['showLoadingIndicator'] as bool? ?? true,
      loadingIndicatorColor: parseColor(json['loadingIndicatorColor']),
      showErrorMessages: json['showErrorMessages'] as bool? ?? true,
      errorMessage: json['errorMessage'] as String?,
      onPlay: json['onPlay'] == true ? () {
        debugPrint('Video started playing');
      } : null,
      onPause: json['onPause'] == true ? () {
        debugPrint('Video paused');
      } : null,
      onComplete: json['onComplete'] == true ? () {
        debugPrint('Video playback completed');
      } : null,
      onSeek: json['onSeek'] == true ? (position) {
        debugPrint('Video seeked to $position');
      } : null,
      onVolumeChange: json['onVolumeChange'] == true ? (volume) {
        debugPrint('Video volume changed to $volume');
      } : null,
      onFullscreenChange: json['onFullscreenChange'] == true ? (isFullscreen) {
        debugPrint('Video fullscreen changed to $isFullscreen');
      } : null,
      onError: json['onError'] == true ? (error) {
        debugPrint('Video error: $error');
      } : null,
      onLoaded: json['onLoaded'] == true ? () {
        debugPrint('Video loaded');
      } : null,
      onBufferChange: json['onBufferChange'] == true ? (bufferPercent) {
        debugPrint('Video buffer changed to $bufferPercent');
      } : null,
      onHover: json['onHover'] == true ? (isHovered) {
        debugPrint('Video hover changed to $isHovered');
      } : null,
      onFocus: json['onFocus'] == true ? (isFocused) {
        debugPrint('Video focus changed to $isFocused');
      } : null,
      focusNode: null, // Cannot be created from JSON
      autofocus: json['autofocus'] as bool? ?? false,
      semanticsLabel: json['semanticsLabel'] as String?,
    );
  }

  /// Converts the VideoWidget to a JSON map
  ///
  /// This method allows for serializing the widget configuration to JSON,
  /// which can be useful for saving configurations or sharing them.
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {
      'autoplay': autoplay,
      'loop': loop,
      'showControls': showControls,
      'showFullscreenButton': showFullscreenButton,
      'showPlaybackSpeedButton': showPlaybackSpeedButton,
      'showVolumeButton': showVolumeButton,
      'showProgressBar': showProgressBar,
      'showDuration': showDuration,
      'showTitle': showTitle,
      'initialVolume': initialVolume,
      'compact': compact,
      'showThumbnail': showThumbnail,
      'allowPip': allowPip,
      'initiallyMuted': initiallyMuted,
      'showClosedCaptions': showClosedCaptions,
      'allowSeeking': allowSeeking,
      'showOverlayOnTap': showOverlayOnTap,
      'hideControlsOnInactivity': hideControlsOnInactivity,
      'showReplayButton': showReplayButton,
      'showCenterPlayButton': showCenterPlayButton,
      'showLoadingIndicator': showLoadingIndicator,
      'showErrorMessages': showErrorMessages,
      'autofocus': autofocus,
    };

    // Add optional properties
    if (url != null) json['url'] = url;
    if (assetPath != null) json['assetPath'] = assetPath;
    if (filePath != null) json['filePath'] = filePath;
    if (playlist != null) json['playlist'] = playlist;
    if (title != null) json['title'] = title;
    if (subtitle != null) json['subtitle'] = subtitle;
    if (thumbnailUrl != null) json['thumbnailUrl'] = thumbnailUrl;
    if (aspectRatio != null) json['aspectRatio'] = aspectRatio;
    if (closedCaptionsUrl != null) json['closedCaptionsUrl'] = closedCaptionsUrl;
    if (errorMessage != null) json['errorMessage'] = errorMessage;
    if (semanticsLabel != null) json['semanticsLabel'] = semanticsLabel;

    // Add colors
    if (themeColor != null) json['themeColor'] = _colorToString(themeColor!);
    if (backgroundColor != null) json['backgroundColor'] = _colorToString(backgroundColor!);
    if (textColor != null) json['textColor'] = _colorToString(textColor!);
    if (iconColor != null) json['iconColor'] = _colorToString(iconColor!);
    if (progressBarColor != null) json['progressBarColor'] = _colorToString(progressBarColor!);
    if (bufferBarColor != null) json['bufferBarColor'] = _colorToString(bufferBarColor!);
    if (loadingIndicatorColor != null) json['loadingIndicatorColor'] = _colorToString(loadingIndicatorColor!);

    // Add icons
    if (playIcon != Icons.play_arrow) json['playIcon'] = _iconDataToString(playIcon);
    if (pauseIcon != Icons.pause) json['pauseIcon'] = _iconDataToString(pauseIcon);
    if (fullscreenIcon != Icons.fullscreen) json['fullscreenIcon'] = _iconDataToString(fullscreenIcon);
    if (exitFullscreenIcon != Icons.fullscreen_exit) json['exitFullscreenIcon'] = _iconDataToString(exitFullscreenIcon);
    if (volumeIcon != Icons.volume_up) json['volumeIcon'] = _iconDataToString(volumeIcon);
    if (muteIcon != Icons.volume_off) json['muteIcon'] = _iconDataToString(muteIcon);

    // Add durations
    if (startPosition != null) {
      json['startPosition'] = {
        'hours': startPosition!.inHours,
        'minutes': startPosition!.inMinutes.remainder(60),
        'seconds': startPosition!.inSeconds.remainder(60),
        'milliseconds': startPosition!.inMilliseconds.remainder(1000),
      };
    }

    if (controlsHideDuration != const Duration(seconds: 3)) {
      json['controlsHideDuration'] = {
        'seconds': controlsHideDuration.inSeconds,
      };
    }

    // Add callback flags
    if (onPlay != null) json['onPlay'] = true;
    if (onPause != null) json['onPause'] = true;
    if (onComplete != null) json['onComplete'] = true;
    if (onSeek != null) json['onSeek'] = true;
    if (onVolumeChange != null) json['onVolumeChange'] = true;
    if (onFullscreenChange != null) json['onFullscreenChange'] = true;
    if (onError != null) json['onError'] = true;
    if (onLoaded != null) json['onLoaded'] = true;
    if (onBufferChange != null) json['onBufferChange'] = true;
    if (onHover != null) json['onHover'] = true;
    if (onFocus != null) json['onFocus'] = true;

    return json;
  }

  /// Helper method to convert a Color to a string
  static String _colorToString(Color color) {
    // Handle standard colors by name for better readability
    if (color == Colors.red) return 'red';
    if (color == Colors.blue) return 'blue';
    if (color == Colors.green) return 'green';
    if (color == Colors.yellow) return 'yellow';
    if (color == Colors.orange) return 'orange';
    if (color == Colors.purple) return 'purple';
    if (color == Colors.pink) return 'pink';
    if (color == Colors.brown) return 'brown';
    if (color == Colors.grey) return 'grey';
    if (color == Colors.black) return 'black';
    if (color == Colors.white) return 'white';
    if (color == Colors.transparent) return 'transparent';
    if (color == Colors.teal) return 'teal';
    if (color == Colors.cyan) return 'cyan';
    if (color == Colors.amber) return 'amber';
    if (color == Colors.indigo) return 'indigo';
    if (color == Colors.lime) return 'lime';

    // Convert to hex string
    final int colorValue = color.toARGB32();
    final r = ((colorValue >> 16) & 0xFF).toRadixString(16).padLeft(2, '0');
    final g = ((colorValue >> 8) & 0xFF).toRadixString(16).padLeft(2, '0');
    final b = (colorValue & 0xFF).toRadixString(16).padLeft(2, '0');
    return '#$r$g$b';
  }

  /// Helper method to convert an IconData to a string
  static String _iconDataToString(IconData icon) {
    if (icon == Icons.play_arrow) return 'play';
    if (icon == Icons.pause) return 'pause';
    if (icon == Icons.fullscreen) return 'fullscreen';
    if (icon == Icons.fullscreen_exit) return 'exit_fullscreen';
    if (icon == Icons.volume_up) return 'volume';
    if (icon == Icons.volume_off) return 'mute';
    if (icon == Icons.skip_next) return 'skip_next';
    if (icon == Icons.skip_previous) return 'skip_previous';
    if (icon == Icons.replay) return 'replay';
    if (icon == Icons.settings) return 'settings';
    if (icon == Icons.closed_caption) return 'closed_caption';
    if (icon == Icons.hd) return 'hd';
    if (icon == Icons.speed) return 'speed';
    if (icon == Icons.forward_10) return 'forward';
    if (icon == Icons.replay_10) return 'rewind';

    return 'custom';
  }

  @override
  State<VideoWidget> createState() => _VideoWidgetState();
}

class _VideoWidgetState extends State<VideoWidget> {
  VideoPlayerController? _videoPlayerController;
  ChewieController? _chewieController;
  bool _isPlaying = false;
  bool _isLoading = true;
  bool _isMuted = false;
  double _volume = 0.7;
  String? _errorMessage;
  int _currentVideoIndex = 0;
  List<String> _playlist = [];
  bool _isShowingThumbnail = false;
  Timer? _hideControlsTimer;
  bool _isHovered = false;
  bool _isFocused = false;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _volume = widget.initialVolume;
    _isMuted = widget.initiallyMuted;
    _isShowingThumbnail = widget.showThumbnail;

    // Initialize focus node
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_handleFocusChange);

    // Request focus if autofocus is enabled
    if (widget.autofocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }

    // Initialize playlist if provided
    if (widget.playlist != null && widget.playlist!.isNotEmpty) {
      _playlist = List.from(widget.playlist!);
      _initializeVideo(_playlist[_currentVideoIndex]);
    } else {
      // Initialize with single video source
      String? videoSource;
      if (widget.url != null) {
        videoSource = widget.url;
      } else if (widget.assetPath != null) {
        videoSource = widget.assetPath;
      } else if (widget.filePath != null) {
        videoSource = widget.filePath;
      }

      if (videoSource != null) {
        _initializeVideo(videoSource);
      } else {
        setState(() {
          _errorMessage = 'No video source provided';
          _isLoading = false;
        });
      }
    }
  }

  // Handle focus changes
  void _handleFocusChange() {
    if (_focusNode.hasFocus != _isFocused) {
      setState(() {
        _isFocused = _focusNode.hasFocus;
      });

      if (widget.onFocus != null) {
        widget.onFocus!(_isFocused);
      }
    }
  }

  // Handle hover changes
  void _handleHoverChange(bool isHovered) {
    if (_isHovered != isHovered) {
      setState(() {
        _isHovered = isHovered;
      });

      if (widget.onHover != null) {
        widget.onHover!(isHovered);
      }
    }
  }

  @override
  void dispose() {
    _hideControlsTimer?.cancel();
    _videoPlayerController?.dispose();
    _chewieController?.dispose();

    // Clean up focus node
    if (widget.focusNode == null) {
      // Only dispose the focus node if we created it
      _focusNode.removeListener(_handleFocusChange);
      _focusNode.dispose();
    }

    super.dispose();
  }

  void _initializeVideo(String source) {
    // Dispose previous controllers if they exist
    _videoPlayerController?.dispose();
    _chewieController?.dispose();

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Create the appropriate VideoPlayerController based on the source type
      if (source.startsWith('http://') || source.startsWith('https://')) {
        _videoPlayerController = VideoPlayerController.networkUrl(Uri.parse(source));
      } else if (source.startsWith('asset:')) {
        _videoPlayerController = VideoPlayerController.asset(source.replaceFirst('asset:', ''));
      } else {
        _videoPlayerController = VideoPlayerController.file(File(source));
      }

      // Initialize the video player
      _videoPlayerController!.initialize().then((_) {
        // Create the Chewie controller once video is initialized
        _createChewieController();

        setState(() {
          _isLoading = false;
        });

        // Set up listeners
        _setupListeners();

        // Autoplay if enabled
        if (widget.autoplay) {
          _play();
        }

        // Call onLoaded callback if provided
        if (widget.onLoaded != null) {
          widget.onLoaded!();
        }
      }).catchError((error) {
        setState(() {
          _errorMessage = 'Failed to initialize video: $error';
          _isLoading = false;
        });

        if (widget.onError != null) {
          widget.onError!('Failed to initialize video: $error');
        }
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading video: $e';
        _isLoading = false;
      });

      if (widget.onError != null) {
        widget.onError!('Error loading video: $e');
      }
    }
  }

  void _createChewieController() {
    if (_videoPlayerController == null || !_videoPlayerController!.value.isInitialized) {
      return;
    }

    // Set initial volume
    _videoPlayerController!.setVolume(_isMuted ? 0 : _volume);

    // Create Chewie controller with customized options
    _chewieController = ChewieController(
      videoPlayerController: _videoPlayerController!,
      autoPlay: widget.autoplay,
      looping: widget.loop,
      showControls: widget.showControls,
      showOptions: widget.showPlaybackSpeedButton, // For playback speed menu
      aspectRatio: widget.aspectRatio ?? _videoPlayerController!.value.aspectRatio,
      startAt: widget.startPosition,
      allowFullScreen: widget.showFullscreenButton,
      allowMuting: widget.showVolumeButton,
      showControlsOnInitialize: false,
      hideControlsTimer: widget.controlsHideDuration,
      customControls: widget.showControls ? null : const SizedBox.shrink(),
      errorBuilder: (context, errorMessage) {
        return _buildErrorMessage(errorMessage);
      },
      placeholder: widget.showThumbnail && widget.thumbnailUrl != null
          ? _buildThumbnail()
          : const Center(child: CircularProgressIndicator()),
      // Progress colors are handled by Chewie internally
    );

    // Set up position listener for seeking callback
    if (widget.onSeek != null) {
      _videoPlayerController!.addListener(() {
        if (_videoPlayerController!.value.position != Duration.zero) {
          widget.onSeek!(_videoPlayerController!.value.position);
        }
      });
    }
  }

  void _setupListeners() {
    if (_videoPlayerController == null) return;

    // Listen for playback state changes
    _videoPlayerController!.addListener(() {
      final isPlaying = _videoPlayerController!.value.isPlaying;

      if (isPlaying != _isPlaying) {
        setState(() {
          _isPlaying = isPlaying;
        });

        if (isPlaying) {
          if (widget.onPlay != null) {
            widget.onPlay!();
          }
        } else {
          if (widget.onPause != null) {
            widget.onPause!();
          }
        }
      }

      // Check if video has completed
      if (_videoPlayerController!.value.position >= _videoPlayerController!.value.duration) {
        if (widget.onComplete != null) {
          widget.onComplete!();
        }

        // If we have a playlist and not looping, go to next video
        if (widget.playlist != null &&
            widget.playlist!.length > 1 &&
            _currentVideoIndex < widget.playlist!.length - 1 &&
            !widget.loop) {
          _playNextVideo();
        }
      }
    });
  }

  void _play() {
    if (_videoPlayerController == null) return;

    if (_isShowingThumbnail) {
      setState(() {
        _isShowingThumbnail = false;
      });
    }

    _videoPlayerController!.play();
    setState(() {
      _isPlaying = true;
    });

    if (widget.onPlay != null) {
      widget.onPlay!();
    }
  }

  // This method is used internally by the player

  void _playNextVideo() {
    if (_playlist.isEmpty || _currentVideoIndex >= _playlist.length - 1) return;

    setState(() {
      _currentVideoIndex++;
    });

    _initializeVideo(_playlist[_currentVideoIndex]);
  }

  void _showControls() {
    if (!widget.showControls) return;

    // Reset the timer
    _hideControlsTimer?.cancel();
    if (widget.hideControlsOnInactivity) {
      _hideControlsTimer = Timer(widget.controlsHideDuration, () {
        if (mounted && _isPlaying) {
          setState(() {
            // Controls will be hidden automatically by Chewie
          });
        }
      });
    }
  }

  Widget _buildThumbnail() {
    if (widget.thumbnailUrl == null) {
      return Container(
        color: Colors.black,
        child: Center(
          child: Icon(
            Icons.video_library,
            size: 80,
            color: Colors.white.withAlpha(128),
          ),
        ),
      );
    }

    Widget thumbnailWidget;
    if (widget.thumbnailUrl!.startsWith('http://') || widget.thumbnailUrl!.startsWith('https://')) {
      thumbnailWidget = Image.network(
        widget.thumbnailUrl!,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            color: Colors.black,
            child: Center(
              child: Icon(
                Icons.broken_image,
                size: 80,
                color: Colors.white.withAlpha(128),
              ),
            ),
          );
        },
      );
    } else if (widget.thumbnailUrl!.startsWith('asset:')) {
      thumbnailWidget = Image.asset(
        widget.thumbnailUrl!.replaceFirst('asset:', ''),
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            color: Colors.black,
            child: Center(
              child: Icon(
                Icons.broken_image,
                size: 80,
                color: Colors.white.withAlpha(128),
              ),
            ),
          );
        },
      );
    } else {
      thumbnailWidget = Image.file(
        File(widget.thumbnailUrl!),
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            color: Colors.black,
            child: Center(
              child: Icon(
                Icons.broken_image,
                size: 80,
                color: Colors.white.withAlpha(128),
              ),
            ),
          );
        },
      );
    }

    return Stack(
      alignment: Alignment.center,
      children: [
        thumbnailWidget,
        Container(
          color: Colors.black.withAlpha(77), // 0.3 opacity
        ),
        IconButton(
          icon: Icon(
            widget.playIcon,
            size: 60,
            color: Colors.white,
          ),
          onPressed: () {
            setState(() {
              _isShowingThumbnail = false;
            });
            _play();
          },
        ),
      ],
    );
  }

  Widget _buildErrorMessage([String? message]) {
    final errorText = message ?? widget.errorMessage ?? _errorMessage ?? 'An error occurred';

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 60,
          ),
          const SizedBox(height: 16),
          Text(
            errorText,
            style: TextStyle(
              color: widget.textColor ?? Colors.white,
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              if (_playlist.isNotEmpty) {
                _initializeVideo(_playlist[_currentVideoIndex]);
              } else if (widget.url != null) {
                _initializeVideo(widget.url!);
              } else if (widget.assetPath != null) {
                _initializeVideo(widget.assetPath!);
              } else if (widget.filePath != null) {
                _initializeVideo(widget.filePath!);
              }
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return Center(
      child: CircularProgressIndicator(
        color: widget.loadingIndicatorColor ?? widget.themeColor ?? Colors.red,
      ),
    );
  }

  Widget _buildCompactView() {
    if (_errorMessage != null && widget.showErrorMessages) {
      return _buildErrorMessage();
    }

    if (_isLoading && widget.showLoadingIndicator) {
      return _buildLoadingIndicator();
    }

    if (_isShowingThumbnail) {
      return _buildThumbnail();
    }

    if (_chewieController != null && _videoPlayerController != null) {
      // Use VideoPlayer widget directly
      return AspectRatio(
        aspectRatio: widget.aspectRatio ?? _videoPlayerController!.value.aspectRatio,
        child: VideoPlayer(_videoPlayerController!),
      );
    }

    return _buildLoadingIndicator();
  }

  Widget _buildFullView() {
    if (_errorMessage != null && widget.showErrorMessages) {
      return _buildErrorMessage();
    }

    if (_isLoading && widget.showLoadingIndicator) {
      return _buildLoadingIndicator();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title and subtitle
        if (widget.showTitle && (widget.title != null || widget.subtitle != null))
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (widget.title != null)
                  Text(
                    widget.title!,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: widget.textColor ?? Colors.black,
                    ),
                  ),
                if (widget.subtitle != null)
                  Text(
                    widget.subtitle!,
                    style: TextStyle(
                      fontSize: 14,
                      color: widget.textColor != null
                          ? widget.textColor!.withAlpha(179) // 0.7 opacity
                          : Colors.grey.shade600,
                    ),
                  ),
              ],
            ),
          ),

        // Video player
        Expanded(
          child: _isShowingThumbnail
              ? _buildThumbnail()
              : (_chewieController != null && _videoPlayerController != null
                  ? AspectRatio(
                      aspectRatio: widget.aspectRatio ?? _videoPlayerController!.value.aspectRatio,
                      child: VideoPlayer(_videoPlayerController!),
                    )
                  : _buildLoadingIndicator()),
        ),

        // Playlist if available
        if (widget.playlist != null && widget.playlist!.length > 1)
          _buildPlaylist(),
      ],
    );
  }

  Widget _buildPlaylist() {
    return Container(
      height: 120,
      margin: const EdgeInsets.only(top: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Playlist',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: widget.textColor ?? Colors.black,
            ),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _playlist.length,
              itemBuilder: (context, index) {
                final isCurrentVideo = index == _currentVideoIndex;
                final videoName = _getVideoName(_playlist[index]);

                return Container(
                  width: 160,
                  margin: const EdgeInsets.only(right: 8),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: isCurrentVideo
                          ? (widget.themeColor ?? Colors.red)
                          : Colors.grey.shade300,
                      width: isCurrentVideo ? 2 : 1,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: InkWell(
                    onTap: () {
                      setState(() {
                        _currentVideoIndex = index;
                      });
                      _initializeVideo(_playlist[index]);
                    },
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        // Thumbnail or placeholder
                        Container(
                          color: Colors.black,
                          child: Center(
                            child: Icon(
                              Icons.video_library,
                              size: 40,
                              color: Colors.white.withAlpha(128),
                            ),
                          ),
                        ),
                        // Video name
                        Positioned(
                          bottom: 0,
                          left: 0,
                          right: 0,
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            color: Colors.black.withAlpha(150),
                            child: Text(
                              videoName,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                        // Current video indicator
                        if (isCurrentVideo)
                          Positioned(
                            top: 4,
                            right: 4,
                            child: Container(
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: widget.themeColor ?? Colors.red,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.play_arrow,
                                color: Colors.white,
                                size: 12,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  String _getVideoName(String path) {
    // Extract filename from path
    String fileName = path;

    if (path.contains('/')) {
      fileName = path.split('/').last;
    }

    if (fileName.contains('?')) {
      fileName = fileName.split('?').first;
    }

    // Remove extension
    if (fileName.contains('.')) {
      fileName = fileName.split('.').first;
    }

    // Replace underscores and hyphens with spaces
    fileName = fileName.replaceAll('_', ' ').replaceAll('-', ' ');

    // Capitalize first letter of each word
    fileName = fileName.split(' ').map((word) {
      if (word.isNotEmpty) {
        return word[0].toUpperCase() + word.substring(1);
      }
      return word;
    }).join(' ');

    return fileName;
  }

  @override
  Widget build(BuildContext context) {
    // Apply theme color if provided
    final effectiveBackgroundColor = widget.backgroundColor ?? Colors.white;

    // Create the base widget with gesture detection and focus handling
    Widget videoWidget = MouseRegion(
      onEnter: (_) => _handleHoverChange(true),
      onExit: (_) => _handleHoverChange(false),
      child: GestureDetector(
        onTap: _showControls,
        child: Focus(
          focusNode: _focusNode,
          child: Container(
            color: effectiveBackgroundColor,
            child: widget.compact ? _buildCompactView() : _buildFullView(),
          ),
        ),
      ),
    );

    // Add semantics if needed
    if (widget.semanticsLabel != null) {
      videoWidget = Semantics(
        label: widget.semanticsLabel,
        child: videoWidget,
      );
    }

    return videoWidget;
  }
}