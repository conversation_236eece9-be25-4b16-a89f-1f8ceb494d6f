import 'package:flutter/material.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:nsl/models/entities_data.dart' as entities_model;
import 'package:nsl/models/conversation_response.dart';
import '../comments_bottom_sheet.dart';

/// Constants for consistent styling across the entity details panel
class _EntityDetailsPanelMobileConstants {
  // Layout constants
  static const double borderRadius = 20.0;
  static const double headerPadding = 16.0;
  static const double contentPadding = 16.0;
  static const double sectionVerticalPadding = 16.0;
  static const double bottomSpacerHeight = 200.0;

  // Avatar constants
  static const double avatarRadius = 12.0;
  static const double avatarIconSize = 18.0;

  // Navigation chip constants
  static const double chipHorizontalPadding = 12.0;
  static const double chipVerticalPadding = 6.0;
  static const double chipBorderRadius = 16.0;
  static const double chipSpacing = 8.0;

  // Card constants
  static const double cardBorderRadius = 8.0;
  static const double cardPadding = 12.0;

  // Spacing constants
  static const double spacingSmall = 8.0;
  static const double spacingMedium = 12.0;

  // Text constants
  static const String fontFamily = "TiemposText";
  static const double titleFontSize = 16.0;
  static const double sectionTitleFontSize = 16.0;
  static const double contentFontSize = 14.0;
  static const double chipFontSize = 12.0;

  // Colors
  static const Color avatarBackgroundColor = Color(0xffE6F7FF);
  static const Color avatarIconColor = Color(0xff1890FF);

  // Animation constants
  static const Duration scrollAnimationDuration = Duration(milliseconds: 300);
  static const Duration postFrameDelay = Duration(milliseconds: 50);
}

/// A mobile-optimized entity details panel widget that displays detailed information about a selected entity.
///
/// This widget shows entity information including attributes, business rules, relationships,
/// and validations in a structured format optimized for mobile screens with touch-friendly interactions.
class EntityDetailsPanelMobile extends StatefulWidget {
  /// The entity information to display
  final entities_model.Entity entity;

  /// Callback when the close button is pressed
  final VoidCallback? onClose;

  /// Chat controller for interactive elements
  final TextEditingController? chatController;

  /// Callback when a message is sent
  final VoidCallback? onSendMessage;

  /// Global entity elements for additional data
  /// Currently unused but kept for future functionality
  // ignore: unused_field
  final Map<String, EntityElement> globalEntityElements;

  const EntityDetailsPanelMobile({
    super.key,
    required this.entity,
    this.onClose,
    this.chatController,
    this.onSendMessage,
    this.globalEntityElements = const {},
  });

  @override
  State<EntityDetailsPanelMobile> createState() =>
      _EntityDetailsPanelMobileState();
}

class _EntityDetailsPanelMobileState extends State<EntityDetailsPanelMobile> {
  /// Track active section for navigation styling
  String? _activeSectionId;

  /// Global keys for sections - persists across builds
  late Map<String, GlobalKey> _sectionKeys;

  /// ScrollController for the content area
  late ScrollController _scrollController;

  /// Map to store visibility information for each section
  final Map<String, VisibilityInfo> _sectionVisibilityInfo = {};

  @override
  void initState() {
    super.initState();
    _initializeScrollController();
    _initializeSectionKeys();
    _initializeActiveSection();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  /// Initialize the scroll controller and add listener
  void _initializeScrollController() {
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  /// Initialize section keys for navigation
  void _initializeSectionKeys() {
    _sectionKeys = {
      'attributes': GlobalKey(),
      'business_rules': GlobalKey(),
      'relationships': GlobalKey(),
      'constants_validations': GlobalKey(),
    };
  }

  /// Initialize the first section as active after frame is built
  void _initializeActiveSection() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _activeSectionId = 'attributes';
        });
      }
    });
  }

  /// Handle scroll events for active section detection
  void _onScroll() {
    // Handle scroll events if needed for future enhancements
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: _buildContainerDecoration(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          _buildDescriptionSection(),
          _buildNavigationChips(),
          _buildContentArea(),
        ],
      ),
    );
  }

  /// Builds the main container decoration
  BoxDecoration _buildContainerDecoration() {
    return BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.vertical(
        top: Radius.circular(_EntityDetailsPanelMobileConstants.borderRadius),
      ),
    );
  }

  /// Builds the header section with avatar, title, and action buttons
  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(_EntityDetailsPanelMobileConstants.headerPadding),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: Row(
        children: [
          _buildAvatar(),
          SizedBox(width: _EntityDetailsPanelMobileConstants.spacingMedium),
          _buildTitle(),
          _buildChatButton(),
          _buildCloseButton(),
        ],
      ),
    );
  }

  /// Builds the entity avatar
  Widget _buildAvatar() {
    return CircleAvatar(
      backgroundColor: _EntityDetailsPanelMobileConstants.avatarBackgroundColor,
      radius: _EntityDetailsPanelMobileConstants.avatarRadius,
      child: Icon(
        Icons.person_outline,
        color: _EntityDetailsPanelMobileConstants.avatarIconColor,
        size: _EntityDetailsPanelMobileConstants.avatarIconSize,
      ),
    );
  }

  /// Builds the entity title
  Widget _buildTitle() {
    return Expanded(
      child: Text(
        widget.entity.title ?? 'Entity Details',
        style: TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: _EntityDetailsPanelMobileConstants.titleFontSize,
          fontFamily: _EntityDetailsPanelMobileConstants.fontFamily,
        ),
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  /// Builds the chat button
  Widget _buildChatButton() {
    return IconButton(
      icon: Icon(Icons.chat, color: Colors.black, size: 20),
      onPressed: () => _showChatBottomSheet(context),
    );
  }

  /// Builds the close button
  Widget _buildCloseButton() {
    return IconButton(
      icon: Icon(Icons.close, color: Colors.black, size: 20),
      onPressed: widget.onClose,
    );
  }

  /// Builds the description section if description exists
  Widget _buildDescriptionSection() {
    if (widget.entity.description?.isNotEmpty != true) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      padding:
          EdgeInsets.all(_EntityDetailsPanelMobileConstants.contentPadding),
      child: Container(
        padding: EdgeInsets.all(_EntityDetailsPanelMobileConstants.cardPadding),
        decoration: BoxDecoration(
          color: Colors.indigo.shade50,
          borderRadius: BorderRadius.circular(
              _EntityDetailsPanelMobileConstants.cardBorderRadius),
          border: Border.all(color: Colors.indigo.shade100),
        ),
        child: Text(
          widget.entity.description!,
          style: TextStyle(
            fontSize: _EntityDetailsPanelMobileConstants.contentFontSize,
            fontFamily: _EntityDetailsPanelMobileConstants.fontFamily,
            color: Colors.black87,
            height: 1.4,
          ),
        ),
      ),
    );
  }

  /// Builds the navigation chips section
  Widget _buildNavigationChips() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: _EntityDetailsPanelMobileConstants.contentPadding,
        vertical: _EntityDetailsPanelMobileConstants.spacingMedium,
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: _buildNavigationChipsList(),
        ),
      ),
    );
  }

  /// Builds the main content area
  Widget _buildContentArea() {
    return Expanded(
      child: SingleChildScrollView(
        controller: _scrollController,
        padding: EdgeInsets.symmetric(
            horizontal: _EntityDetailsPanelMobileConstants.contentPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildAttributesSection(context, _sectionKeys),
            _buildBusinessRulesSection(context, _sectionKeys),
            _buildRelationshipsSection(context, _sectionKeys),
            _buildConstantsValidationsSection(context, _sectionKeys),
            SizedBox(
                height: _EntityDetailsPanelMobileConstants.bottomSpacerHeight),
          ],
        ),
      ),
    );
  }

  /// Scrolls to a specific entity section
  void _scrollToEntitySection(String sectionId) {
    if (_sectionKeys.containsKey(sectionId)) {
      final key = _sectionKeys[sectionId];

      if (key?.currentContext != null) {
        setState(() {
          _activeSectionId = sectionId;
        });

        Future.delayed(_EntityDetailsPanelMobileConstants.postFrameDelay, () {
          if (_scrollController.hasClients) {
            Scrollable.ensureVisible(
              key!.currentContext!,
              alignment: 0.0,
              duration:
                  _EntityDetailsPanelMobileConstants.scrollAnimationDuration,
              curve: Curves.easeInOut,
            );
          }
        });
      }
    }
  }

  /// Builds the list of navigation chips
  List<Widget> _buildNavigationChipsList() {
    final sections = _getSectionDefinitions();
    return sections.map((section) => _buildNavigationChip(section)).toList();
  }

  /// Gets section definitions for entity details
  List<Map<String, String>> _getSectionDefinitions() {
    return [
      {'id': 'attributes', 'label': 'Attributes', 'short': 'ATT'},
      {'id': 'business_rules', 'label': 'Business Rules', 'short': 'BR'},
      {'id': 'relationships', 'label': 'Relationships', 'short': 'REL'},
      {
        'id': 'constants_validations',
        'label': 'Constants & Validations',
        'short': 'CV'
      },
    ];
  }

  /// Builds an individual navigation chip
  Widget _buildNavigationChip(Map<String, String> section) {
    final isActive = _activeSectionId == section['id'];

    return Padding(
      padding: EdgeInsets.only(
          right: _EntityDetailsPanelMobileConstants.chipSpacing),
      child: GestureDetector(
        onTap: () => _scrollToEntitySection(section['id']!),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal:
                _EntityDetailsPanelMobileConstants.chipHorizontalPadding,
            vertical: _EntityDetailsPanelMobileConstants.chipVerticalPadding,
          ),
          decoration: BoxDecoration(
            color: isActive ? Colors.blue.shade50 : Colors.grey.shade100,
            borderRadius: BorderRadius.circular(
                _EntityDetailsPanelMobileConstants.chipBorderRadius),
            border: Border.all(
              color: isActive ? Colors.blue.shade300 : Colors.grey.shade300,
              width: 1,
            ),
          ),
          child: Text(
            section['short']!,
            style: TextStyle(
              fontSize: _EntityDetailsPanelMobileConstants.chipFontSize,
              fontWeight: isActive ? FontWeight.bold : FontWeight.w500,
              color: isActive ? Colors.blue.shade700 : Colors.grey.shade700,
              fontFamily: _EntityDetailsPanelMobileConstants.fontFamily,
            ),
          ),
        ),
      ),
    );
  }

  /// Builds a generic section widget with VisibilityDetector for mobile
  Widget _buildSection(
    BuildContext context,
    Map<String, GlobalKey> sectionKeys,
    String sectionId,
    String title,
    bool hasData,
    Widget Function() contentBuilder,
  ) {
    return VisibilityDetector(
      key: Key(sectionId),
      onVisibilityChanged: (VisibilityInfo info) {
        _sectionVisibilityInfo[sectionId] = info;
        _updateActiveSectionFromVisibility();
      },
      child: Container(
        key: sectionKeys[sectionId],
        padding: EdgeInsets.symmetric(
            vertical:
                _EntityDetailsPanelMobileConstants.sectionVerticalPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle(title),
            SizedBox(height: _EntityDetailsPanelMobileConstants.spacingMedium),
            hasData ? contentBuilder() : _buildNoDataWidget(title),
            SizedBox(height: _EntityDetailsPanelMobileConstants.spacingSmall),
            _buildSectionDivider(),
          ],
        ),
      ),
    );
  }

  /// Builds the section title
  Widget _buildSectionTitle(String title) {
    return Text(
      '$title:',
      style: TextStyle(
        fontWeight: FontWeight.w500,
        fontSize: _EntityDetailsPanelMobileConstants.sectionTitleFontSize,
        fontFamily: _EntityDetailsPanelMobileConstants.fontFamily,
        color: Colors.black,
      ),
    );
  }

  /// Builds the section divider
  Widget _buildSectionDivider() {
    return Container(
      height: 1,
      color: Colors.grey.shade200,
    );
  }

  /// Builds a no data widget
  Widget _buildNoDataWidget(String sectionName) {
    return Container(
      padding:
          EdgeInsets.all(_EntityDetailsPanelMobileConstants.contentPadding),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(
            _EntityDetailsPanelMobileConstants.cardBorderRadius),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Text(
        'No $sectionName data available.',
        style: TextStyle(
          fontFamily: _EntityDetailsPanelMobileConstants.fontFamily,
          fontSize: _EntityDetailsPanelMobileConstants.contentFontSize,
          fontStyle: FontStyle.italic,
          color: Colors.grey.shade600,
        ),
      ),
    );
  }

  /// Method to update active section based on visibility
  void _updateActiveSectionFromVisibility() {
    String? mostVisibleSection;
    double maxVisibility = 0;

    _sectionVisibilityInfo.forEach((sectionId, info) {
      if (info.visibleFraction > maxVisibility) {
        maxVisibility = info.visibleFraction;
        mostVisibleSection = sectionId;
      }
    });

    if (mostVisibleSection != null && mostVisibleSection != _activeSectionId) {
      if (mounted) {
        setState(() {
          _activeSectionId = mostVisibleSection;
        });
      }
    }
  }

  /// Builds the attributes section
  Widget _buildAttributesSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'attributes',
      'Attributes',
      widget.entity.attributeMetaDataList != null &&
          widget.entity.attributeMetaDataList!.isNotEmpty,
      () => _buildAttributesContent(context),
    );
  }

  /// Builds the attributes content
  Widget _buildAttributesContent(BuildContext context) {
    if (widget.entity.attributeMetaDataList != null &&
        widget.entity.attributeMetaDataList!.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: widget.entity.attributeMetaDataList!
            .map((attribute) => Container(
                  margin: EdgeInsets.only(bottom: 12),
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade100),
                  ),
                  child: GestureDetector(
                    onTap: () => _showChatDialog(context, 'attributes'),
                    child: _generateAttributeSentence(attribute),
                  ),
                ))
            .toList(),
      );
    }

    return _buildNoDataWidget('attributes');
  }

  /// Builds the business rules section
  Widget _buildBusinessRulesSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'business_rules',
      'Business Rules',
      widget.entity.relationshipProperties != null &&
          widget.entity.relationshipProperties!.isNotEmpty,
      () => _buildBusinessRulesContent(context),
    );
  }

  /// Builds the business rules content
  Widget _buildBusinessRulesContent(BuildContext context) {
    if (widget.entity.relationshipProperties != null &&
        widget.entity.relationshipProperties!.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: widget.entity.relationshipProperties!
            .map((relationShipProp) => Container(
                  margin: EdgeInsets.only(bottom: 12),
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.green.shade100),
                  ),
                  child: GestureDetector(
                    onTap: () => _showChatDialog(context, 'business rules'),
                    child: Text.rich(
                      TextSpan(
                        children: [
                          TextSpan(
                            text: relationShipProp.sourceEntity,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                              fontFamily: 'TiemposText',
                            ),
                          ),
                          TextSpan(
                            text: ' and ',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.normal,
                              color: Colors.black,
                              fontFamily: 'TiemposText',
                            ),
                          ),
                          TextSpan(
                            text: relationShipProp.targetEntity,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                              fontFamily: 'TiemposText',
                            ),
                          ),
                          TextSpan(
                            text:
                                ' have relationship. those are  ${relationShipProp.onDelete} and  ${relationShipProp.onUpdate}.',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.normal,
                              color: Colors.black,
                              fontFamily: 'TiemposText',
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ))
            .toList(),
      );
    }

    return _buildNoDataWidget('business rules');
  }

  /// Builds the relationships section
  Widget _buildRelationshipsSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'relationships',
      'Relationships',
      widget.entity.relationships != null &&
          widget.entity.relationships!.isNotEmpty,
      () => _buildRelationshipsContent(context),
    );
  }

  /// Builds the relationships content
  Widget _buildRelationshipsContent(BuildContext context) {
    if (widget.entity.relationships != null &&
        widget.entity.relationships!.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: widget.entity.relationships!
            .map((relation) => Container(
                  margin: EdgeInsets.only(bottom: 12),
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.orange.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.orange.shade100),
                  ),
                  child: GestureDetector(
                    onTap: () => _showChatDialog(context, 'relationships'),
                    child: Text.rich(
                      TextSpan(
                        children: [
                          TextSpan(
                            text: relation.sourceEntity,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                              fontFamily: 'TiemposText',
                            ),
                          ),
                          TextSpan(
                            text: ' has ${relation.type} relationship with ',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.normal,
                              color: Colors.black,
                              fontFamily: 'TiemposText',
                            ),
                          ),
                          TextSpan(
                            text: relation.targetEntity,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                              fontFamily: 'TiemposText',
                            ),
                          ),
                          TextSpan(
                            text: ' using ',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.normal,
                              color: Colors.black,
                              fontFamily: 'TiemposText',
                            ),
                          ),
                          TextSpan(
                            text: relation.joinCondition.replaceAll("=", "to"),
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                              fontFamily: 'TiemposText',
                            ),
                          ),
                          TextSpan(
                            text: '.',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.normal,
                              color: Colors.black,
                              fontFamily: 'TiemposText',
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ))
            .toList(),
      );
    }

    return _buildNoDataWidget('relationships');
  }

  /// Builds the constants & validations section
  Widget _buildConstantsValidationsSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'constants_validations',
      'Constants & Validations',
      widget.entity.validationsList != null &&
          widget.entity.validationsList!.isNotEmpty,
      () => _buildConstantsValidationsContent(context),
    );
  }

  /// Builds the constants & validations content
  Widget _buildConstantsValidationsContent(BuildContext context) {
    if (widget.entity.validationsList != null &&
        widget.entity.validationsList!.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: widget.entity.validationsList!
            .map((validation) => Container(
                  margin: EdgeInsets.only(bottom: 12),
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.purple.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.purple.shade100),
                  ),
                  child: GestureDetector(
                    onTap: () =>
                        _showChatDialog(context, 'constants & validations'),
                    child: Text.rich(
                      TextSpan(
                        children: [
                          TextSpan(
                            text: validation.attribute,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                              fontFamily: 'TiemposText',
                            ),
                          ),
                          TextSpan(
                            text: '  ${validation.constraintText}.',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.normal,
                              color: Colors.black,
                              fontFamily: 'TiemposText',
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ))
            .toList(),
      );
    }

    return _buildNoDataWidget('constants & validations');
  }

  /// Generate attribute sentence from attribute data
  Widget _generateAttributeSentence(dynamic attribute) {
    return Text.rich(TextSpan(
        text: "${attribute.displayName} ",
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
          color: Colors.black,
          fontFamily: 'TiemposText',
        ),
        children: <InlineSpan>[
          TextSpan(
            text:
                "is ${attribute.keyType} key and ${attribute.description}. Data type is ${attribute.dataType}${attribute.values != "N/A" ? " ( ${attribute.values} )" : ""}. Error message: \"${attribute.errorMessage}\". ${attribute.required} field.",
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.normal,
              color: Colors.black,
              fontFamily: 'TiemposText',
            ),
          )
        ]));
  }

  /// Show chat dialog (placeholder for now)
  void _showChatDialog(BuildContext context, String section) {
    // Placeholder for chat functionality
    // This would typically open a chat interface or send a message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Chat about $section'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  /// Show chat bottom sheet for entity-related conversations
  void _showChatBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.8,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (context, scrollController) => CommentsBottomSheet(
          chatController: widget.chatController,
          onSendMessage: widget.onSendMessage,
          context: 'entity: ${widget.entity.title ?? 'Entity Details'}',
        ),
      ),
    );
  }
}
