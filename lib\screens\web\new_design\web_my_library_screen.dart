import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/hover_create_button.dart';
import 'package:nsl/screens/web/new_design/widgets/hover_nav_item.dart';
import 'package:nsl/screens/web/new_design/widgets/library_side_drawer.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';

class Book {
  final String title;
  final String subtitle;
  final String imageUrl;
  final bool isDraft;
  final double imageWidth;
  final double imageHeight;

  Book({
    required this.title,
    required this.subtitle,
    required this.imageUrl,
    this.isDraft = false,
    this.imageWidth = 136.0,
    this.imageHeight = 200.0,
  });

  factory Book.fromJson(Map<String, dynamic> json) {
    return Book(
      title: json['title'] as String,
      subtitle: json['subtitle'] as String,
      imageUrl: json['imageUrl'] as String,
      isDraft: json['isDraft'] as bool? ?? false,
      imageWidth: (json['imageWidth'] as num?)?.toDouble() ?? 136.0,
      imageHeight: (json['imageHeight'] as num?)?.toDouble() ?? 200.0,
    );
  }
}

// ignore: must_be_immutable
class WebMyLibraryScreen extends StatefulWidget {
  WebMyLibraryScreen({super.key, this.showNavigationBar = true});

  bool showNavigationBar = true;

  @override
  State<WebMyLibraryScreen> createState() => WebMyLibraryScreenState();
}

class WebMyLibraryScreenState extends State<WebMyLibraryScreen>
    with TickerProviderStateMixin {
  late List<Book> books;
  List<Book> _allBooks = []; // Store original unfiltered books
  List<Book> _filteredBooks = []; // Store filtered books for display
  bool isLoading = true;
  late List<AnimationController> _animationControllers;
  late List<Animation<double>> _slideAnimations;
  late List<Animation<double>> _fadeAnimations;

  // Search state
  final TextEditingController _searchController = TextEditingController();

  // Pagination state
  int _currentPage = 1;
  int _itemsPerPage = 10; // Default for 1366px width
  int _totalPages = 1;

  // Side panel state
  bool _showSidePanel = false;

  // JSON string containing book data
  static const String booksJsonString = '''
{
  "books": [
    {
      "title": "Ecommerce Ecommerce Ecommerce ",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book_01.png",
      "isDraft": false
    },
    {
      "title": "Fashion & Apparel Fashion",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-02.png",
      "isDraft": false
    },
    {
      "title": "Financial Advisory Financial Advisory Financial Advisory ",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-03.png",
      "isDraft": false
    },
    {
      "title": "Home Rentals Home Rentals Home Rentals ",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": true
    },
    {
      "title": "Online Grocery Online Grocery Online Grocery ",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-05.png",
      "isDraft": false
    },
    {
      "title": "Courier & Logistics Couri & Logistics Courier & Logistics ",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-06.png",
      "isDraft": false
    },
    {
      "title": "Automotive Automotive Automotive ",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-07.png",
      "isDraft": true
    },
    {
      "title": "Fitness & Wellness Fit & Wellness ",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-08.png",
      "isDraft": false
    },
    {
      "title": "Real Estate Real Estate",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-09.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
     {
      "title": "Real Estate Real Estate",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-09.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    }, 
    {
      "title": "Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
     {
      "title": "Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
     {
      "title": "Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
     {
      "title": "Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
     {
      "title": "Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
     {
      "title": "Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    }
  ]
}
''';

  @override
  void initState() {
    super.initState();
    // Ensure the navigation state is set to webMyLibrary when this screen is displayed
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   if (mounted) {
    //     Provider.of<WebHomeProvider>(context, listen: false)
    //         .currentScreenIndex = ScreenConstants.webMyLibrary;
    //   }
    // });
    _loadBooks();
    _searchController.addListener(_onSearchChanged);
  }

  void _initializeAnimations() {
    if (!widget.showNavigationBar) {
      // Single animation controller for the entire grid section
      _animationControllers = [
        AnimationController(
          duration: const Duration(milliseconds: 1000),
          vsync: this,
        ),
      ];

      _slideAnimations = [
        Tween<double>(begin: 100.0, end: 0.0).animate(
          CurvedAnimation(
              parent: _animationControllers[0], curve: Curves.easeOutCubic),
        ),
      ];

      _fadeAnimations = [
        Tween<double>(begin: 0.0, end: 1.0).animate(
          CurvedAnimation(
              parent: _animationControllers[0], curve: Curves.easeInOut),
        ),
      ];

      // Start the single animation for the entire grid section
      Future.delayed(Duration(milliseconds: 200), () {
        if (mounted) {
          _animationControllers[0].forward();
        }
      });
    } else {
      // Individual animation controllers for each book
      _animationControllers = List.generate(
        books.length,
        (index) => AnimationController(
          duration: const Duration(milliseconds: 800),
          vsync: this,
        ),
      );

      _slideAnimations = _animationControllers.map((controller) {
        return Tween<double>(begin: 50.0, end: 0.0).animate(
          CurvedAnimation(parent: controller, curve: Curves.easeOutCubic),
        );
      }).toList();

      _fadeAnimations = _animationControllers.map((controller) {
        return Tween<double>(begin: 0.0, end: 1.0).animate(
          CurvedAnimation(parent: controller, curve: Curves.easeInOut),
        );
      }).toList();

      // Start animations with staggered delays (bottom to top)
      for (int i = 0; i < _animationControllers.length; i++) {
        Future.delayed(Duration(milliseconds: i * 100), () {
          if (mounted) {
            _animationControllers[i].forward();
          }
        });
      }
    }
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    for (var controller in _animationControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _loadBooks() {
    try {
      // Parse the JSON string
      final data = json.decode(booksJsonString);

      // Convert to Book objects
      final List<Book> loadedBooks = (data['books'] as List)
          .map((bookJson) => Book.fromJson(bookJson))
          .toList();

      setState(() {
        _allBooks = loadedBooks;
        books = loadedBooks;
        _filteredBooks = loadedBooks;
        isLoading = false;
        _updatePagination();
      });

      // Initialize animations after books are loaded
      _initializeAnimations();
    } catch (e) {
      setState(() {
        _allBooks = [];
        books = [];
        _filteredBooks = [];
        isLoading = false;
      });
      // Log error in a production-safe way
      debugPrint('Error loading books: $e');
    }
  }

  // Handle search text changes
  void _onSearchChanged() {
    _filterBooks();
  }

  // Filter books based on search text
  void _filterBooks() {
    final searchText = _searchController.text.toLowerCase();

    setState(() {
      if (searchText.isEmpty) {
        _filteredBooks = List.from(_allBooks);
      } else {
        _filteredBooks = _allBooks
            .where((book) =>
                book.title.toLowerCase().contains(searchText) ||
                book.subtitle.toLowerCase().contains(searchText))
            .toList();
      }

      books = _filteredBooks;
      // Reset to first page when filtering
      _currentPage = 1;
      _updatePagination();
    });
  }

  // Calculate items per page based on screen width and side panel state
  int _calculateItemsPerPage(double availableWidth) {
    // For 1920px and above screens, force exactly 8 books per row
    if (availableWidth >= 1520.0) {
      // Using 1520 to account for side panel
      return 8 * 2; // 8 books per row, 2 rows = 16 total
    }

    const double cardWidth = 136.0;
    double minSpacing = 50.0;

    // Calculate books per row for smaller screens
    int booksPerRow =
        ((availableWidth + minSpacing) / (cardWidth + minSpacing)).floor();
    if (booksPerRow == 0) booksPerRow = 1;

    // Determine number of rows
    int rows = 2;

    return booksPerRow * rows;
  }

  // Update pagination based on current state
  void _updatePagination() {
    if (books.isEmpty) {
      _totalPages = 1;
      return;
    }

    _totalPages = (books.length / _itemsPerPage).ceil();

    // Ensure current page is valid
    if (_currentPage > _totalPages) {
      _currentPage = _totalPages;
    }
    if (_currentPage < 1) {
      _currentPage = 1;
    }
  }

  // Get books for current page
  List<Book> _getCurrentPageBooks() {
    if (books.isEmpty) return [];

    int startIndex = (_currentPage - 1) * _itemsPerPage;
    int endIndex = startIndex + _itemsPerPage;

    if (startIndex >= books.length) return [];
    if (endIndex > books.length) endIndex = books.length;

    return books.sublist(startIndex, endIndex);
  }

  // Navigate to previous page
  void _goToPreviousPage() {
    if (_currentPage > 1) {
      setState(() {
        _currentPage--;
      });
    }
  }

  // Navigate to next page
  void _goToNextPage() {
    if (_currentPage < _totalPages) {
      setState(() {
        _currentPage++;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          widget.showNavigationBar ? Color(0xffF7F9FB) : Colors.transparent,
      body: Stack(
        children: [
          Row(
            children: [
              // Main content area
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    widget.showNavigationBar
                        ?
                        // Top navigation bar
                        Padding(
                            padding: widget.showNavigationBar
                                ? const EdgeInsets.only(
                                    left: 94, right: 94, bottom: 0.0, top: 0)
                                : EdgeInsets.zero,
                            child: Column(
                              children: [
                                const HoverNavItems(),
                                SizedBox(height: 20),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    // My Books text
                                    Expanded(
                                      child: Text(
                                        AppLocalizations.of(context)
                                            .translate('library.pageTitle'),
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Colors.black,
                                          fontWeight: FontWeight.w600,
                                          fontFamily: "TiemposText",
                                        ),
                                      ),
                                    ),

                                    // Search bar with filter
                                    Expanded(
                                      child: Container(
                                        width: MediaQuery.of(context)
                                                .size
                                                .width /
                                            3.722,
                                        height: 36,
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius:
                                              BorderRadius.circular(6),
                                              border:
                                              Border.all(color: Colors.grey.shade200),
                                        ),
                                        child: Row(
                                          children: [
                                            // Search text field
                                            Expanded(
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.only(
                                                        left: 16.0),
                                                child: TextField(
                                                  controller:
                                                      _searchController,
                                                  decoration:
                                                      InputDecoration(
                                                    enabledBorder:
                                                        InputBorder.none,
                                                    focusedBorder:
                                                        InputBorder.none,
                                                    hintText: 'Search Books...',
                                                    border:
                                                        InputBorder.none,
                                                    hintStyle: TextStyle(
                                                        fontSize: 14,
                                                        color: Color(
                                                            0xff939393)),
                                                    isDense: true,
                                                    contentPadding:
                                                        EdgeInsets.zero,
                                                    filled: false,
                                                    fillColor:
                                                        Colors.transparent,
                                                  ),
                                                ),
                                              ),
                                            ),
                                            // Search icon
                                            _HoverSvgButton(
                                              normalIconPath:
                                                  'assets/images/search.svg',
                                              hoverIconPath:
                                                  'assets/images/search.svg',
                                              onPressed: () {
                                                _filterBooks();
                                              },
                                              imageWidth: 20,
                                              imageHeight: 20,
                                              showBorderOnHover: false,
                                            ),
                                            // Divider between search and filter
                                            Container(
                                              height: 23,
                                              width: 1,
                                                color: Colors.grey.shade200,
                                              margin: const EdgeInsets
                                                  .symmetric(horizontal: 4),
                                            ),
                                            // Filter icon
                                            _HoverSvgButton(
                                              normalIconPath:
                                                  'assets/images/filter-icon.svg',
                                              hoverIconPath:
                                                  'assets/images/filter-hover.svg',
                                              onPressed: () {},
                                              imageWidth: 32,
                                              imageHeight: 32,
                                              showBorderOnHover: false,
                                            ),
                                            const SizedBox(width: 8),
                                          ],
                                        ),
                                      ),
                                    ),

                                    // Create book button
                                    Expanded(
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.end,
                                        mainAxisSize: MainAxisSize.min,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          HoverCreateButton(
                                            text: AppLocalizations.of(context)
                                                .translate(
                                                    'library.createButtonText'),
                                            onPressed: () {
                                              Provider.of<WebHomeProvider>(
                                                          context,
                                                          listen: false)
                                                      .currentScreenIndex =
                                                  ScreenConstants
                                                      .webBookDetailPage;
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 12),
                              ],
                            ),
                          )
                        : Container(),
                    // Main content
                    myBooks(context),
                  ],
                ),
              ),
              // Side panel
              if (_showSidePanel)
                Container(
                  width: MediaQuery.of(context).size.width /
                      4.6462, // dynamic width depending upon the screen width 1366/294=4.6462
                  height: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(
                      color: Color(0xC9C9C9BF),
                      width: 1,
                    ),
                  ),
                  child: SingleChildScrollView(
                    physics: AlwaysScrollableScrollPhysics(),
                    child: LibrarySideDrawer(),
                  ),
                ),
            ],
          ),
          // Toggle button overlay
          !widget.showNavigationBar ? Container() :  
        
          Positioned(
            right: _showSidePanel
                ? (MediaQuery.of(context).size.width / 4.6462) - 0
                : 0, // dynamic width depending upon the screen width 1366/294=4.6462
            top: 30,
            child: MouseRegion(
              cursor: SystemMouseCursors.click,
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    _showSidePanel = !_showSidePanel;
                  });
                },
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Color(0xff0058FF),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(25),
                      bottomLeft: Radius.circular(25),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        blurRadius: 4,
                        offset: Offset(2, 2),
                      ),
                    ],
                  ),
                  child: Transform.rotate(
                    angle:
                        _showSidePanel ? 0 : 3.14159, // 180 degrees in radians
                    child: Center(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            width: 2,
                            height: 16,
                            color: Colors.white,
                          ),
                          SizedBox(width: 1),
                          Icon(
                            Icons.arrow_forward,
                            color: Colors.white,
                            size: 16,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget myBooks(context) {
    return Expanded(
      child: SingleChildScrollView(
        physics: widget.showNavigationBar
            ? const AlwaysScrollableScrollPhysics()
            : const NeverScrollableScrollPhysics(),
        child: Container(
          padding:
              const EdgeInsets.only(left: 94, right: 94, bottom: 0.0, top: 0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 32),
              // Book grid
              isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : books.isEmpty
                      ? const Center(child: Text('No books found'))
                      : Consumer<WebHomeProvider>(
                          builder: (context, webHomeProvider, _) {
                            return Center(
                              child: LayoutBuilder(
                                builder: (context, constraints) {
                                  // Calculate available width considering side panel
                                  double availableWidth = constraints.maxWidth;
                                  if (webHomeProvider.showSidePanel) {
                                    // Subtract approximate side panel width
                                    availableWidth -=
                                        400; // Adjust based on your side panel width
                                  }

                                  // Update items per page based on available width
                                  int newItemsPerPage =
                                      _calculateItemsPerPage(availableWidth);
                                  if (newItemsPerPage != _itemsPerPage) {
                                    WidgetsBinding.instance
                                        .addPostFrameCallback((_) {
                                      setState(() {
                                        _itemsPerPage = newItemsPerPage;
                                        _updatePagination();
                                      });
                                    });
                                  }

                                  // Get books for current page
                                  List<Book> currentPageBooks =
                                      _getCurrentPageBooks();

                                  // Calculate books per row using the same logic as _calculateItemsPerPage
                                  int booksPerRow;
                                  if (availableWidth >= 1520.0) {
                                    booksPerRow =
                                        8; // Force 8 books per row for large screens
                                  } else {
                                    // For smaller screens, calculate based on available space
                                    const double cardWidth = 136.0;
                                    double minSpacing = 50.0;
                                    booksPerRow =
                                        ((availableWidth + minSpacing) /
                                                (cardWidth + minSpacing))
                                            .floor();
                                    if (booksPerRow == 0) booksPerRow = 1;
                                  }

                                  // Define spacing for layout
                                  double minSpacing;
                                  if (availableWidth >= 1920) {
                                    minSpacing = 200;
                                  } else {
                                    minSpacing = 50;
                                  }

                                  // Calculate rows needed for current page
                                  int rowCount =
                                      (currentPageBooks.length / booksPerRow)
                                          .ceil();

                                  // Conditional animation based on showNavigationBar (reversed logic)
                                  if (!widget.showNavigationBar) {
                                    // Grid section animation
                                    return AnimatedBuilder(
                                      animation: _animationControllers[0],
                                      builder: (context, child) {
                                        return Transform.translate(
                                          offset: Offset(
                                              0, _slideAnimations[0].value),
                                          child: Opacity(
                                            opacity: _fadeAnimations[0].value,
                                            child: Column(
                                              children: List.generate(rowCount,
                                                  (rowIndex) {
                                                // Calculate start and end indices for this row
                                                int startIndex =
                                                    rowIndex * booksPerRow;
                                                int endIndex = (startIndex +
                                                            booksPerRow <=
                                                        currentPageBooks.length)
                                                    ? startIndex + booksPerRow
                                                    : currentPageBooks.length;

                                                // Create a list of books for this row
                                                List<Book> rowBooks =
                                                    currentPageBooks.sublist(
                                                        startIndex, endIndex);

                                                // For the last row with fewer items, calculate positions
                                                bool isLastRowWithFewerItems =
                                                    rowBooks.length <
                                                            booksPerRow &&
                                                        rowIndex ==
                                                            rowCount - 1;

                                                return Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          bottom: 42.0),
                                                  child: Row(
                                                    mainAxisAlignment:
                                                        isLastRowWithFewerItems
                                                            ? MainAxisAlignment
                                                                .start
                                                            : MainAxisAlignment
                                                                .spaceBetween,
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: rowBooks
                                                        .asMap()
                                                        .entries
                                                        .map((entry) {
                                                      int idx = entry.key;
                                                      Book book = entry.value;

                                                      // Calculate spacing for consistent layout
                                                      double rightPadding = 0;
                                                      if (idx <
                                                          rowBooks.length - 1) {
                                                        // For large screens (8 books per row), use calculated spacing
                                                        if (availableWidth >=
                                                            1520.0) {
                                                          rightPadding =
                                                              (availableWidth -
                                                                      (8 *
                                                                          136)) /
                                                                  7; // Distribute remaining space evenly
                                                        } else {
                                                          // For medium screens, calculate spacing based on actual books per row
                                                          rightPadding =
                                                              (availableWidth -
                                                                      (booksPerRow *
                                                                          136)) /
                                                                  (booksPerRow -
                                                                      1);
                                                        }
                                                      }

                                                      return Padding(
                                                        padding: EdgeInsets.only(
                                                            right:
                                                                rightPadding),
                                                        child: _HoverBookCard(
                                                          onTap: () {
                                                            Provider.of<WebHomeProvider>(
                                                                        context,
                                                                        listen:
                                                                            false)
                                                                    .currentScreenIndex =
                                                                ScreenConstants
                                                                    .webBookSolution;
                                                          },
                                                          child:
                                                              _buildStaticBookCard(
                                                            title: book.title,
                                                            subtitle:
                                                                book.subtitle,
                                                            imageUrl:
                                                                book.imageUrl,
                                                            isDraft:
                                                                book.isDraft,
                                                            imageWidth:
                                                                book.imageWidth,
                                                            imageHeight: book
                                                                .imageHeight,
                                                            availableWidth:
                                                                availableWidth,
                                                          ),
                                                        ),
                                                      );
                                                    }).toList(),
                                                  ),
                                                );
                                              }),
                                            ),
                                          ),
                                        );
                                      },
                                    );
                                  } else {
                                    // Individual book animations
                                    return Column(
                                      children:
                                          List.generate(rowCount, (rowIndex) {
                                        // Calculate start and end indices for this row
                                        int startIndex = rowIndex * booksPerRow;
                                        int endIndex =
                                            (startIndex + booksPerRow <=
                                                    currentPageBooks.length)
                                                ? startIndex + booksPerRow
                                                : currentPageBooks.length;

                                        // Create a list of books for this row
                                        List<Book> rowBooks = currentPageBooks
                                            .sublist(startIndex, endIndex);

                                        // For the last row with fewer items, calculate positions
                                        bool isLastRowWithFewerItems =
                                            rowBooks.length < booksPerRow &&
                                                rowIndex == rowCount - 1;

                                        return Padding(
                                          padding: const EdgeInsets.only(
                                              bottom: 42.0),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: rowBooks
                                                .asMap()
                                                .entries
                                                .map((entry) {
                                              int idx = entry.key;
                                              Book book = entry.value;

                                              // Calculate spacing for consistent layout
                                              double rightPadding = 0;
                                              if (idx < rowBooks.length - 1) {
                                                // For large screens (8 books per row), use calculated spacing
                                                if (availableWidth >= 1520.0) {
                                                  rightPadding = (availableWidth -
                                                          (8 * 136)) /
                                                      7; // Distribute remaining space evenly
                                                } else {
                                                  // For medium screens, calculate spacing based on actual books per row
                                                  rightPadding =
                                                      (availableWidth -
                                                              (booksPerRow *
                                                                  136)) /
                                                          (booksPerRow - 1);
                                                }
                                              }

                                              return Padding(
                                                padding: EdgeInsets.only(
                                                    right: rightPadding),
                                                child: _HoverBookCard(
                                                  onTap: () {
                                                    Provider.of<WebHomeProvider>(
                                                                context,
                                                                listen: false)
                                                            .currentScreenIndex =
                                                        ScreenConstants
                                                            .webBookSolution;
                                                  },
                                                  child: _buildBookCard(
                                                    title: book.title,
                                                    subtitle: book.subtitle,
                                                    imageUrl: book.imageUrl,
                                                    isDraft: book.isDraft,
                                                    imageWidth: book.imageWidth,
                                                    imageHeight:
                                                        book.imageHeight,
                                                    index: books.indexOf(book),
                                                  ),
                                                ),
                                              );
                                            }).toList(),
                                          ),
                                        );
                                      }),
                                    );
                                  }
                                },
                              ),
                            );
                          },
                        ),
              // Pagination controls
              if (books.isNotEmpty && _totalPages > 1)
                Container(
                  margin: const EdgeInsets.only(bottom: 10),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Page info
                      Text(
                        'Page $_currentPage of $_totalPages',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          fontFamily: "TiemposText",
                          color: Colors.grey,
                        ),
                      ),
                      // Navigation buttons
                      Row(
                        children: [
                          // Previous button
                          _HoverPaginationButton(
                            icon: const Icon(Icons.chevron_left, size: 20),
                            onPressed:
                                _currentPage > 1 ? _goToPreviousPage : null,
                          ),
                          const SizedBox(width: 8),
                          // Next button
                          _HoverPaginationButton(
                            icon: const Icon(Icons.chevron_right, size: 20),
                            onPressed: _currentPage < _totalPages
                                ? _goToNextPage
                                : null,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBookCard({
    required String title,
    required String subtitle,
    required String imageUrl,
    bool isDraft = false,
    double imageWidth = 136.0,
    double imageHeight = 200.0,
    int index = 0,
    double availableWidth = 1366.0,
  }) {
    // Check if animations are initialized and index is valid
    if (index >= _animationControllers.length) {
      // Fallback to non-animated version if index is out of bounds
      return _buildStaticBookCard(
        title: title,
        subtitle: subtitle,
        imageUrl: imageUrl,
        isDraft: isDraft,
        imageWidth: imageWidth,
        imageHeight: imageHeight,
      );
    }

    return AnimatedBuilder(
      animation: _animationControllers[index],
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _slideAnimations[index].value),
          child: Opacity(
            opacity: _fadeAnimations[index].value,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Book cover with staggered animations
                Stack(
                  clipBehavior: Clip.none,
                  children: [
                    // Background SVG shape - appears first
                    Positioned(
                      right: -7,
                      bottom: 0,
                      child: AnimatedBuilder(
                        animation: _animationControllers[index],
                        builder: (context, child) {
                          // SVG shape animation starts immediately
                          double svgOpacity =
                              _animationControllers[index].value;
                          double svgScale =
                              0.8 + (0.2 * _animationControllers[index].value);

                          return Transform.scale(
                            scale: svgScale,
                            child: Opacity(
                              opacity: svgOpacity,
                              child: SvgPicture.asset(
                                'assets/images/home-lib-shape.svg',
                                width: 126,
                                height: 190,
                                fit: BoxFit.contain,
                              ),
                            ),
                          );
                        },
                      ),
                    ),

                    // Main book image - appears after delay
                    AnimatedBuilder(
                      animation: _animationControllers[index],
                      builder: (context, child) {
                        // Book image animation starts after 300ms delay
                        double delayedProgress =
                            (_animationControllers[index].value * 1.5 - 0.5)
                                .clamp(0.0, 1.0);
                        double bookOpacity = delayedProgress;
                        double bookScale = 0.9 + (0.1 * delayedProgress);

                        return Transform.scale(
                          scale: bookScale,
                          child: Opacity(
                            opacity: bookOpacity,
                            child: Container(
                              width: 136,
                              height: 200,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.only(
                                  topRight: Radius.circular(24),
                                  topLeft: Radius.circular(12),
                                  bottomLeft: Radius.circular(12),
                                  bottomRight: Radius.circular(12),
                                ),
                                image: DecorationImage(
                                  image: AssetImage(imageUrl),
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),

                    // Draft label if needed - appears with book image
                    if (isDraft)
                      AnimatedBuilder(
                        animation: _animationControllers[index],
                        builder: (context, child) {
                          double delayedProgress =
                              (_animationControllers[index].value * 1.5 - 0.5)
                                  .clamp(0.0, 1.0);

                          return Positioned(
                            top: 8,
                            right: 14,
                            child: Opacity(
                              opacity: delayedProgress,
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: Colors.amber,
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: const Text(
                                  'Draft',
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black,
                                    fontFamily: "TiemposText",
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                  ],
                ),
                const SizedBox(height: AppSpacing.xs),

                // Title and subtitle with fade animation
                SizedBox(
                  width: imageWidth,
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: AppSpacing.size14,
                      color: Color(0xFF222222),
                      fontFamily: "TiemposText",
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(height: 2),
                SizedBox(
                  width: imageWidth,
                  child: Text(
                    subtitle,
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: AppSpacing.size14,
                      color: Color(0xFF222222),
                      fontFamily: "TiemposText",
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Fallback static version for cases where animations aren't ready
  Widget _buildStaticBookCard({
    required String title,
    required String subtitle,
    required String imageUrl,
    bool isDraft = false,
    double imageWidth = 136.0,
    double imageHeight = 200.0,
    double availableWidth = 1366.0,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Stack(
          clipBehavior: Clip.none,
          children: [
            Positioned(
              right: -7,
              bottom: 0,
              child: SvgPicture.asset(
                'assets/images/home-lib-shape.svg',
                width: 126,
                height: 190,
                fit: BoxFit.contain,
              ),
            ),
            Container(
              width: 136,
              height: 200,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(24),
                  topLeft: Radius.circular(12),
                  bottomLeft: Radius.circular(12),
                  bottomRight: Radius.circular(12),
                ),
                image: DecorationImage(
                  image: AssetImage(imageUrl),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            if (isDraft)
              Positioned(
                top: 8,
                right: 14,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.amber,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'Draft',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                      fontFamily: "TiemposText",
                    ),
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: AppSpacing.xs),
        SizedBox(
          width: imageWidth,
          child: Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: AppSpacing.size14,
              color: Color(0xFF222222),
              fontFamily: "TiemposText",
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        const SizedBox(height: 2),
        SizedBox(
          width: imageWidth,
          child: Text(
            subtitle,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: AppSpacing.size14,
              color: Color(0xFF222222),
              fontFamily: "TiemposText",
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}

class _HoverIconButton extends StatefulWidget {
  final Icon icon;
  final VoidCallback onPressed;

  const _HoverIconButton({
    required this.icon,
    required this.onPressed,
  });

  @override
  State<_HoverIconButton> createState() => _HoverIconButtonState();
}

class _HoverIconButtonState extends State<_HoverIconButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Container(
        height: 32,
        width: 32,
        decoration: BoxDecoration(
          border: Border.all(
            color: isHovered ? Color(0xff0058FF) : Colors.transparent,
            width: 1.0,
          ),
          borderRadius: BorderRadius.circular(4),
        ),
        child: IconButton(
          icon: widget.icon,
          onPressed: widget.onPressed,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
      ),
    );
  }
}

class _HoverSvgButton extends StatefulWidget {
  final String normalIconPath;
  final String hoverIconPath;
  final VoidCallback onPressed;
  final double imageWidth;
  final double imageHeight;
  final bool showBorderOnHover;

  const _HoverSvgButton({
    required this.normalIconPath,
    required this.hoverIconPath,
    required this.onPressed,
    this.imageWidth = 18,
    this.imageHeight = 18,
    this.showBorderOnHover = true,
  });

  @override
  State<_HoverSvgButton> createState() => _HoverSvgButtonState();
}

class _HoverSvgButtonState extends State<_HoverSvgButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Container(
        height: 32,
        width: 32,
        decoration: BoxDecoration(
          border: widget.showBorderOnHover
              ? Border.all(
                  color: isHovered ? Color(0xff0058FF) : Colors.transparent,
                  width: 1.0,
                )
              : null,
          borderRadius: BorderRadius.circular(4),
        ),
        child: IconButton(
          icon: SvgPicture.asset(
            isHovered ? widget.hoverIconPath : widget.normalIconPath,
            width: widget.imageWidth,
            height: widget.imageHeight,
          ),
          onPressed: widget.onPressed,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
      ),
    );
  }
}

class _HoverPaginationButton extends StatefulWidget {
  final Icon icon;
  final VoidCallback? onPressed;

  const _HoverPaginationButton({
    required this.icon,
    required this.onPressed,
  });

  @override
  State<_HoverPaginationButton> createState() => _HoverPaginationButtonState();
}

class _HoverPaginationButtonState extends State<_HoverPaginationButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    bool isDisabled = widget.onPressed == null;

    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Container(
        width: 24,
        height: 24,
        decoration: BoxDecoration(
          border: Border.all(
            color: isDisabled
                ? Colors.grey.shade200
                : isHovered
                    ? Color(0xff0058FF)
                    : Colors.grey.shade300,
            width: 1.0,
          ),
          // No border radius when hovered
          borderRadius: isHovered && !isDisabled ? BorderRadius.zero : null,
          color: isDisabled ? Colors.grey.shade50 : Colors.white,
        ),
        child: IconButton(
          icon: widget.icon,
          onPressed: widget.onPressed,
          padding: EdgeInsets.zero,
          color: isDisabled
              ? Colors.grey.shade400
              : isHovered
                  ? Color(0xff0058FF)
                  : Colors.black,
          constraints: const BoxConstraints(),
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
      ),
    );
  }
}

class _HoverBookCard extends StatefulWidget {
  final Widget child;
  final VoidCallback onTap;

  const _HoverBookCard({
    required this.child,
    required this.onTap,
  });

  @override
  State<_HoverBookCard> createState() => _HoverBookCardState();
}

class _HoverBookCardState extends State<_HoverBookCard> {
  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: widget.onTap,
        child: widget.child,
      ),
    );
  }
}
