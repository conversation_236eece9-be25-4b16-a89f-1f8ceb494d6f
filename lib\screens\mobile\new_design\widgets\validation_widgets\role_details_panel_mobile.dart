import 'package:flutter/material.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:nsl/models/agent_manual_response_model.dart';
import '../../../../../models/role_info.dart';
import '../comments_bottom_sheet.dart';

/// Constants for consistent styling across the role details panel
class _RoleDetailsPanelMobileConstants {
  // Layout constants
  static const double borderRadius = 20.0;
  static const double headerPadding = 16.0;
  static const double contentPadding = 16.0;
  static const double sectionVerticalPadding = 16.0;
  static const double bottomSpacerHeight = 200.0;

  // Avatar constants
  static const double avatarRadius = 12.0;
  static const double avatarIconSize = 18.0;

  // Navigation chip constants
  static const double chipHorizontalPadding = 12.0;
  static const double chipVerticalPadding = 6.0;
  static const double chipBorderRadius = 16.0;
  static const double chipSpacing = 8.0;

  // Card constants
  static const double cardBorderRadius = 8.0;
  static const double cardPadding = 12.0;
  static const double cardMarginBottom = 12.0;
  static const double userCardPadding = 16.0;

  // Spacing constants
  static const double spacingSmall = 8.0;
  static const double spacingMedium = 12.0;

  // Text constants
  static const String fontFamily = "TiemposText";
  static const double titleFontSize = 16.0;
  static const double sectionTitleFontSize = 16.0;
  static const double contentFontSize = 14.0;
  static const double chipFontSize = 12.0;

  // Colors
  static const Color avatarBackgroundColor = Color(0xffE6F7FF);
  static const Color avatarIconColor = Color(0xff1890FF);

  // Visibility threshold for section detection
  static const double visibilityThreshold = 0.3;

  // Animation constants
  static const Duration scrollAnimationDuration = Duration(milliseconds: 300);
  static const Duration postFrameDelay = Duration(milliseconds: 50);
}

/// A mobile-optimized role details panel widget that displays detailed information about a selected role.
///
/// This widget shows role information including use cases and permissions in a structured format
/// optimized for mobile screens with touch-friendly interactions.
class RoleDetailsPanelMobile extends StatefulWidget {
  /// The role information to display
  final RoleInfo role;

  /// Callback when the close button is pressed
  final VoidCallback? onClose;

  /// Chat controller for interactive elements
  final TextEditingController? chatController;

  /// Callback when a message is sent
  final VoidCallback? onSendMessage;

  /// Configuration flag to show legacy sections (Use Cases/Permissions) vs new sections (CR/KPI/DA)
  final bool showLegacySections;

  /// List of users to display in the Users section
  final List<User>? users;

  const RoleDetailsPanelMobile({
    super.key,
    required this.role,
    this.onClose,
    this.chatController,
    this.onSendMessage,
    this.showLegacySections = false,
    this.users,
  });

  @override
  State<RoleDetailsPanelMobile> createState() => _RoleDetailsPanelMobileState();
}

class _RoleDetailsPanelMobileState extends State<RoleDetailsPanelMobile> {
  /// Track expanded state for each user card
  final Set<String> _expandedUserIds = <String>{};

  /// Track active section for navigation styling
  String? _activeSectionId;

  /// Global keys for sections - persists across builds
  late Map<String, GlobalKey> _roleSectionKeys;

  /// ScrollController for the content area
  late ScrollController _scrollController;

  /// VisibilityInfo objects for tracking section visibility
  final VisibilityInfo _useCasesVi = VisibilityInfo(key: Key('useCasesVi'));
  final VisibilityInfo _permissionsVi =
      VisibilityInfo(key: Key('permissionsVi'));
  final VisibilityInfo _coreResponsibilitiesVi =
      VisibilityInfo(key: Key('coreResponsibilitiesVi'));
  final VisibilityInfo _kpisVi = VisibilityInfo(key: Key('kpisVi'));
  final VisibilityInfo _decisionAuthorityVi =
      VisibilityInfo(key: Key('decisionAuthorityVi'));
  final VisibilityInfo _usersVi = VisibilityInfo(key: Key('usersVi'));

  /// Map to store visibility information for each section
  final Map<String, VisibilityInfo> _sectionVisibilityInfo = {};

  @override
  void initState() {
    super.initState();
    _initializeScrollController();
    _initializeSectionKeys();
    _initializeActiveSection();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  /// Initialize the scroll controller and add listener
  void _initializeScrollController() {
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  /// Initialize section keys based on legacy/new mode
  void _initializeSectionKeys() {
    _roleSectionKeys = widget.showLegacySections
        ? {
            'useCases': GlobalKey(),
            'permissions': GlobalKey(),
            'users': GlobalKey(),
          }
        : {
            'coreResponsibilities': GlobalKey(),
            'kpis': GlobalKey(),
            'decisionAuthority': GlobalKey(),
            'users': GlobalKey(),
          };
  }

  /// Initialize the first section as active after frame is built
  void _initializeActiveSection() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _activeSectionId =
              widget.showLegacySections ? 'useCases' : 'coreResponsibilities';
        });
      }
    });
  }

  /// Handle scroll events for active section detection
  void _onScroll() {
    if (widget.showLegacySections) {
      _handleLegacySectionVisibility();
    } else {
      _handleNewSectionVisibility();
    }
  }

  /// Handle visibility detection for legacy sections
  void _handleLegacySectionVisibility() {
    if (_useCasesVi.visibleFraction >
        _RoleDetailsPanelMobileConstants.visibilityThreshold) {
      _updateActiveSection('useCases');
    } else if (_permissionsVi.visibleFraction >
        _RoleDetailsPanelMobileConstants.visibilityThreshold) {
      _updateActiveSection('permissions');
    }
  }

  /// Handle visibility detection for new sections
  void _handleNewSectionVisibility() {
    if (_coreResponsibilitiesVi.visibleFraction >
        _RoleDetailsPanelMobileConstants.visibilityThreshold) {
      _updateActiveSection('coreResponsibilities');
    } else if (_kpisVi.visibleFraction >
        _RoleDetailsPanelMobileConstants.visibilityThreshold) {
      _updateActiveSection('kpis');
    } else if (_decisionAuthorityVi.visibleFraction >
        _RoleDetailsPanelMobileConstants.visibilityThreshold) {
      _updateActiveSection('decisionAuthority');
    } else if (_usersVi.visibleFraction >
        _RoleDetailsPanelMobileConstants.visibilityThreshold) {
      _updateActiveSection('users');
    }
  }

  /// Update the active section if it has changed
  void _updateActiveSection(String sectionId) {
    if (_activeSectionId != sectionId && mounted) {
      setState(() {
        _activeSectionId = sectionId;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: _buildContainerDecoration(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          _buildDescriptionSection(),
          _buildNavigationChips(),
          _buildContentArea(),
        ],
      ),
    );
  }

  /// Builds the main container decoration
  BoxDecoration _buildContainerDecoration() {
    return BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.vertical(
        top: Radius.circular(_RoleDetailsPanelMobileConstants.borderRadius),
      ),
    );
  }

  /// Builds the header section with avatar, title, and action buttons
  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(_RoleDetailsPanelMobileConstants.headerPadding),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: Row(
        children: [
          _buildAvatar(),
          SizedBox(width: _RoleDetailsPanelMobileConstants.spacingMedium),
          _buildTitle(),
          _buildChatButton(),
          _buildCloseButton(),
        ],
      ),
    );
  }

  /// Builds the role avatar
  Widget _buildAvatar() {
    return CircleAvatar(
      backgroundColor: _RoleDetailsPanelMobileConstants.avatarBackgroundColor,
      radius: _RoleDetailsPanelMobileConstants.avatarRadius,
      child: Icon(
        Icons.person_outline,
        color: _RoleDetailsPanelMobileConstants.avatarIconColor,
        size: _RoleDetailsPanelMobileConstants.avatarIconSize,
      ),
    );
  }

  /// Builds the role title
  Widget _buildTitle() {
    return Expanded(
      child: Text(
        widget.role.title,
        style: TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: _RoleDetailsPanelMobileConstants.titleFontSize,
          fontFamily: _RoleDetailsPanelMobileConstants.fontFamily,
        ),
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  /// Builds the chat button
  Widget _buildChatButton() {
    return IconButton(
      icon: Icon(Icons.chat, color: Colors.black, size: 20),
      onPressed: () => _showChatBottomSheet(context),
    );
  }

  /// Builds the close button
  Widget _buildCloseButton() {
    return IconButton(
      icon: Icon(Icons.close, color: Colors.black, size: 20),
      onPressed: widget.onClose,
    );
  }

  /// Builds the description section if description exists
  Widget _buildDescriptionSection() {
    if (widget.role.description.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(_RoleDetailsPanelMobileConstants.contentPadding),
      child: Container(
        padding: EdgeInsets.all(_RoleDetailsPanelMobileConstants.cardPadding),
        decoration: BoxDecoration(
          color: Colors.indigo.shade50,
          borderRadius: BorderRadius.circular(
              _RoleDetailsPanelMobileConstants.cardBorderRadius),
          border: Border.all(color: Colors.indigo.shade100),
        ),
        child: Text(
          widget.role.description,
          style: TextStyle(
            fontSize: _RoleDetailsPanelMobileConstants.contentFontSize,
            fontFamily: _RoleDetailsPanelMobileConstants.fontFamily,
            color: Colors.black87,
            height: 1.4,
          ),
        ),
      ),
    );
  }

  /// Builds the navigation chips section
  Widget _buildNavigationChips() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: _RoleDetailsPanelMobileConstants.contentPadding,
        vertical: _RoleDetailsPanelMobileConstants.spacingMedium,
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: _buildNavigationChipsList(),
        ),
      ),
    );
  }

  /// Builds the main content area
  Widget _buildContentArea() {
    return Expanded(
      child: SingleChildScrollView(
        controller: _scrollController,
        padding: EdgeInsets.symmetric(
            horizontal: _RoleDetailsPanelMobileConstants.contentPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ..._buildSectionsList(),
            SizedBox(
                height: _RoleDetailsPanelMobileConstants.bottomSpacerHeight),
          ],
        ),
      ),
    );
  }

  /// Builds the list of sections based on legacy/new mode
  List<Widget> _buildSectionsList() {
    return widget.showLegacySections
        ? [
            _buildUseCasesSection(context, _roleSectionKeys),
            _buildPermissionsSection(context, _roleSectionKeys),
          ]
        : [
            _buildCoreResponsibilitiesSection(context, _roleSectionKeys),
            _buildKPIsSection(context, _roleSectionKeys),
            _buildDecisionAuthoritySection(context, _roleSectionKeys),
            _buildUsersSection(context, _roleSectionKeys),
          ];
  }

  /// Scrolls to a specific role section
  void _scrollToRoleSection(String sectionId) {
    if (_roleSectionKeys.containsKey(sectionId)) {
      final key = _roleSectionKeys[sectionId];

      if (key?.currentContext != null) {
        _updateActiveSection(sectionId);

        Future.delayed(_RoleDetailsPanelMobileConstants.postFrameDelay, () {
          if (_scrollController.hasClients) {
            Scrollable.ensureVisible(
              key!.currentContext!,
              alignment: 0.0,
              duration:
                  _RoleDetailsPanelMobileConstants.scrollAnimationDuration,
              curve: Curves.easeInOut,
            );
          }
        });
      }
    }
  }

  /// Builds the list of navigation chips
  List<Widget> _buildNavigationChipsList() {
    final sections = _getSectionDefinitions();
    return sections.map((section) => _buildNavigationChip(section)).toList();
  }

  /// Gets section definitions based on legacy/new mode
  List<Map<String, String>> _getSectionDefinitions() {
    return widget.showLegacySections
        ? [
            {'id': 'useCases', 'label': 'Use Cases', 'short': 'UC'},
            {'id': 'permissions', 'label': 'Permissions', 'short': 'PR'},
            {'id': 'users', 'label': 'Users', 'short': 'U'},
          ]
        : [
            {
              'id': 'coreResponsibilities',
              'label': 'Core Responsibilities',
              'short': 'CR'
            },
            {'id': 'kpis', 'label': 'KPIs', 'short': 'KPI'},
            {
              'id': 'decisionAuthority',
              'label': 'Decision Authority',
              'short': 'DA'
            },
            {'id': 'users', 'label': 'Users', 'short': 'U'},
          ];
  }

  /// Builds an individual navigation chip
  Widget _buildNavigationChip(Map<String, String> section) {
    final isActive = _activeSectionId == section['id'];

    return Padding(
      padding:
          EdgeInsets.only(right: _RoleDetailsPanelMobileConstants.chipSpacing),
      child: GestureDetector(
        onTap: () => _scrollToRoleSection(section['id']!),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: _RoleDetailsPanelMobileConstants.chipHorizontalPadding,
            vertical: _RoleDetailsPanelMobileConstants.chipVerticalPadding,
          ),
          decoration: BoxDecoration(
            color: isActive ? Colors.blue.shade50 : Colors.grey.shade100,
            borderRadius: BorderRadius.circular(
                _RoleDetailsPanelMobileConstants.chipBorderRadius),
            border: Border.all(
              color: isActive ? Colors.blue.shade300 : Colors.grey.shade300,
              width: 1,
            ),
          ),
          child: Text(
            section['short']!,
            style: TextStyle(
              fontSize: _RoleDetailsPanelMobileConstants.chipFontSize,
              fontWeight: isActive ? FontWeight.bold : FontWeight.w500,
              color: isActive ? Colors.blue.shade700 : Colors.grey.shade700,
              fontFamily: _RoleDetailsPanelMobileConstants.fontFamily,
            ),
          ),
        ),
      ),
    );
  }

  /// Builds a generic section widget with VisibilityDetector for mobile
  Widget _buildSection(
    BuildContext context,
    Map<String, GlobalKey> sectionKeys,
    String sectionId,
    String title,
    bool hasData,
    Widget Function() contentBuilder,
  ) {
    return VisibilityDetector(
      key: Key(sectionId),
      onVisibilityChanged: (VisibilityInfo info) {
        _sectionVisibilityInfo[sectionId] = info;
        _updateActiveSectionFromVisibility();
      },
      child: Container(
        key: sectionKeys[sectionId],
        padding: EdgeInsets.symmetric(
            vertical: _RoleDetailsPanelMobileConstants.sectionVerticalPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle(title),
            SizedBox(height: _RoleDetailsPanelMobileConstants.spacingMedium),
            hasData ? contentBuilder() : _buildNoDataWidget(title),
            SizedBox(height: _RoleDetailsPanelMobileConstants.spacingSmall),
            _buildSectionDivider(),
          ],
        ),
      ),
    );
  }

  /// Builds the section title
  Widget _buildSectionTitle(String title) {
    return Text(
      '$title:',
      style: TextStyle(
        fontWeight: FontWeight.w500,
        fontSize: _RoleDetailsPanelMobileConstants.sectionTitleFontSize,
        fontFamily: _RoleDetailsPanelMobileConstants.fontFamily,
        color: Colors.black,
      ),
    );
  }

  /// Builds the section divider
  Widget _buildSectionDivider() {
    return Container(
      height: 1,
      color: Colors.grey.shade200,
    );
  }

  /// Builds a no data widget
  Widget _buildNoDataWidget(String sectionName) {
    return Container(
      padding: EdgeInsets.all(_RoleDetailsPanelMobileConstants.contentPadding),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(
            _RoleDetailsPanelMobileConstants.cardBorderRadius),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Text(
        'No $sectionName data available.',
        style: TextStyle(
          fontFamily: _RoleDetailsPanelMobileConstants.fontFamily,
          fontSize: _RoleDetailsPanelMobileConstants.contentFontSize,
          fontStyle: FontStyle.italic,
          color: Colors.grey.shade600,
        ),
      ),
    );
  }

  /// Method to update active section based on visibility
  void _updateActiveSectionFromVisibility() {
    String? mostVisibleSection;
    double maxVisibility = 0;

    _sectionVisibilityInfo.forEach((sectionId, info) {
      if (info.visibleFraction > maxVisibility) {
        maxVisibility = info.visibleFraction;
        mostVisibleSection = sectionId;
      }
    });

    if (mostVisibleSection != null && mostVisibleSection != _activeSectionId) {
      if (mounted) {
        setState(() {
          _activeSectionId = mostVisibleSection;
        });
      }
    }
  }

  /// Builds the core responsibilities section
  Widget _buildCoreResponsibilitiesSection(
      BuildContext context, Map<String, GlobalKey> roleSectionKeys) {
    return _buildSection(
      context,
      roleSectionKeys,
      'coreResponsibilities',
      'Core Responsibilities',
      widget.role.coreResponsibilities != null &&
          widget.role.coreResponsibilities!.isNotEmpty,
      () => _buildCoreResponsibilitiesContent(context),
    );
  }

  /// Builds the core responsibilities content
  Widget _buildCoreResponsibilitiesContent(BuildContext context) {
    List<String> responsibilities = widget.role.coreResponsibilities ?? [];

    if (responsibilities.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: responsibilities
            .map((responsibility) => Container(
                  margin: EdgeInsets.only(bottom: 8),
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade100),
                  ),
                  child: GestureDetector(
                    onTap: () =>
                        _showChatDialog(context, 'core responsibilities'),
                    child: Text(
                      responsibility,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.black87,
                        fontFamily: "TiemposText",
                        height: 1.4,
                      ),
                    ),
                  ),
                ))
            .toList(),
      );
    }

    return _buildNoDataWidget('core responsibilities');
  }

  /// Builds the KPIs section
  Widget _buildKPIsSection(
      BuildContext context, Map<String, GlobalKey> roleSectionKeys) {
    return _buildSection(
      context,
      roleSectionKeys,
      'kpis',
      'Key Performance Indicators',
      widget.role.kpis != null && widget.role.kpis!.isNotEmpty,
      () => _buildKPIsContent(context),
    );
  }

  /// Builds the KPIs content
  Widget _buildKPIsContent(BuildContext context) {
    List<dynamic> kpis = (widget.role.kpis ?? []).map((kpi) => kpi).toList();

    if (kpis.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: kpis
            .map((kpi) => Container(
                  margin: EdgeInsets.only(bottom: 12),
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.green.shade100),
                  ),
                  child: GestureDetector(
                    onTap: () =>
                        _showChatDialog(context, 'key performance indicators'),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          kpi.name ?? 'KPI',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                            fontFamily: "TiemposText",
                          ),
                        ),
                        if (kpi.description != null) ...[
                          SizedBox(height: 4),
                          Text(
                            kpi.description,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.black87,
                              fontFamily: "TiemposText",
                              height: 1.4,
                            ),
                          ),
                        ],
                        if (kpi.formula != null) ...[
                          SizedBox(height: 4),
                          Text(
                            'Formula: ${kpi.formula!}',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade700,
                              fontFamily: "TiemposText",
                            ),
                          ),
                        ],
                        if (kpi.target != null) ...[
                          SizedBox(height: 4),
                          Text(
                            'Target: ${kpi.target!}',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade700,
                              fontFamily: "TiemposText",
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ))
            .toList(),
      );
    }

    return _buildNoDataWidget('key performance indicators');
  }

  /// Builds the decision authority section
  Widget _buildDecisionAuthoritySection(
      BuildContext context, Map<String, GlobalKey> roleSectionKeys) {
    return _buildSection(
      context,
      roleSectionKeys,
      'decisionAuthority',
      'Decision Authority',
      widget.role.decisionAuthority != null &&
          widget.role.decisionAuthority!.isNotEmpty,
      () => _buildDecisionAuthorityContent(context),
    );
  }

  /// Builds the decision authority content
  Widget _buildDecisionAuthorityContent(BuildContext context) {
    List<String> authorities = widget.role.decisionAuthority ?? [];

    if (authorities.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: authorities
            .map((authority) => Container(
                  margin: EdgeInsets.only(bottom: 8),
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.orange.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.orange.shade100),
                  ),
                  child: GestureDetector(
                    onTap: () => _showChatDialog(context, 'decision authority'),
                    child: Text(
                      authority,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.black87,
                        fontFamily: "TiemposText",
                        height: 1.4,
                      ),
                    ),
                  ),
                ))
            .toList(),
      );
    }

    return _buildNoDataWidget('decision authority');
  }

  /// Builds the users section
  Widget _buildUsersSection(
      BuildContext context, Map<String, GlobalKey> roleSectionKeys) {
    return _buildSection(
      context,
      roleSectionKeys,
      'users',
      'Users',
      widget.users != null && widget.users!.isNotEmpty,
      () => _buildUsersContent(context),
    );
  }

  /// Builds the users content
  Widget _buildUsersContent(BuildContext context) {
    List<User> assignedUsers = _getAssignedUsers();

    if (assignedUsers.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: assignedUsers
            .map((user) => _buildMobileUserCard(context, user))
            .toList(),
      );
    }

    return _buildNoDataWidget('users');
  }

  /// Get users assigned to this role
  List<User> _getAssignedUsers() {
    if (widget.users == null || widget.users!.isEmpty) {
      return [];
    }

    return widget.users!.where((user) {
      if (user.roleAssignments != null) {
        final primaryRole = user.roleAssignments!.primaryRole;
        return primaryRole == widget.role.title ||
            primaryRole == widget.role.id;
      }
      return false;
    }).toList();
  }

  /// Builds a mobile-optimized user card
  Widget _buildMobileUserCard(BuildContext context, User user) {
    final userId = _getUserId(user);
    final isExpanded = _expandedUserIds.contains(userId);

    return Container(
      margin: EdgeInsets.only(
          bottom: _RoleDetailsPanelMobileConstants.cardMarginBottom),
      decoration: _buildUserCardDecoration(),
      child: Column(
        children: [
          _buildUserCardHeader(user, userId, isExpanded),
          if (isExpanded) _buildUserCardExpandedContent(user),
        ],
      ),
    );
  }

  /// Builds the decoration for user cards
  BoxDecoration _buildUserCardDecoration() {
    return BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(
          _RoleDetailsPanelMobileConstants.cardBorderRadius),
      border: Border.all(color: Colors.grey.shade300),
      boxShadow: [
        BoxShadow(
          color: Colors.grey.withValues(alpha: 0.1),
          spreadRadius: 1,
          blurRadius: 3,
          offset: Offset(0, 1),
        ),
      ],
    );
  }

  /// Builds the user card header
  Widget _buildUserCardHeader(User user, String userId, bool isExpanded) {
    return GestureDetector(
      onTap: () => _toggleUserExpansion(userId),
      child: Container(
        padding:
            EdgeInsets.all(_RoleDetailsPanelMobileConstants.userCardPadding),
        child: Row(
          children: [
            _buildUserAvatar(),
            SizedBox(width: _RoleDetailsPanelMobileConstants.spacingMedium),
            _buildUserInfo(user),
            _buildExpandIcon(isExpanded),
          ],
        ),
      ),
    );
  }

  /// Builds the user avatar
  Widget _buildUserAvatar() {
    return CircleAvatar(
      backgroundColor: Colors.blue.shade100,
      radius: _RoleDetailsPanelMobileConstants.avatarRadius,
      child: Icon(
        Icons.person,
        color: Colors.blue.shade700,
        size: _RoleDetailsPanelMobileConstants.avatarIconSize,
      ),
    );
  }

  /// Builds the user information section
  Widget _buildUserInfo(User user) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            user.name ?? 'Unknown User',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: _RoleDetailsPanelMobileConstants.contentFontSize,
              fontFamily: _RoleDetailsPanelMobileConstants.fontFamily,
            ),
          ),
          if (user.personalInformation?.email != null)
            Text(
              user.personalInformation!.email!,
              style: TextStyle(
                fontSize: _RoleDetailsPanelMobileConstants.chipFontSize,
                color: Colors.grey.shade600,
                fontFamily: _RoleDetailsPanelMobileConstants.fontFamily,
              ),
            ),
        ],
      ),
    );
  }

  /// Builds the expand/collapse icon
  Widget _buildExpandIcon(bool isExpanded) {
    return Icon(
      isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
      color: Colors.grey.shade600,
    );
  }

  /// Builds the expanded content for user cards
  Widget _buildUserCardExpandedContent(User user) {
    return Container(
      padding: EdgeInsets.fromLTRB(
        _RoleDetailsPanelMobileConstants.userCardPadding,
        0,
        _RoleDetailsPanelMobileConstants.userCardPadding,
        _RoleDetailsPanelMobileConstants.userCardPadding,
      ),
      child: _buildExpandedUserDetails(context, user),
    );
  }

  /// Get user ID for tracking expanded state
  String _getUserId(User user) {
    return user.name ?? 'unknown_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// Toggle user card expansion
  void _toggleUserExpansion(String userId) {
    setState(() {
      if (_expandedUserIds.contains(userId)) {
        _expandedUserIds.remove(userId);
      } else {
        _expandedUserIds.add(userId);
      }
    });
  }

  /// Builds expanded user details for mobile
  Widget _buildExpandedUserDetails(BuildContext context, User user) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (user.personalInformation?.phone != null)
            _buildUserDetailRow('Phone', user.personalInformation!.phone!),
          if (user.personalInformation?.employeeId != null)
            _buildUserDetailRow(
                'Employee ID', user.personalInformation!.employeeId!),
          if (user.department != null)
            _buildUserDetailRow('Department', user.department!),
          if (user.team != null) _buildUserDetailRow('Team', user.team!),
          if (user.roleAssignments?.primaryRole != null)
            _buildUserDetailRow(
                'Primary Role', user.roleAssignments!.primaryRole!),
        ],
      ),
    );
  }

  /// Builds a user detail row
  Widget _buildUserDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.grey.shade700,
                fontFamily: "TiemposText",
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 12,
                color: Colors.black87,
                fontFamily: "TiemposText",
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Legacy sections for backward compatibility
  Widget _buildUseCasesSection(
      BuildContext context, Map<String, GlobalKey> roleSectionKeys) {
    return _buildSection(
      context,
      roleSectionKeys,
      'useCases',
      'Use Cases',
      widget.role.useCases != null && widget.role.useCases!.isNotEmpty,
      () => _buildUseCasesContent(context),
    );
  }

  Widget _buildUseCasesContent(BuildContext context) {
    List<String> useCases = widget.role.useCases ?? [];

    if (useCases.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: useCases
            .map((useCase) => Container(
                  margin: EdgeInsets.only(bottom: 8),
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.purple.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.purple.shade100),
                  ),
                  child: GestureDetector(
                    onTap: () => _showChatDialog(context, 'use cases'),
                    child: Text(
                      useCase,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.black87,
                        fontFamily: "TiemposText",
                        height: 1.4,
                      ),
                    ),
                  ),
                ))
            .toList(),
      );
    }

    return _buildNoDataWidget('use cases');
  }

  Widget _buildPermissionsSection(
      BuildContext context, Map<String, GlobalKey> roleSectionKeys) {
    return _buildSection(
      context,
      roleSectionKeys,
      'permissions',
      'Permissions',
      widget.role.permissions != null && widget.role.permissions!.isNotEmpty,
      () => _buildPermissionsContent(context),
    );
  }

  Widget _buildPermissionsContent(BuildContext context) {
    if (widget.role.permissions != null &&
        widget.role.permissions!.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: widget.role.permissions!.entries.map((entry) {
          return Container(
            margin: EdgeInsets.only(bottom: 12),
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.red.shade100),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  entry.key.toUpperCase(),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                    fontFamily: "TiemposText",
                  ),
                ),
                SizedBox(height: 8),
                ...entry.value.map((permission) => Padding(
                      padding: EdgeInsets.only(bottom: 4),
                      child: GestureDetector(
                        onTap: () => _showChatDialog(context, 'permissions'),
                        child: Text(
                          '• $permission',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.black87,
                            fontFamily: "TiemposText",
                            height: 1.4,
                          ),
                        ),
                      ),
                    )),
              ],
            ),
          );
        }).toList(),
      );
    }

    return _buildNoDataWidget('permissions');
  }

  /// Show chat dialog (placeholder for now)
  void _showChatDialog(BuildContext context, String section) {
    // Placeholder for chat functionality
    // This would typically open a chat interface or send a message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Chat about $section'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  /// Show chat bottom sheet for role-related conversations
  void _showChatBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.8,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (context, scrollController) => CommentsBottomSheet(
          chatController: widget.chatController,
          onSendMessage: widget.onSendMessage,
          context: 'role: ${widget.role.title}',
        ),
      ),
    );
  }
}
