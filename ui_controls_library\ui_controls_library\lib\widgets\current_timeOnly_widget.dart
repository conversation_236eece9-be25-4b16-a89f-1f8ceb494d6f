import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'dart:async';
import 'dart:convert';
import 'package:ui_controls_library/utils/callback_interpreter.dart';

/// Format options for the time display
enum TimeFormat {
  /// Standard format (e.g., "3:45:30 PM" or "15:45:30")
  standard,

  /// Short format (e.g., "3:45 PM" or "15:45")
  short,

  /// Long format (e.g., "3 hours 45 minutes 30 seconds")
  long,

  /// Compact format (e.g., "3h 45m 30s")
  compact,

  /// Custom format using DateFormat pattern
  custom,
}

/// A comprehensive widget for displaying the current time.
///
/// This widget provides extensive customization options for displaying
/// the current time with various formats and styles.
class CurrentTimeOnlyWidget extends StatefulWidget {
  /// Format of the time display
  final TimeFormat format;

  /// Custom format pattern (used when format is TimeFormat.custom)
  final String? formatPattern;

  /// Whether to use 24-hour format (military time)
  final bool use24HourFormat;

  /// Whether to show seconds
  final bool showSeconds;

  /// Whether to show AM/PM indicator
  final bool showAmPm;

  /// Whether to automatically update the time
  final bool autoUpdate;

  /// Interval in seconds for auto-update
  final int updateIntervalSeconds;

  /// Locale for time formatting (e.g., 'en_US', 'fr_FR')
  final String locale;

  /// Custom text style
  final TextStyle? textStyle;

  /// Text color
  final Color textColor;

  /// Background color
  final Color backgroundColor;

  /// Font size
  final double fontSize;

  /// Font weight
  final FontWeight fontWeight;

  /// Font family
  final String? fontFamily;

  /// Text alignment
  final TextAlign textAlign;

  /// Whether to show a border
  final bool hasBorder;

  /// Border radius
  final double borderRadius;

  /// Border color
  final Color borderColor;

  /// Border width
  final double borderWidth;

  /// Whether to show a shadow
  final bool hasShadow;

  /// Shadow elevation
  final double elevation;

  /// Whether to use a compact layout
  final bool isCompact;

  /// Optional label text
  final String? label;

  /// Optional prefix text
  final String? prefix;

  /// Optional suffix text
  final String? suffix;

  /// Optional prefix icon
  final IconData? prefixIcon;

  /// Optional suffix icon
  final IconData? suffixIcon;

  /// Optional prefix icon color
  final Color? prefixIconColor;

  /// Optional suffix icon color
  final Color? suffixIconColor;

  /// Whether to show a clock icon
  final bool showClockIcon;

  /// Clock icon
  final IconData clockIcon;

  /// Clock icon color
  final Color? clockIconColor;

  /// Whether to use dark theme
  final bool isDarkTheme;

  /// Whether to animate the widget
  final bool isAnimated;

  /// Whether to use bold text
  final bool isBold;

  /// Whether to use italic text
  final bool isItalic;

  /// Whether to underline the text
  final bool isUnderlined;

  /// Whether to show a separator between hours, minutes, and seconds
  final bool showSeparator;

  /// Separator character (e.g., ":", "-", ".")
  final String separator;

  /// Whether to blink the separator
  final bool blinkSeparator;

  /// Whether to show the time zone
  final bool showTimeZone;

  /// Time zone to display
  final String? timeZone;

  /// Whether to use a digital style display
  final bool digitalStyle;

  /// Whether to show a helper text
  final bool showHelperText;

  /// Helper text
  final String? helperText;

  /// Helper text color
  final Color? helperTextColor;

  /// Width of the widget
  final double? width;

  /// Height of the widget
  final double? height;

  /// Padding around the time text
  final EdgeInsetsGeometry padding;

  /// Alignment of the time within its container
  final Alignment alignment;

  /// Whether to show a shadow under the text
  final bool showTextShadow;

  /// Shadow color for text
  final Color shadowColor;

  /// Shadow offset for text
  final Offset shadowOffset;

  /// Shadow blur radius for text
  final double shadowBlurRadius;

  /// Whether to use uppercase text
  final bool isUpperCase;

  /// Whether to use lowercase text
  final bool isLowerCase;

  /// Whether to capitalize the first letter of each word
  final bool isCapitalized;

  // Advanced interaction properties
  /// Callback for mouse hover
  ///
  /// This function is called when the mouse pointer enters or exits the widget.
  /// The parameter is true when the mouse enters and false when it exits.
  final void Function(bool)? onHover;

  /// Callback for keyboard focus
  ///
  /// This function is called when the widget gains or loses keyboard focus.
  /// The parameter is true when focus is gained and false when it is lost.
  final void Function(bool)? onFocus;

  /// Color when the widget is hovered
  final Color? hoverColor;

  /// Color when the widget is focused
  final Color? focusColor;

  /// Tooltip text to display on hover
  final String? tooltip;

  /// Whether to enable haptic/audio feedback
  ///
  /// If true, the widget will provide haptic and/or audio feedback when interacted with.
  final bool enableFeedback;

  /// Callback for tap gesture
  ///
  /// This function is called when the widget is tapped.
  final VoidCallback? onTap;

  /// Callback for double tap gesture
  ///
  /// This function is called when the widget is double-tapped.
  final VoidCallback? onDoubleTap;

  /// Callback for long press gesture
  ///
  /// This function is called when the widget is long-pressed.
  final VoidCallback? onLongPress;

  /// Focus node for controlling focus
  final FocusNode? focusNode;

  /// Whether the widget should autofocus
  final bool autofocus;

  /// Callback for time change
  ///
  /// This function is called when the time changes.
  final void Function(DateTime)? onTimeChanged;

  // JSON callback properties
  /// Dynamic callback definitions from JSON
  ///
  /// This can include callback definitions for various events:
  /// - onTimeChanged: Executed when the time changes
  /// - onTap: Executed when the widget is tapped
  /// - onDoubleTap: Executed when the widget is double-tapped
  /// - onLongPress: Executed when the widget is long-pressed
  /// - onHover: Executed when the mouse enters or exits the widget
  /// - onFocus: Executed when the widget gains or loses focus
  final Map<String, dynamic>? jsonCallbacks;

  /// Whether to use dynamic callbacks from JSON
  final bool useJsonCallbacks;

  /// State map for dynamic callbacks
  ///
  /// This map can be used by dynamic callbacks to store and retrieve state.
  final Map<String, dynamic>? callbackState;

  /// Custom handlers for dynamic callbacks
  ///
  /// This map contains custom handler functions that can be called by dynamic callbacks.
  final Map<String, Function>? customCallbackHandlers;

  // Advanced time formatting options
  /// Custom time formatter function
  ///
  /// This function allows for custom formatting of the time beyond what's possible
  /// with the standard format options.
  final String Function(DateTime)? customTimeFormatter;

  /// Whether to use a relative time format (e.g., "Just now", "A minute ago")
  final bool useRelativeTime;

  /// Map of special time formats for specific times
  ///
  /// This map allows for special formatting of specific times.
  /// The keys are time strings in the format "HH:mm:ss", and the values are
  /// the display strings for those times.
  final Map<String, String>? specialTimeFormats;

  /// Creates a current time only widget.
  const CurrentTimeOnlyWidget({
    super.key,
    this.format = TimeFormat.standard,
    this.formatPattern,
    this.use24HourFormat = false,
    this.showSeconds = true,
    this.showAmPm = true,
    this.autoUpdate = true,
    this.updateIntervalSeconds = 1,
    this.locale = 'en_US',
    this.textStyle,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.transparent,
    this.fontSize = 24.0,
    this.fontWeight = FontWeight.normal,
    this.fontFamily,
    this.textAlign = TextAlign.center,
    this.hasBorder = false,
    this.borderRadius = 8.0,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.isCompact = false,
    this.label,
    this.prefix,
    this.suffix,
    this.prefixIcon,
    this.suffixIcon,
    this.prefixIconColor,
    this.suffixIconColor,
    this.showClockIcon = false,
    this.clockIcon = Icons.access_time,
    this.clockIconColor,
    this.isDarkTheme = false,
    this.isAnimated = false,
    this.isBold = false,
    this.isItalic = false,
    this.isUnderlined = false,
    this.showSeparator = true,
    this.separator = ":",
    this.blinkSeparator = false,
    this.showTimeZone = false,
    this.timeZone,
    this.digitalStyle = false,
    this.showHelperText = false,
    this.helperText,
    this.helperTextColor,
    this.width,
    this.height,
    this.padding = const EdgeInsets.all(16.0),
    this.alignment = Alignment.center,
    this.showTextShadow = false,
    this.shadowColor = Colors.black26,
    this.shadowOffset = const Offset(2, 2),
    this.shadowBlurRadius = 4.0,
    this.isUpperCase = false,
    this.isLowerCase = false,
    this.isCapitalized = false,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.hoverColor,
    this.focusColor,
    this.tooltip,
    this.enableFeedback = true,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    this.focusNode,
    this.autofocus = false,
    this.onTimeChanged,
    // JSON callback properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    // Advanced time formatting options
    this.customTimeFormatter,
    this.useRelativeTime = false,
    this.specialTimeFormats,
  });

  /// Creates a CurrentTimeOnlyWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the CurrentTimeOnlyWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "format": "standard",
  ///   "use24HourFormat": true,
  ///   "showSeconds": true,
  ///   "textColor": "blue",
  ///   "fontSize": 24,
  ///   "hasBorder": true
  /// }
  /// ```
  factory CurrentTimeOnlyWidget.fromJson(Map<String, dynamic> json) {
    // Parse time format
    TimeFormat format = TimeFormat.standard;
    if (json['format'] != null) {
      switch (json['format'].toString().toLowerCase()) {
        case 'standard':
          format = TimeFormat.standard;
          break;
        case 'short':
          format = TimeFormat.short;
          break;
        case 'long':
          format = TimeFormat.long;
          break;
        case 'compact':
          format = TimeFormat.compact;
          break;
        case 'custom':
          format = TimeFormat.custom;
          break;
      }
    }

    // Parse text alignment
    TextAlign textAlign = TextAlign.center;
    if (json['textAlign'] != null) {
      switch (json['textAlign'].toString().toLowerCase()) {
        case 'center':
          textAlign = TextAlign.center;
          break;
        case 'left':
        case 'start':
          textAlign = TextAlign.left;
          break;
        case 'right':
        case 'end':
          textAlign = TextAlign.right;
          break;
        case 'justify':
          textAlign = TextAlign.justify;
          break;
      }
    }

    // Parse font weight
    FontWeight fontWeight = FontWeight.normal;
    if (json['fontWeight'] != null) {
      if (json['fontWeight'] == 'bold' || json['fontWeight'] == true) {
        fontWeight = FontWeight.bold;
      } else if (json['fontWeight'] == 'light') {
        fontWeight = FontWeight.w300;
      } else if (json['fontWeight'] is int) {
        final weight = json['fontWeight'] as int;
        switch (weight) {
          case 100:
            fontWeight = FontWeight.w100;
            break;
          case 200:
            fontWeight = FontWeight.w200;
            break;
          case 300:
            fontWeight = FontWeight.w300;
            break;
          case 400:
            fontWeight = FontWeight.w400;
            break;
          case 500:
            fontWeight = FontWeight.w500;
            break;
          case 600:
            fontWeight = FontWeight.w600;
            break;
          case 700:
            fontWeight = FontWeight.w700;
            break;
          case 800:
            fontWeight = FontWeight.w800;
            break;
          case 900:
            fontWeight = FontWeight.w900;
            break;
        }
      }
    }

    // Parse alignment
    Alignment alignment = Alignment.center;
    if (json['alignment'] != null) {
      switch (json['alignment'].toString().toLowerCase()) {
        case 'center':
          alignment = Alignment.center;
          break;
        case 'topleft':
        case 'top_left':
          alignment = Alignment.topLeft;
          break;
        case 'topright':
        case 'top_right':
          alignment = Alignment.topRight;
          break;
        case 'topcenter':
        case 'top_center':
        case 'top':
          alignment = Alignment.topCenter;
          break;
        case 'bottomleft':
        case 'bottom_left':
          alignment = Alignment.bottomLeft;
          break;
        case 'bottomright':
        case 'bottom_right':
          alignment = Alignment.bottomRight;
          break;
        case 'bottomcenter':
        case 'bottom_center':
        case 'bottom':
          alignment = Alignment.bottomCenter;
          break;
        case 'centerleft':
        case 'center_left':
        case 'left':
          alignment = Alignment.centerLeft;
          break;
        case 'centerright':
        case 'center_right':
        case 'right':
          alignment = Alignment.centerRight;
          break;
      }
    }

    // Parse padding
    EdgeInsetsGeometry padding = const EdgeInsets.all(16.0);
    if (json['padding'] != null) {
      if (json['padding'] is num) {
        final paddingValue = (json['padding'] as num).toDouble();
        padding = EdgeInsets.all(paddingValue);
      } else if (json['padding'] is Map) {
        final paddingMap = json['padding'] as Map;
        final left = (paddingMap['left'] as num?)?.toDouble() ?? 0.0;
        final top = (paddingMap['top'] as num?)?.toDouble() ?? 0.0;
        final right = (paddingMap['right'] as num?)?.toDouble() ?? 0.0;
        final bottom = (paddingMap['bottom'] as num?)?.toDouble() ?? 0.0;
        padding = EdgeInsets.fromLTRB(left, top, right, bottom);
      }
    }

    // Parse shadow offset
    Offset shadowOffset = const Offset(2, 2);
    if (json['shadowOffset'] != null && json['shadowOffset'] is Map) {
      final offsetMap = json['shadowOffset'] as Map;
      final dx = (offsetMap['dx'] as num?)?.toDouble() ?? 2.0;
      final dy = (offsetMap['dy'] as num?)?.toDouble() ?? 2.0;
      shadowOffset = Offset(dx, dy);
    }

    // Parse icons
    IconData? prefixIcon;
    if (json['prefixIcon'] != null) {
      prefixIcon = _getIconData(json['prefixIcon'].toString());
    }

    IconData? suffixIcon;
    if (json['suffixIcon'] != null) {
      suffixIcon = _getIconData(json['suffixIcon'].toString());
    }

    IconData clockIcon = Icons.access_time;
    if (json['clockIcon'] != null) {
      clockIcon = _getIconData(json['clockIcon'].toString()) ?? Icons.access_time;
    }

    // Parse colors
    Color? textColor;
    if (json['textColor'] != null) {
      textColor = _colorFromJson(json['textColor']);
    }

    Color? backgroundColor;
    if (json['backgroundColor'] != null) {
      backgroundColor = _colorFromJson(json['backgroundColor']);
    }

    Color? borderColor;
    if (json['borderColor'] != null) {
      borderColor = _colorFromJson(json['borderColor']);
    }

    Color? shadowColor;
    if (json['shadowColor'] != null) {
      shadowColor = _colorFromJson(json['shadowColor']);
    }

    Color? prefixIconColor;
    if (json['prefixIconColor'] != null) {
      prefixIconColor = _colorFromJson(json['prefixIconColor']);
    }

    Color? suffixIconColor;
    if (json['suffixIconColor'] != null) {
      suffixIconColor = _colorFromJson(json['suffixIconColor']);
    }

    Color? clockIconColor;
    if (json['clockIconColor'] != null) {
      clockIconColor = _colorFromJson(json['clockIconColor']);
    }

    Color? helperTextColor;
    if (json['helperTextColor'] != null) {
      helperTextColor = _colorFromJson(json['helperTextColor']);
    }

    Color? hoverColor;
    if (json['hoverColor'] != null) {
      hoverColor = _colorFromJson(json['hoverColor']);
    }

    Color? focusColor;
    if (json['focusColor'] != null) {
      focusColor = _colorFromJson(json['focusColor']);
    }

    // Parse JSON callback properties
    Map<String, dynamic>? jsonCallbacks;
    bool useJsonCallbacks = json['useJsonCallbacks'] as bool? ?? false;

    if (json['callbacks'] != null) {
      if (json['callbacks'] is Map) {
        jsonCallbacks = Map<String, dynamic>.from(json['callbacks'] as Map);
        useJsonCallbacks = true;
      } else if (json['callbacks'] is String) {
        try {
          jsonCallbacks = jsonDecode(json['callbacks'] as String) as Map<String, dynamic>;
          useJsonCallbacks = true;
        } catch (e) {
          // Silently handle the error
        }
      }
    }

    // Parse advanced time formatting options
    bool useRelativeTime = json['useRelativeTime'] as bool? ?? false;

    Map<String, String>? specialTimeFormats;
    if (json['specialTimeFormats'] != null && json['specialTimeFormats'] is Map) {
      specialTimeFormats = Map<String, String>.from(
        (json['specialTimeFormats'] as Map).map(
          (key, value) => MapEntry(key.toString(), value.toString())
        )
      );
    }

    return CurrentTimeOnlyWidget(
      format: format,
      formatPattern: json['formatPattern'] as String?,
      use24HourFormat: json['use24HourFormat'] as bool? ?? false,
      showSeconds: json['showSeconds'] as bool? ?? true,
      showAmPm: json['showAmPm'] as bool? ?? true,
      autoUpdate: json['autoUpdate'] as bool? ?? true,
      updateIntervalSeconds: json['updateIntervalSeconds'] as int? ?? 1,
      locale: json['locale'] as String? ?? 'en_US',
      textColor: textColor ?? Colors.black,
      backgroundColor: backgroundColor ?? Colors.transparent,
      fontSize: (json['fontSize'] as num?)?.toDouble() ?? 24.0,
      fontWeight: fontWeight,
      fontFamily: json['fontFamily'] as String?,
      textAlign: textAlign,
      hasBorder: json['hasBorder'] as bool? ?? false,
      borderRadius: (json['borderRadius'] as num?)?.toDouble() ?? 8.0,
      borderColor: borderColor ?? Colors.grey,
      borderWidth: (json['borderWidth'] as num?)?.toDouble() ?? 1.0,
      hasShadow: json['hasShadow'] as bool? ?? false,
      elevation: (json['elevation'] as num?)?.toDouble() ?? 2.0,
      isCompact: json['isCompact'] as bool? ?? false,
      label: json['label'] as String?,
      prefix: json['prefix'] as String?,
      suffix: json['suffix'] as String?,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      prefixIconColor: prefixIconColor,
      suffixIconColor: suffixIconColor,
      showClockIcon: json['showClockIcon'] as bool? ?? false,
      clockIcon: clockIcon,
      clockIconColor: clockIconColor,
      isDarkTheme: json['isDarkTheme'] as bool? ?? false,
      isAnimated: json['isAnimated'] as bool? ?? false,
      isBold: json['isBold'] as bool? ?? false,
      isItalic: json['isItalic'] as bool? ?? false,
      isUnderlined: json['isUnderlined'] as bool? ?? false,
      showSeparator: json['showSeparator'] as bool? ?? true,
      separator: json['separator'] as String? ?? ":",
      blinkSeparator: json['blinkSeparator'] as bool? ?? false,
      showTimeZone: json['showTimeZone'] as bool? ?? false,
      timeZone: json['timeZone'] as String?,
      digitalStyle: json['digitalStyle'] as bool? ?? false,
      showHelperText: json['showHelperText'] as bool? ?? false,
      helperText: json['helperText'] as String?,
      helperTextColor: helperTextColor,
      width: json['width'] != null ? (json['width'] as num).toDouble() : null,
      height: json['height'] != null ? (json['height'] as num).toDouble() : null,
      padding: padding,
      alignment: alignment,
      showTextShadow: json['showTextShadow'] as bool? ?? false,
      shadowColor: shadowColor ?? Colors.black26,
      shadowOffset: shadowOffset,
      shadowBlurRadius: (json['shadowBlurRadius'] as num?)?.toDouble() ?? 4.0,
      isUpperCase: json['isUpperCase'] as bool? ?? false,
      isLowerCase: json['isLowerCase'] as bool? ?? false,
      isCapitalized: json['isCapitalized'] as bool? ?? false,
      // Advanced interaction properties
      hoverColor: hoverColor,
      focusColor: focusColor,
      tooltip: json['tooltip'] as String?,
      enableFeedback: json['enableFeedback'] as bool? ?? true,
      autofocus: json['autofocus'] as bool? ?? false,
      // JSON callback properties
      jsonCallbacks: jsonCallbacks,
      useJsonCallbacks: useJsonCallbacks,
      callbackState: {},
      // Advanced time formatting options
      useRelativeTime: useRelativeTime,
      specialTimeFormats: specialTimeFormats,
    );
  }

  /// Converts a JSON color value to a Flutter Color
  ///
  /// Accepts hex strings (e.g., "#FF0000"), color names (e.g., "red"),
  /// or integer values (e.g., 0xFFFF0000)
  static Color? _colorFromJson(dynamic colorValue) {
    if (colorValue == null) return null;

    if (colorValue is String) {
      // Handle hex strings like "#FF0000"
      if (colorValue.startsWith('#')) {
        String hexColor = colorValue.substring(1);

        // Handle shorthand hex like #RGB
        if (hexColor.length == 3) {
          hexColor = hexColor.split('').map((c) => '$c$c').join('');
        }

        // Add alpha channel if missing
        if (hexColor.length == 6) {
          hexColor = 'FF$hexColor';
        }

        // Parse the hex value
        try {
          return Color(int.parse('0x$hexColor'));
        } catch (e) {
          // Silently handle the error and return null
          return null;
        }
      }

      // Handle named colors
      switch (colorValue.toLowerCase()) {
        case 'red': return Colors.red;
        case 'blue': return Colors.blue;
        case 'green': return Colors.green;
        case 'yellow': return Colors.yellow;
        case 'orange': return Colors.orange;
        case 'purple': return Colors.purple;
        case 'pink': return Colors.pink;
        case 'brown': return Colors.brown;
        case 'grey':
        case 'gray': return Colors.grey;
        case 'black': return Colors.black;
        case 'white': return Colors.white;
        case 'amber': return Colors.amber;
        case 'cyan': return Colors.cyan;
        case 'indigo': return Colors.indigo;
        case 'lime': return Colors.lime;
        case 'teal': return Colors.teal;
        default: return null;
      }
    } else if (colorValue is int) {
      // Handle integer color values
      return Color(colorValue);
    }

    return null;
  }

  /// Gets an IconData from a string name
  static IconData? _getIconData(String iconName) {
    switch (iconName.toLowerCase()) {
      case 'access_time':
      case 'clock':
        return Icons.access_time;
      case 'watch':
      case 'watch_later':
        return Icons.watch_later;
      case 'schedule':
        return Icons.schedule;
      case 'timer':
        return Icons.timer;
      case 'hourglass':
      case 'hourglass_empty':
        return Icons.hourglass_empty;
      case 'hourglass_full':
        return Icons.hourglass_full;
      case 'alarm':
        return Icons.alarm;
      case 'alarm_on':
        return Icons.alarm_on;
      case 'alarm_add':
        return Icons.alarm_add;
      case 'update':
        return Icons.update;
      case 'history':
        return Icons.history;
      case 'timelapse':
        return Icons.timelapse;
      case 'timer_3':
        return Icons.timer_3;
      case 'timer_10':
        return Icons.timer_10;
      case 'av_timer':
        return Icons.av_timer;
      case 'more_time':
        return Icons.more_time;
      default:
        return Icons.access_time; // Default icon
    }
  }

  /// Converts the CurrentTimeOnlyWidget to a JSON map
  ///
  /// This method allows for serializing the widget configuration to JSON,
  /// which can be useful for saving configurations or sharing them.
  Map<String, dynamic> toJson() {
    // Convert format to string
    String formatString;
    switch (format) {
      case TimeFormat.standard:
        formatString = 'standard';
        break;
      case TimeFormat.short:
        formatString = 'short';
        break;
      case TimeFormat.long:
        formatString = 'long';
        break;
      case TimeFormat.compact:
        formatString = 'compact';
        break;
      case TimeFormat.custom:
        formatString = 'custom';
        break;
    }

    // Convert text alignment to string
    String textAlignString;
    if (textAlign == TextAlign.left) {
      textAlignString = 'left';
    } else if (textAlign == TextAlign.right) {
      textAlignString = 'right';
    } else if (textAlign == TextAlign.justify) {
      textAlignString = 'justify';
    } else {
      textAlignString = 'center';
    }

    // Convert font weight to string
    String fontWeightString;
    if (fontWeight == FontWeight.bold) {
      fontWeightString = 'bold';
    } else if (fontWeight == FontWeight.w300) {
      fontWeightString = 'light';
    } else {
      fontWeightString = 'normal';
    }

    // Convert alignment to string
    String alignmentString;
    if (alignment == Alignment.topLeft) {
      alignmentString = 'topLeft';
    } else if (alignment == Alignment.topCenter) {
      alignmentString = 'topCenter';
    } else if (alignment == Alignment.topRight) {
      alignmentString = 'topRight';
    } else if (alignment == Alignment.centerLeft) {
      alignmentString = 'centerLeft';
    } else if (alignment == Alignment.centerRight) {
      alignmentString = 'centerRight';
    } else if (alignment == Alignment.bottomLeft) {
      alignmentString = 'bottomLeft';
    } else if (alignment == Alignment.bottomCenter) {
      alignmentString = 'bottomCenter';
    } else if (alignment == Alignment.bottomRight) {
      alignmentString = 'bottomRight';
    } else {
      alignmentString = 'center';
    }

    // Convert padding to map
    Map<String, dynamic> paddingMap;
    if (padding is EdgeInsets) {
      final edgeInsets = padding as EdgeInsets;
      paddingMap = {
        'left': edgeInsets.left,
        'top': edgeInsets.top,
        'right': edgeInsets.right,
        'bottom': edgeInsets.bottom,
      };
    } else {
      paddingMap = {'all': 16.0};
    }

    // Convert shadow offset to map
    final shadowOffsetMap = {
      'dx': shadowOffset.dx,
      'dy': shadowOffset.dy,
    };

    // Convert icons to strings
    String? prefixIconString;
    if (prefixIcon != null) {
      prefixIconString = _getIconName(prefixIcon!);
    }

    String? suffixIconString;
    if (suffixIcon != null) {
      suffixIconString = _getIconName(suffixIcon!);
    }

    String clockIconString = _getIconName(clockIcon);

    // Convert JSON callbacks to a serializable format
    Map<String, dynamic>? serializedCallbacks;
    if (jsonCallbacks != null) {
      serializedCallbacks = {};
      jsonCallbacks!.forEach((key, value) {
        if (value is Map) {
          serializedCallbacks![key] = Map<String, dynamic>.from(value);
        } else if (value is String) {
          serializedCallbacks![key] = value;
        } else {
          // Convert other types to string representation
          serializedCallbacks![key] = value.toString();
        }
      });
    }

    return {
      'format': formatString,
      if (formatPattern != null) 'formatPattern': formatPattern,
      'use24HourFormat': use24HourFormat,
      'showSeconds': showSeconds,
      'showAmPm': showAmPm,
      'autoUpdate': autoUpdate,
      'updateIntervalSeconds': updateIntervalSeconds,
      'locale': locale,
      'textColor': _colorToString(textColor),
      'backgroundColor': _colorToString(backgroundColor),
      'fontSize': fontSize,
      'fontWeight': fontWeightString,
      if (fontFamily != null) 'fontFamily': fontFamily,
      'textAlign': textAlignString,
      'hasBorder': hasBorder,
      'borderRadius': borderRadius,
      'borderColor': _colorToString(borderColor),
      'borderWidth': borderWidth,
      'hasShadow': hasShadow,
      'elevation': elevation,
      'isCompact': isCompact,
      if (label != null) 'label': label,
      if (prefix != null) 'prefix': prefix,
      if (suffix != null) 'suffix': suffix,
      if (prefixIconString != null) 'prefixIcon': prefixIconString,
      if (suffixIconString != null) 'suffixIcon': suffixIconString,
      if (prefixIconColor != null) 'prefixIconColor': _colorToString(prefixIconColor!),
      if (suffixIconColor != null) 'suffixIconColor': _colorToString(suffixIconColor!),
      'showClockIcon': showClockIcon,
      'clockIcon': clockIconString,
      if (clockIconColor != null) 'clockIconColor': _colorToString(clockIconColor!),
      'isDarkTheme': isDarkTheme,
      'isAnimated': isAnimated,
      'isBold': isBold,
      'isItalic': isItalic,
      'isUnderlined': isUnderlined,
      'showSeparator': showSeparator,
      'separator': separator,
      'blinkSeparator': blinkSeparator,
      'showTimeZone': showTimeZone,
      if (timeZone != null) 'timeZone': timeZone,
      'digitalStyle': digitalStyle,
      'showHelperText': showHelperText,
      if (helperText != null) 'helperText': helperText,
      if (helperTextColor != null) 'helperTextColor': _colorToString(helperTextColor!),
      if (width != null) 'width': width,
      if (height != null) 'height': height,
      'padding': paddingMap,
      'alignment': alignmentString,
      'showTextShadow': showTextShadow,
      'shadowColor': _colorToString(shadowColor),
      'shadowOffset': shadowOffsetMap,
      'shadowBlurRadius': shadowBlurRadius,
      'isUpperCase': isUpperCase,
      'isLowerCase': isLowerCase,
      'isCapitalized': isCapitalized,
      // Advanced interaction properties
      if (hoverColor != null) 'hoverColor': hoverColor.toString(),
      if (focusColor != null) 'focusColor': focusColor.toString(),
      if (tooltip != null) 'tooltip': tooltip,
      'enableFeedback': enableFeedback,
      'autofocus': autofocus,
      // JSON callback properties
      'useJsonCallbacks': useJsonCallbacks,
      if (serializedCallbacks != null) 'callbacks': serializedCallbacks,
      // Advanced time formatting options
      'useRelativeTime': useRelativeTime,
      if (specialTimeFormats != null) 'specialTimeFormats': specialTimeFormats,
    };
  }

  /// Converts a color to a string representation
  static String _colorToString(Color color) {
    // Handle standard colors by name for better readability
    if (color == Colors.red) return 'red';
    if (color == Colors.blue) return 'blue';
    if (color == Colors.green) return 'green';
    if (color == Colors.yellow) return 'yellow';
    if (color == Colors.orange) return 'orange';
    if (color == Colors.purple) return 'purple';
    if (color == Colors.pink) return 'pink';
    if (color == Colors.brown) return 'brown';
    if (color == Colors.grey) return 'grey';
    if (color == Colors.black) return 'black';
    if (color == Colors.white) return 'white';
    if (color == Colors.amber) return 'amber';
    if (color == Colors.cyan) return 'cyan';
    if (color == Colors.indigo) return 'indigo';
    if (color == Colors.lime) return 'lime';
    if (color == Colors.teal) return 'teal';

    // Convert to hex format for other colors
    return '#${color.value.toRadixString(16).padLeft(8, '0').substring(2)}';
  }

  /// Gets a string name from an IconData
  static String _getIconName(IconData icon) {
    if (icon == Icons.access_time) return 'access_time';
    if (icon == Icons.watch_later) return 'watch_later';
    if (icon == Icons.schedule) return 'schedule';
    if (icon == Icons.timer) return 'timer';
    if (icon == Icons.hourglass_empty) return 'hourglass_empty';
    if (icon == Icons.hourglass_full) return 'hourglass_full';
    if (icon == Icons.alarm) return 'alarm';
    if (icon == Icons.alarm_on) return 'alarm_on';
    if (icon == Icons.alarm_add) return 'alarm_add';
    if (icon == Icons.update) return 'update';
    if (icon == Icons.history) return 'history';
    if (icon == Icons.timelapse) return 'timelapse';
    if (icon == Icons.timer_3) return 'timer_3';
    if (icon == Icons.timer_10) return 'timer_10';
    if (icon == Icons.av_timer) return 'av_timer';
    if (icon == Icons.more_time) return 'more_time';

    return 'access_time'; // Default icon name
  }

  @override
  State<CurrentTimeOnlyWidget> createState() => _CurrentTimeOnlyWidgetState();
}

class _CurrentTimeOnlyWidgetState extends State<CurrentTimeOnlyWidget> with TickerProviderStateMixin {
  late DateTime _currentTime;
  String _formattedTime = '';
  bool _separatorVisible = true;
  Timer? _updateTimer;
  Timer? _blinkTimer;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  bool _isHovered = false;
  bool _hasFocus = false;
  late Map<String, dynamic> _callbackState;

  @override
  void initState() {
    super.initState();
    _currentTime = DateTime.now();
    _updateFormattedTime();

    // Set up animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.6,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    if (widget.isAnimated) {
      _animationController.repeat(reverse: true);
    }

    // Initialize callback state
    _callbackState = widget.callbackState != null
        ? Map<String, dynamic>.from(widget.callbackState!)
        : {};

    // Set up timer for auto-update
    if (widget.autoUpdate) {
      _updateTimer = Timer.periodic(
        Duration(seconds: widget.updateIntervalSeconds),
        (timer) {
          setState(() {
            _currentTime = DateTime.now();
            _updateFormattedTime();

            // Execute time changed callback if provided
            if (widget.onTimeChanged != null) {
              widget.onTimeChanged!(_currentTime);
            }

            // Execute dynamic callback if enabled
            if (widget.useJsonCallbacks &&
                widget.jsonCallbacks != null &&
                widget.jsonCallbacks!.containsKey('onTimeChanged')) {
              final callback = widget.jsonCallbacks!['onTimeChanged'];
              CallbackInterpreter.executeCallback(
                callback,
                context,
                value: _formattedTime,
                state: _callbackState,
                customHandlers: widget.customCallbackHandlers,
              );
            }
          });
        },
      );
    }

    // Set up timer for blinking separator
    if (widget.blinkSeparator && widget.showSeparator) {
      _blinkTimer = Timer.periodic(
        const Duration(milliseconds: 500),
        (timer) {
          setState(() {
            _separatorVisible = !_separatorVisible;
          });
        },
      );
    }
  }

  /// Handles hover state changes
  void _onHoverChange(bool isHovered) {
    setState(() {
      _isHovered = isHovered;

      // Call onHover callback if provided
      if (widget.onHover != null) {
        widget.onHover!(isHovered);
      }

      // Execute dynamic callback if enabled
      if (widget.useJsonCallbacks &&
          widget.jsonCallbacks != null &&
          widget.jsonCallbacks!.containsKey('onHover')) {
        final callback = widget.jsonCallbacks!['onHover'];
        CallbackInterpreter.executeCallback(
          callback,
          context,
          value: isHovered,
          state: _callbackState,
          customHandlers: widget.customCallbackHandlers,
        );
      }
    });
  }

  /// Handles focus state changes
  void _onFocusChange(bool hasFocus) {
    setState(() {
      _hasFocus = hasFocus;

      // Call onFocus callback if provided
      if (widget.onFocus != null) {
        widget.onFocus!(hasFocus);
      }

      // Execute dynamic callback if enabled
      if (widget.useJsonCallbacks &&
          widget.jsonCallbacks != null &&
          widget.jsonCallbacks!.containsKey('onFocus')) {
        final callback = widget.jsonCallbacks!['onFocus'];
        CallbackInterpreter.executeCallback(
          callback,
          context,
          value: hasFocus,
          state: _callbackState,
          customHandlers: widget.customCallbackHandlers,
        );
      }
    });
  }

  @override
  void dispose() {
    _updateTimer?.cancel();
    _blinkTimer?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  void _updateFormattedTime() {
    // Use custom time formatter if provided
    if (widget.customTimeFormatter != null) {
      _formattedTime = widget.customTimeFormatter!(_currentTime);
      return;
    }

    // Check for special time formats
    if (widget.specialTimeFormats != null) {
      final timeKey = '${_currentTime.hour.toString().padLeft(2, '0')}:${_currentTime.minute.toString().padLeft(2, '0')}:${_currentTime.second.toString().padLeft(2, '0')}';
      if (widget.specialTimeFormats!.containsKey(timeKey)) {
        _formattedTime = widget.specialTimeFormats![timeKey]!;
        return;
      }
    }

    // Use relative time if enabled
    if (widget.useRelativeTime) {
      final now = DateTime.now();
      final difference = now.difference(_currentTime);

      if (difference.inSeconds.abs() < 5) {
        _formattedTime = 'Just now';
        return;
      } else if (difference.inMinutes.abs() < 1) {
        _formattedTime = '${difference.inSeconds.abs()} seconds ${difference.isNegative ? 'from now' : 'ago'}';
        return;
      } else if (difference.inHours.abs() < 1) {
        _formattedTime = '${difference.inMinutes.abs()} minutes ${difference.isNegative ? 'from now' : 'ago'}';
        return;
      } else if (difference.inHours.abs() < 24) {
        _formattedTime = '${difference.inHours.abs()} hours ${difference.isNegative ? 'from now' : 'ago'}';
        return;
      }
    }

    String timeStr = '';

    // Format based on the selected format
    switch (widget.format) {
      case TimeFormat.standard:
        timeStr = _formatStandardTime();
        break;
      case TimeFormat.short:
        timeStr = _formatShortTime();
        break;
      case TimeFormat.long:
        timeStr = _formatLongTime();
        break;
      case TimeFormat.compact:
        timeStr = _formatCompactTime();
        break;
      case TimeFormat.custom:
        timeStr = _formatCustomTime();
        break;
    }

    // Apply text transformations
    if (widget.isUpperCase) {
      timeStr = timeStr.toUpperCase();
    } else if (widget.isLowerCase) {
      timeStr = timeStr.toLowerCase();
    } else if (widget.isCapitalized) {
      timeStr = timeStr.split(' ').map((word) =>
        word.isNotEmpty ? '${word[0].toUpperCase()}${word.substring(1)}' : ''
      ).join(' ');
    }

    _formattedTime = timeStr;
  }

  String _formatStandardTime() {
    final hour = _currentTime.hour;
    final minute = _currentTime.minute;
    final second = _currentTime.second;

    // Adjust hour for 12-hour format if needed
    final displayHour = widget.use24HourFormat ? hour : (hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour));

    // Determine AM/PM
    final amPm = hour < 12 ? 'AM' : 'PM';

    final hourStr = displayHour.toString();
    final minuteStr = minute.toString().padLeft(2, '0');
    final secondStr = second.toString().padLeft(2, '0');

    final separator = widget.showSeparator
        ? (widget.blinkSeparator ? (_separatorVisible ? widget.separator : ' ') : widget.separator)
        : ' ';

    String timeStr = '';
    if (widget.showSeconds) {
      timeStr = '$hourStr$separator$minuteStr$separator$secondStr';
    } else {
      timeStr = '$hourStr$separator$minuteStr';
    }

    if (!widget.use24HourFormat && widget.showAmPm) {
      timeStr += ' $amPm';
    }

    if (widget.showTimeZone && widget.timeZone != null) {
      timeStr += ' ${widget.timeZone}';
    }

    return timeStr;
  }

  String _formatShortTime() {
    final hour = _currentTime.hour;
    final minute = _currentTime.minute;

    // Adjust hour for 12-hour format if needed
    final displayHour = widget.use24HourFormat ? hour : (hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour));

    // Determine AM/PM
    final amPm = hour < 12 ? 'AM' : 'PM';

    final hourStr = displayHour.toString();
    final minuteStr = minute.toString().padLeft(2, '0');

    final separator = widget.showSeparator
        ? (widget.blinkSeparator ? (_separatorVisible ? widget.separator : ' ') : widget.separator)
        : ' ';

    String timeStr = '$hourStr$separator$minuteStr';

    if (!widget.use24HourFormat && widget.showAmPm) {
      timeStr += ' $amPm';
    }

    if (widget.showTimeZone && widget.timeZone != null) {
      timeStr += ' ${widget.timeZone}';
    }

    return timeStr;
  }

  String _formatLongTime() {
    final hour = _currentTime.hour;
    final minute = _currentTime.minute;
    final second = _currentTime.second;

    // Adjust hour for 12-hour format if needed
    final displayHour = widget.use24HourFormat ? hour : (hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour));

    // Determine AM/PM
    final amPm = hour < 12 ? 'AM' : 'PM';

    final hourStr = '$displayHour ${displayHour == 1 ? 'hour' : 'hours'}';
    final minuteStr = '$minute ${minute == 1 ? 'minute' : 'minutes'}';

    String timeStr = '$hourStr $minuteStr';

    if (widget.showSeconds) {
      final secondStr = '$second ${second == 1 ? 'second' : 'seconds'}';
      timeStr += ' $secondStr';
    }

    if (!widget.use24HourFormat && widget.showAmPm) {
      timeStr += ' $amPm';
    }

    if (widget.showTimeZone && widget.timeZone != null) {
      timeStr += ' ${widget.timeZone}';
    }

    return timeStr;
  }

  String _formatCompactTime() {
    final hour = _currentTime.hour;
    final minute = _currentTime.minute;
    final second = _currentTime.second;

    // Adjust hour for 12-hour format if needed
    final displayHour = widget.use24HourFormat ? hour : (hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour));

    // Determine AM/PM
    final amPm = hour < 12 ? 'AM' : 'PM';

    final hourStr = '${displayHour}h';
    final minuteStr = '${minute}m';

    String timeStr = '$hourStr $minuteStr';

    if (widget.showSeconds) {
      final secondStr = '${second}s';
      timeStr += ' $secondStr';
    }

    if (!widget.use24HourFormat && widget.showAmPm) {
      timeStr += ' $amPm';
    }

    if (widget.showTimeZone && widget.timeZone != null) {
      timeStr += ' ${widget.timeZone}';
    }

    return timeStr;
  }

  String _formatCustomTime() {
    if (widget.formatPattern != null) {
      try {
        return DateFormat(widget.formatPattern!, widget.locale).format(_currentTime);
      } catch (e) {
        return 'Invalid format';
      }
    }

    // Use IntlDateFormat for custom formatting
    String pattern = widget.use24HourFormat ? 'HH:mm' : 'h:mm';

    if (widget.showSeconds) {
      pattern += ':ss';
    }

    if (!widget.use24HourFormat && widget.showAmPm) {
      pattern += ' a';
    }

    if (widget.showTimeZone) {
      pattern += ' z';
    }

    try {
      return DateFormat(pattern, widget.locale).format(_currentTime);
    } catch (e) {
      return 'Invalid format';
    }
  }

  @override
  Widget build(BuildContext context) {
    // Apply dark theme if specified
    final effectiveTextColor = widget.isDarkTheme ? Colors.white : widget.textColor;
    final effectiveBackgroundColor = widget.isDarkTheme ? Colors.grey.shade800 : widget.backgroundColor;

    // Create text style
    TextStyle textStyle = widget.textStyle ??
        TextStyle(
          color: effectiveTextColor,
          fontSize: widget.fontSize,
          fontWeight: widget.isBold ? FontWeight.bold : widget.fontWeight,
          fontStyle: widget.isItalic ? FontStyle.italic : FontStyle.normal,
          fontFamily: widget.digitalStyle ? 'monospace' : widget.fontFamily,
          decoration: widget.isUnderlined ? TextDecoration.underline : TextDecoration.none,
          letterSpacing: widget.digitalStyle ? 2.0 : null,
          shadows: widget.showTextShadow
              ? [
                  Shadow(
                    color: widget.shadowColor,
                    offset: widget.shadowOffset,
                    blurRadius: widget.shadowBlurRadius,
                  ),
                ]
              : null,
        );

    // Create the main content widget
    Widget timeText = widget.isAnimated
        ? FadeTransition(
            opacity: _fadeAnimation,
            child: Text(
              _formattedTime,
              style: textStyle,
              textAlign: widget.textAlign,
            ),
          )
        : Text(
            _formattedTime,
            style: textStyle,
            textAlign: widget.textAlign,
          );

    // Create row with icons if needed
    Widget content = Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.prefixIcon != null) ...[
          Icon(
            widget.prefixIcon,
            size: widget.fontSize,
            color: widget.prefixIconColor ?? effectiveTextColor.withAlpha(179), // ~0.7 opacity
          ),
          const SizedBox(width: 8),
        ],
        if (widget.prefix != null) ...[
          Text(
            widget.prefix!,
            style: TextStyle(
              color: effectiveTextColor.withAlpha(179), // ~0.7 opacity
              fontSize: widget.fontSize,
            ),
          ),
          const SizedBox(width: 8),
        ],
        if (widget.showClockIcon) ...[
          Icon(
            widget.clockIcon,
            size: widget.fontSize,
            color: widget.clockIconColor ?? effectiveTextColor.withAlpha(179), // ~0.7 opacity
          ),
          const SizedBox(width: 8),
        ],
        Expanded(child: timeText),
        if (widget.suffix != null) ...[
          const SizedBox(width: 8),
          Text(
            widget.suffix!,
            style: TextStyle(
              color: effectiveTextColor.withAlpha(179), // ~0.7 opacity
              fontSize: widget.fontSize,
            ),
          ),
        ],
        if (widget.suffixIcon != null) ...[
          const SizedBox(width: 8),
          Icon(
            widget.suffixIcon,
            size: widget.fontSize,
            color: widget.suffixIconColor ?? effectiveTextColor.withAlpha(179), // ~0.7 opacity
          ),
        ],
      ],
    );

    // Apply container with border and shadow if needed
    Widget container = Container(
      width: widget.width,
      height: widget.height,
      padding: widget.padding,
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.hasBorder
            ? Border.all(
                color: widget.borderColor,
                width: widget.borderWidth,
              )
            : null,
        boxShadow: widget.hasShadow
            ? [
                BoxShadow(
                  color: Colors.black.withAlpha(51), // ~0.2 opacity
                  blurRadius: widget.elevation,
                  offset: Offset(0, widget.elevation / 2),
                ),
              ]
            : null,
      ),
      child: Align(
        alignment: widget.alignment,
        child: content,
      ),
    );

    // Build the final widget with label and helper text if needed
    // Apply tooltip if provided
    if (widget.tooltip != null) {
      container = Tooltip(
        message: widget.tooltip!,
        child: container,
      );
    }

    // Apply focus and hover handling
    container = MouseRegion(
      onEnter: (_) => _onHoverChange(true),
      onExit: (_) => _onHoverChange(false),
      cursor: SystemMouseCursors.click,
      child: Focus(
        focusNode: widget.focusNode,
        autofocus: widget.autofocus,
        onFocusChange: _onFocusChange,
        child: GestureDetector(
          onTap: () {
            // Execute tap callback if provided
            if (widget.onTap != null) {
              widget.onTap!();
            }

            // Execute dynamic callback if enabled
            if (widget.useJsonCallbacks &&
                widget.jsonCallbacks != null &&
                widget.jsonCallbacks!.containsKey('onTap')) {
              final callback = widget.jsonCallbacks!['onTap'];
              CallbackInterpreter.executeCallback(
                callback,
                context,
                value: _formattedTime,
                state: _callbackState,
                customHandlers: widget.customCallbackHandlers,
              );
            }
          },
          onDoubleTap: widget.onDoubleTap != null ||
                      (widget.useJsonCallbacks &&
                       widget.jsonCallbacks != null &&
                       widget.jsonCallbacks!.containsKey('onDoubleTap'))
              ? () {
                  // Execute standard callback if provided
                  if (widget.onDoubleTap != null) {
                    widget.onDoubleTap!();
                  }

                  // Execute dynamic callback if enabled
                  if (widget.useJsonCallbacks &&
                      widget.jsonCallbacks != null &&
                      widget.jsonCallbacks!.containsKey('onDoubleTap')) {
                    final callback = widget.jsonCallbacks!['onDoubleTap'];
                    CallbackInterpreter.executeCallback(
                      callback,
                      context,
                      value: _formattedTime,
                      state: _callbackState,
                      customHandlers: widget.customCallbackHandlers,
                    );
                  }
                }
              : null,
          onLongPress: widget.onLongPress != null ||
                      (widget.useJsonCallbacks &&
                       widget.jsonCallbacks != null &&
                       widget.jsonCallbacks!.containsKey('onLongPress'))
              ? () {
                  // Execute standard callback if provided
                  if (widget.onLongPress != null) {
                    widget.onLongPress!();
                  }

                  // Execute dynamic callback if enabled
                  if (widget.useJsonCallbacks &&
                      widget.jsonCallbacks != null &&
                      widget.jsonCallbacks!.containsKey('onLongPress')) {
                    final callback = widget.jsonCallbacks!['onLongPress'];
                    CallbackInterpreter.executeCallback(
                      callback,
                      context,
                      value: _formattedTime,
                      state: _callbackState,
                      customHandlers: widget.customCallbackHandlers,
                    );
                  }
                }
              : null,
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            decoration: BoxDecoration(
              color: _isHovered ?
                (widget.hoverColor ?? Colors.transparent.withAlpha(20)) :
                (_hasFocus ?
                  (widget.focusColor ?? Colors.transparent.withAlpha(30)) :
                  Colors.transparent),
              borderRadius: BorderRadius.circular(widget.borderRadius),
            ),
            child: container,
          ),
        ),
      ),
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.label != null) ...[
          Text(
            widget.label!,
            style: TextStyle(
              color: effectiveTextColor.withAlpha(179), // ~0.7 opacity
              fontSize: widget.fontSize * 0.8,
            ),
          ),
          const SizedBox(height: 4),
        ],
        container,
        if (widget.showHelperText && widget.helperText != null) ...[
          const SizedBox(height: 4),
          Text(
            widget.helperText!,
            style: TextStyle(
              color: widget.helperTextColor ?? effectiveTextColor.withAlpha(128), // ~0.5 opacity
              fontSize: widget.fontSize * 0.7,
            ),
          ),
        ],
      ],
    );
  }
}
