import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// A customizable timer widget with countdown functionality.
///
/// This widget provides a timer with various customization options
/// including duration, appearance, and behavior.
class TimerWidget extends StatefulWidget {
  /// Initial duration of the timer in seconds
  final int durationInSeconds;

  /// Whether the timer should start automatically
  final bool autoStart;

  /// Whether the timer should restart automatically when it reaches zero
  final bool autoRestart;

  /// Whether to show controls (start, pause, reset buttons)
  final bool showControls;

  /// Format of the timer display (e.g., "mm:ss", "hh:mm:ss")
  final TimerFormat format;

  /// Size of the timer text
  final double fontSize;

  /// Font weight of the timer text
  final FontWeight fontWeight;

  /// Color of the timer text
  final Color textColor;

  /// Background color of the timer
  final Color backgroundColor;

  /// Border color of the timer
  final Color? borderColor;

  /// Border width of the timer
  final double borderWidth;

  /// Border radius of the timer
  final double borderRadius;

  /// Width of the timer widget
  final double? width;

  /// Height of the timer widget
  final double? height;

  /// Padding around the timer text
  final EdgeInsetsGeometry padding;

  /// Alignment of the timer within its container
  final Alignment alignment;

  /// Color of the start button
  final Color startButtonColor;

  /// Color of the pause button
  final Color pauseButtonColor;

  /// Color of the reset button
  final Color resetButtonColor;

  /// Icon for the start button
  final IconData startIcon;

  /// Icon for the pause button
  final IconData pauseIcon;

  /// Icon for the reset button
  final IconData resetIcon;

  /// Whether to show a progress indicator
  final bool showProgressIndicator;

  /// Color of the progress indicator
  final Color progressIndicatorColor;

  /// Background color of the progress indicator
  final Color progressIndicatorBackgroundColor;

  /// Thickness of the progress indicator
  final double progressIndicatorThickness;

  /// Callback when the timer starts
  final VoidCallback? onStart;

  /// Callback when the timer pauses
  final VoidCallback? onPause;

  /// Callback when the timer resets
  final VoidCallback? onReset;

  /// Callback when the timer completes
  final VoidCallback? onComplete;

  /// Callback when the timer ticks (updates)
  final Function(int)? onTick;

  // Advanced Interaction Properties

  /// Callback for mouse hover
  ///
  /// This function is called when the mouse pointer enters or exits the widget.
  /// The parameter is true when the mouse enters and false when it exits.
  final void Function(bool)? onHover;

  /// Callback for keyboard focus
  ///
  /// This function is called when the widget gains or loses keyboard focus.
  /// The parameter is true when focus is gained and false when it is lost.
  final void Function(bool)? onFocus;

  /// Focus node for manual focus control
  ///
  /// This allows for programmatic control of the widget's focus state.
  /// If null, the widget will create and manage its own focus node.
  final FocusNode? focusNode;

  /// Whether the widget should be focused automatically when it appears
  ///
  /// If true, the widget will request focus when it is first built.
  final bool autofocus;

  /// Color when the widget is hovered
  final Color? hoverColor;

  /// Color when the widget is focused
  final Color? focusColor;

  /// Tooltip text to display on hover
  final String? tooltip;

  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Whether to enable haptic/audio feedback
  ///
  /// If true, the widget will provide haptic and/or audio feedback when interacted with.
  final bool enableFeedback;

  /// Callback for tap gesture
  ///
  /// This function is called when the widget is tapped.
  final VoidCallback? onTap;

  /// Callback for double tap gesture
  ///
  /// This function is called when the widget is double-tapped.
  final VoidCallback? onDoubleTap;

  /// Callback for long press gesture
  ///
  /// This function is called when the widget is long-pressed.
  final VoidCallback? onLongPress;



  const TimerWidget({
    super.key,
    this.durationInSeconds = 60,
    this.autoStart = false,
    this.autoRestart = false,
    this.showControls = true,
    this.format = TimerFormat.minutesSeconds,
    this.fontSize = 32.0,
    this.fontWeight = FontWeight.bold,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor,
    this.borderWidth = 1.0,
    this.borderRadius = 8.0,
    this.width,
    this.height,
    this.padding = const EdgeInsets.all(16.0),
    this.alignment = Alignment.center,
    this.startButtonColor = Colors.green,
    this.pauseButtonColor = Colors.orange,
    this.resetButtonColor = Colors.red,
    this.startIcon = Icons.play_arrow,
    this.pauseIcon = Icons.pause,
    this.resetIcon = Icons.refresh,
    this.showProgressIndicator = true,
    this.progressIndicatorColor = Colors.blue,
    this.progressIndicatorBackgroundColor = Colors.grey,
    this.progressIndicatorThickness = 4.0,
    this.onStart,
    this.onPause,
    this.onReset,
    this.onComplete,
    this.onTick,
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
    this.hoverColor,
    this.focusColor,
    this.tooltip,
    this.semanticsLabel,
    this.enableFeedback = true,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
  });

  /// Creates a TimerWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the TimerWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "durationInSeconds": 120,
  ///   "autoStart": true,
  ///   "format": "minutesSeconds",
  ///   "textColor": "white",
  ///   "backgroundColor": "black"
  /// }
  /// ```
  factory TimerWidget.fromJson(Map<String, dynamic> json) {
    // Parse colors
    Color? parseColor(dynamic colorValue) {
      if (colorValue == null) return null;

      if (colorValue is String) {
        // Handle named colors
        switch (colorValue.toLowerCase()) {
          case 'red': return Colors.red;
          case 'blue': return Colors.blue;
          case 'green': return Colors.green;
          case 'yellow': return Colors.yellow;
          case 'orange': return Colors.orange;
          case 'purple': return Colors.purple;
          case 'pink': return Colors.pink;
          case 'brown': return Colors.brown;
          case 'grey':
          case 'gray': return Colors.grey;
          case 'black': return Colors.black;
          case 'white': return Colors.white;
          case 'transparent': return Colors.transparent;
          case 'teal': return Colors.teal;
          case 'cyan': return Colors.cyan;
          case 'amber': return Colors.amber;
          case 'indigo': return Colors.indigo;
          case 'lime': return Colors.lime;
          default:
            // Handle hex colors
            if (colorValue.startsWith('#')) {
              try {
                final hexColor = colorValue.substring(1);
                final hexValue = int.parse('0xFF${hexColor.padRight(8, 'F').substring(0, 8)}');
                return Color(hexValue);
              } catch (e) {
                return null;
              }
            }
            return null;
        }
      } else if (colorValue is int) {
        return Color(colorValue);
      }

      return null;
    }

    // Parse font weight
    FontWeight parseFontWeight(dynamic weightValue) {
      if (weightValue == null) return FontWeight.bold;

      if (weightValue is String) {
        switch (weightValue.toLowerCase()) {
          case 'bold': return FontWeight.bold;
          case 'normal': return FontWeight.normal;
          case 'light': return FontWeight.w300;
          case 'thin': return FontWeight.w200;
          case 'medium': return FontWeight.w500;
          case 'semibold': return FontWeight.w600;
          case 'extrabold': return FontWeight.w800;
          case 'black': return FontWeight.w900;
          default: return FontWeight.bold;
        }
      } else if (weightValue is int) {
        switch (weightValue) {
          case 100: return FontWeight.w100;
          case 200: return FontWeight.w200;
          case 300: return FontWeight.w300;
          case 400: return FontWeight.w400;
          case 500: return FontWeight.w500;
          case 600: return FontWeight.w600;
          case 700: return FontWeight.w700;
          case 800: return FontWeight.w800;
          case 900: return FontWeight.w900;
          default: return FontWeight.bold;
        }
      }

      return FontWeight.bold;
    }

    // Parse alignment
    Alignment parseAlignment(dynamic alignmentValue) {
      if (alignmentValue == null) return Alignment.center;

      if (alignmentValue is String) {
        switch (alignmentValue.toLowerCase()) {
          case 'center': return Alignment.center;
          case 'topleft':
          case 'top_left': return Alignment.topLeft;
          case 'topright':
          case 'top_right': return Alignment.topRight;
          case 'bottomleft':
          case 'bottom_left': return Alignment.bottomLeft;
          case 'bottomright':
          case 'bottom_right': return Alignment.bottomRight;
          case 'top':
          case 'topcenter':
          case 'top_center': return Alignment.topCenter;
          case 'bottom':
          case 'bottomcenter':
          case 'bottom_center': return Alignment.bottomCenter;
          case 'left':
          case 'centerleft':
          case 'center_left': return Alignment.centerLeft;
          case 'right':
          case 'centerright':
          case 'center_right': return Alignment.centerRight;
          default: return Alignment.center;
        }
      }

      return Alignment.center;
    }

    // Parse icon data
    IconData parseIconData(String? iconName, IconData defaultIcon) {
      if (iconName == null) return defaultIcon;

      switch (iconName.toLowerCase()) {
        case 'play':
        case 'play_arrow': return Icons.play_arrow;
        case 'pause': return Icons.pause;
        case 'stop': return Icons.stop;
        case 'refresh':
        case 'reset': return Icons.refresh;
        case 'restart': return Icons.restart_alt;
        case 'skip': return Icons.skip_next;
        case 'back': return Icons.skip_previous;
        case 'forward': return Icons.fast_forward;
        case 'rewind': return Icons.fast_rewind;
        case 'timer': return Icons.timer;
        case 'alarm': return Icons.alarm;
        case 'clock': return Icons.access_time;
        case 'hourglass': return Icons.hourglass_empty;
        default: return defaultIcon;
      }
    }

    // Parse timer format
    TimerFormat parseFormat(dynamic formatValue) {
      if (formatValue == null) return TimerFormat.minutesSeconds;

      if (formatValue is String) {
        switch (formatValue.toLowerCase()) {
          case 'minutesseconds':
          case 'minutes_seconds':
          case 'mm:ss': return TimerFormat.minutesSeconds;
          case 'hoursminutesseconds':
          case 'hours_minutes_seconds':
          case 'hh:mm:ss': return TimerFormat.hoursMinutesSeconds;
          case 'secondsonly':
          case 'seconds_only':
          case 'seconds': return TimerFormat.secondsOnly;
          case 'digitalclock':
          case 'digital_clock':
          case 'digital':
          case 'clock': return TimerFormat.digitalClock;
          case 'countdown':
          case 'text':
          case 'verbose': return TimerFormat.countdown;
          default: return TimerFormat.minutesSeconds;
        }
      } else if (formatValue is int) {
        switch (formatValue) {
          case 0: return TimerFormat.minutesSeconds;
          case 1: return TimerFormat.hoursMinutesSeconds;
          case 2: return TimerFormat.secondsOnly;
          case 3: return TimerFormat.digitalClock;
          case 4: return TimerFormat.countdown;
          default: return TimerFormat.minutesSeconds;
        }
      }

      return TimerFormat.minutesSeconds;
    }

    // Parse padding
    EdgeInsetsGeometry parsePadding(dynamic paddingValue) {
      if (paddingValue == null) return const EdgeInsets.all(16.0);

      if (paddingValue is num) {
        return EdgeInsets.all(paddingValue.toDouble());
      } else if (paddingValue is Map<String, dynamic>) {
        final left = (paddingValue['left'] as num?)?.toDouble() ?? 0.0;
        final top = (paddingValue['top'] as num?)?.toDouble() ?? 0.0;
        final right = (paddingValue['right'] as num?)?.toDouble() ?? 0.0;
        final bottom = (paddingValue['bottom'] as num?)?.toDouble() ?? 0.0;

        if (left == right && top == bottom && left == top) {
          return EdgeInsets.all(left);
        } else if (left == right && top == bottom) {
          return EdgeInsets.symmetric(horizontal: left, vertical: top);
        } else {
          return EdgeInsets.fromLTRB(left, top, right, bottom);
        }
      } else if (paddingValue is String) {
        switch (paddingValue.toLowerCase()) {
          case 'none':
          case 'zero': return EdgeInsets.zero;
          case 'small': return const EdgeInsets.all(8.0);
          case 'medium': return const EdgeInsets.all(16.0);
          case 'large': return const EdgeInsets.all(24.0);
          default: return const EdgeInsets.all(16.0);
        }
      }

      return const EdgeInsets.all(16.0);
    }

    return TimerWidget(
      durationInSeconds: json['durationInSeconds'] as int? ?? 60,
      autoStart: json['autoStart'] as bool? ?? false,
      autoRestart: json['autoRestart'] as bool? ?? false,
      showControls: json['showControls'] as bool? ?? true,
      format: parseFormat(json['format']),
      fontSize: (json['fontSize'] as num?)?.toDouble() ?? 32.0,
      fontWeight: parseFontWeight(json['fontWeight']),
      textColor: parseColor(json['textColor']) ?? Colors.black,
      backgroundColor: parseColor(json['backgroundColor']) ?? Colors.white,
      borderColor: parseColor(json['borderColor']),
      borderWidth: (json['borderWidth'] as num?)?.toDouble() ?? 1.0,
      borderRadius: (json['borderRadius'] as num?)?.toDouble() ?? 8.0,
      width: (json['width'] as num?)?.toDouble(),
      height: (json['height'] as num?)?.toDouble(),
      padding: parsePadding(json['padding']),
      alignment: parseAlignment(json['alignment']),
      startButtonColor: parseColor(json['startButtonColor']) ?? Colors.green,
      pauseButtonColor: parseColor(json['pauseButtonColor']) ?? Colors.orange,
      resetButtonColor: parseColor(json['resetButtonColor']) ?? Colors.red,
      startIcon: parseIconData(json['startIcon'] as String?, Icons.play_arrow),
      pauseIcon: parseIconData(json['pauseIcon'] as String?, Icons.pause),
      resetIcon: parseIconData(json['resetIcon'] as String?, Icons.refresh),
      showProgressIndicator: json['showProgressIndicator'] as bool? ?? true,
      progressIndicatorColor: parseColor(json['progressIndicatorColor']) ?? Colors.blue,
      progressIndicatorBackgroundColor: parseColor(json['progressIndicatorBackgroundColor']) ?? Colors.grey,
      progressIndicatorThickness: (json['progressIndicatorThickness'] as num?)?.toDouble() ?? 4.0,
      onStart: json['onStart'] == true ? () {
        debugPrint('Timer started');
      } : null,
      onPause: json['onPause'] == true ? () {
        debugPrint('Timer paused');
      } : null,
      onReset: json['onReset'] == true ? () {
        debugPrint('Timer reset');
      } : null,
      onComplete: json['onComplete'] == true ? () {
        debugPrint('Timer completed');
      } : null,
      onTick: json['onTick'] == true ? (seconds) {
        debugPrint('Timer tick: $seconds seconds remaining');
      } : null,
      onHover: json['onHover'] == true ? (isHovered) {
        debugPrint('Timer hover: $isHovered');
      } : null,
      onFocus: json['onFocus'] == true ? (isFocused) {
        debugPrint('Timer focus: $isFocused');
      } : null,
      focusNode: null, // Cannot be created from JSON
      autofocus: json['autofocus'] as bool? ?? false,
      hoverColor: parseColor(json['hoverColor']),
      focusColor: parseColor(json['focusColor']),
      tooltip: json['tooltip'] as String?,
      semanticsLabel: json['semanticsLabel'] as String?,
      enableFeedback: json['enableFeedback'] as bool? ?? true,
      onTap: json['onTap'] == true ? () {
        debugPrint('Timer tapped');
      } : null,
      onDoubleTap: json['onDoubleTap'] == true ? () {
        debugPrint('Timer double-tapped');
      } : null,
      onLongPress: json['onLongPress'] == true ? () {
        debugPrint('Timer long-pressed');
      } : null,
    );
  }

  /// Converts the TimerWidget to a JSON map
  ///
  /// This method allows for serializing the widget configuration to JSON,
  /// which can be useful for saving configurations or sharing them.
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {
      'durationInSeconds': durationInSeconds,
      'autoStart': autoStart,
      'autoRestart': autoRestart,
      'showControls': showControls,
      'format': _formatToString(format),
      'fontSize': fontSize,
      'fontWeight': _fontWeightToString(fontWeight),
      'textColor': _colorToString(textColor),
      'backgroundColor': _colorToString(backgroundColor),
      'borderWidth': borderWidth,
      'borderRadius': borderRadius,
      'alignment': _alignmentToString(alignment),
      'startButtonColor': _colorToString(startButtonColor),
      'pauseButtonColor': _colorToString(pauseButtonColor),
      'resetButtonColor': _colorToString(resetButtonColor),
      'startIcon': _iconDataToString(startIcon),
      'pauseIcon': _iconDataToString(pauseIcon),
      'resetIcon': _iconDataToString(resetIcon),
      'showProgressIndicator': showProgressIndicator,
      'progressIndicatorColor': _colorToString(progressIndicatorColor),
      'progressIndicatorBackgroundColor': _colorToString(progressIndicatorBackgroundColor),
      'progressIndicatorThickness': progressIndicatorThickness,
      'autofocus': autofocus,
      'enableFeedback': enableFeedback,
    };

    // Add optional properties
    if (borderColor != null) json['borderColor'] = _colorToString(borderColor!);
    if (width != null) json['width'] = width;
    if (height != null) json['height'] = height;
    if (hoverColor != null) json['hoverColor'] = _colorToString(hoverColor!);
    if (focusColor != null) json['focusColor'] = _colorToString(focusColor!);
    if (tooltip != null) json['tooltip'] = tooltip;
    if (semanticsLabel != null) json['semanticsLabel'] = semanticsLabel;

    // Add padding
    if (padding is EdgeInsets) {
      final EdgeInsets p = padding as EdgeInsets;
      if (p.left == p.right && p.top == p.bottom && p.left == p.top) {
        json['padding'] = p.left;
      } else {
        json['padding'] = {
          'left': p.left,
          'top': p.top,
          'right': p.right,
          'bottom': p.bottom,
        };
      }
    }

    // Add callback flags
    if (onStart != null) json['onStart'] = true;
    if (onPause != null) json['onPause'] = true;
    if (onReset != null) json['onReset'] = true;
    if (onComplete != null) json['onComplete'] = true;
    if (onTick != null) json['onTick'] = true;
    if (onHover != null) json['onHover'] = true;
    if (onFocus != null) json['onFocus'] = true;
    if (onTap != null) json['onTap'] = true;
    if (onDoubleTap != null) json['onDoubleTap'] = true;
    if (onLongPress != null) json['onLongPress'] = true;

    return json;
  }

  /// Helper method to convert a Color to a string
  static String _colorToString(Color color) {
    // Handle standard colors by name for better readability
    if (color == Colors.red) return 'red';
    if (color == Colors.blue) return 'blue';
    if (color == Colors.green) return 'green';
    if (color == Colors.yellow) return 'yellow';
    if (color == Colors.orange) return 'orange';
    if (color == Colors.purple) return 'purple';
    if (color == Colors.pink) return 'pink';
    if (color == Colors.brown) return 'brown';
    if (color == Colors.grey) return 'grey';
    if (color == Colors.black) return 'black';
    if (color == Colors.white) return 'white';
    if (color == Colors.transparent) return 'transparent';
    if (color == Colors.teal) return 'teal';
    if (color == Colors.cyan) return 'cyan';
    if (color == Colors.amber) return 'amber';
    if (color == Colors.indigo) return 'indigo';
    if (color == Colors.lime) return 'lime';

    // Convert to hex string
    final int colorValue = color.toARGB32();
    final r = ((colorValue >> 16) & 0xFF).toRadixString(16).padLeft(2, '0');
    final g = ((colorValue >> 8) & 0xFF).toRadixString(16).padLeft(2, '0');
    final b = (colorValue & 0xFF).toRadixString(16).padLeft(2, '0');
    return '#$r$g$b';
  }

  /// Helper method to convert a FontWeight to a string
  static String _fontWeightToString(FontWeight weight) {
    if (weight == FontWeight.bold) return 'bold';
    if (weight == FontWeight.normal) return 'normal';
    if (weight == FontWeight.w100) return '100';
    if (weight == FontWeight.w200) return '200';
    if (weight == FontWeight.w300) return 'light';
    if (weight == FontWeight.w400) return 'normal';
    if (weight == FontWeight.w500) return 'medium';
    if (weight == FontWeight.w600) return 'semibold';
    if (weight == FontWeight.w700) return 'bold';
    if (weight == FontWeight.w800) return 'extrabold';
    if (weight == FontWeight.w900) return 'black';

    return 'bold';
  }

  /// Helper method to convert a TimerFormat to a string
  static String _formatToString(TimerFormat format) {
    switch (format) {
      case TimerFormat.minutesSeconds:
        return 'minutesSeconds';
      case TimerFormat.hoursMinutesSeconds:
        return 'hoursMinutesSeconds';
      case TimerFormat.secondsOnly:
        return 'secondsOnly';
      case TimerFormat.digitalClock:
        return 'digitalClock';
      case TimerFormat.countdown:
        return 'countdown';
    }
  }

  /// Helper method to convert an Alignment to a string
  static String _alignmentToString(Alignment alignment) {
    if (alignment == Alignment.center) return 'center';
    if (alignment == Alignment.topLeft) return 'topLeft';
    if (alignment == Alignment.topCenter) return 'top';
    if (alignment == Alignment.topRight) return 'topRight';
    if (alignment == Alignment.centerLeft) return 'left';
    if (alignment == Alignment.centerRight) return 'right';
    if (alignment == Alignment.bottomLeft) return 'bottomLeft';
    if (alignment == Alignment.bottomCenter) return 'bottom';
    if (alignment == Alignment.bottomRight) return 'bottomRight';

    return 'center';
  }

  /// Helper method to convert an IconData to a string
  static String _iconDataToString(IconData icon) {
    if (icon == Icons.play_arrow) return 'play';
    if (icon == Icons.pause) return 'pause';
    if (icon == Icons.refresh) return 'refresh';
    if (icon == Icons.stop) return 'stop';
    if (icon == Icons.restart_alt) return 'restart';
    if (icon == Icons.skip_next) return 'skip';
    if (icon == Icons.skip_previous) return 'back';
    if (icon == Icons.fast_forward) return 'forward';
    if (icon == Icons.fast_rewind) return 'rewind';
    if (icon == Icons.timer) return 'timer';
    if (icon == Icons.alarm) return 'alarm';
    if (icon == Icons.access_time) return 'clock';
    if (icon == Icons.hourglass_empty) return 'hourglass';

    return 'custom';
  }

  @override
  State<TimerWidget> createState() => _TimerWidgetState();
}

/// Format options for the timer display
enum TimerFormat {
  /// Display as minutes and seconds (mm:ss)
  minutesSeconds,

  /// Display as hours, minutes, and seconds (hh:mm:ss)
  hoursMinutesSeconds,

  /// Display as seconds only
  secondsOnly,

  /// Display as a digital clock (hh:mm:ss)
  digitalClock,

  /// Display as a countdown (e.g., "1 minute 30 seconds")
  countdown,
}

class _TimerWidgetState extends State<TimerWidget> {
  late int _remainingSeconds;
  Timer? _timer;
  bool _isRunning = false;
  double _progress = 1.0;
  bool _isHovered = false;
  bool _isFocused = false;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _remainingSeconds = widget.durationInSeconds;

    // Initialize focus node
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_handleFocusChange);

    // Request focus if autofocus is enabled
    if (widget.autofocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }

    if (widget.autoStart) {
      _startTimer();
    }
  }

  // Handle focus changes
  void _handleFocusChange() {
    if (_focusNode.hasFocus != _isFocused) {
      setState(() {
        _isFocused = _focusNode.hasFocus;
      });

      if (widget.onFocus != null) {
        widget.onFocus!(_isFocused);
      }
    }
  }

  // Handle hover changes
  void _handleHoverChange(bool isHovered) {
    if (_isHovered != isHovered) {
      setState(() {
        _isHovered = isHovered;
      });

      if (widget.onHover != null) {
        widget.onHover!(isHovered);
      }
    }
  }

  @override
  void dispose() {
    _timer?.cancel();

    // Clean up focus node
    if (widget.focusNode == null) {
      // Only dispose the focus node if we created it
      _focusNode.removeListener(_handleFocusChange);
      _focusNode.dispose();
    } else {
      // Just remove our listener
      _focusNode.removeListener(_handleFocusChange);
    }

    super.dispose();
  }

  void _startTimer() {
    if (_isRunning) return;

    _isRunning = true;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_remainingSeconds > 0) {
          _remainingSeconds--;
          _progress = _remainingSeconds / widget.durationInSeconds;

          if (widget.onTick != null) {
            widget.onTick!(_remainingSeconds);
          }
        } else {
          _timer?.cancel();
          _isRunning = false;

          if (widget.onComplete != null) {
            widget.onComplete!();
          }

          if (widget.autoRestart) {
            _remainingSeconds = widget.durationInSeconds;
            _progress = 1.0;
            _startTimer();
          }
        }
      });
    });

    if (widget.onStart != null) {
      widget.onStart!();
    }
  }

  void _pauseTimer() {
    if (!_isRunning) return;

    _timer?.cancel();
    _isRunning = false;

    if (widget.onPause != null) {
      widget.onPause!();
    }
  }

  void _resetTimer() {
    _timer?.cancel();

    setState(() {
      _remainingSeconds = widget.durationInSeconds;
      _isRunning = false;
      _progress = 1.0;
    });

    if (widget.onReset != null) {
      widget.onReset!();
    }
  }

  String _formatTime() {
    final hours = _remainingSeconds ~/ 3600;
    final minutes = (_remainingSeconds % 3600) ~/ 60;
    final seconds = _remainingSeconds % 60;

    switch (widget.format) {
      case TimerFormat.minutesSeconds:
        return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';

      case TimerFormat.hoursMinutesSeconds:
        return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';

      case TimerFormat.secondsOnly:
        return _remainingSeconds.toString();

      case TimerFormat.digitalClock:
        if (hours > 0) {
          return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
        } else {
          return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
        }

      case TimerFormat.countdown:
        if (hours > 0) {
          return '$hours ${hours == 1 ? 'hour' : 'hours'} $minutes ${minutes == 1 ? 'minute' : 'minutes'} $seconds ${seconds == 1 ? 'second' : 'seconds'}';
        } else if (minutes > 0) {
          return '$minutes ${minutes == 1 ? 'minute' : 'minutes'} $seconds ${seconds == 1 ? 'second' : 'seconds'}';
        } else {
          return '$seconds ${seconds == 1 ? 'second' : 'seconds'}';
        }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Create the base timer widget
    Widget timerContent = Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.showProgressIndicator)
          Padding(
            padding: const EdgeInsets.only(bottom: 16.0),
            child: LinearProgressIndicator(
              value: _progress,
              backgroundColor: widget.progressIndicatorBackgroundColor,
              color: _isFocused && widget.focusColor != null
                  ? widget.focusColor!
                  : _isHovered && widget.hoverColor != null
                      ? widget.hoverColor!
                      : widget.progressIndicatorColor,
              minHeight: widget.progressIndicatorThickness,
              borderRadius: BorderRadius.circular(widget.progressIndicatorThickness / 2),
            ),
          ),

        Align(
          alignment: widget.alignment,
          child: Text(
            _formatTime(),
            style: TextStyle(
              fontSize: widget.fontSize,
              fontWeight: widget.fontWeight,
              color: _isFocused && widget.focusColor != null
                  ? widget.focusColor!
                  : _isHovered && widget.hoverColor != null
                      ? widget.hoverColor!
                      : widget.textColor,
            ),
          ),
        ),

        if (widget.showControls)
          Padding(
            padding: const EdgeInsets.only(top: 16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  icon: Icon(_isRunning ? widget.pauseIcon : widget.startIcon),
                  color: _isRunning ? widget.pauseButtonColor : widget.startButtonColor,
                  onPressed: () {
                    if (widget.enableFeedback) {
                      HapticFeedback.selectionClick();
                    }
                    _isRunning ? _pauseTimer() : _startTimer();
                  },
                ),
                IconButton(
                  icon: Icon(widget.resetIcon),
                  color: widget.resetButtonColor,
                  onPressed: () {
                    if (widget.enableFeedback) {
                      HapticFeedback.selectionClick();
                    }
                    _resetTimer();
                  },
                ),
              ],
            ),
          ),
      ],
    );

    // Create the container with decoration
    Widget containerWidget = Container(
      width: widget.width,
      height: widget.height,
      padding: widget.padding,
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.borderColor != null
            ? Border.all(
                color: _isFocused && widget.focusColor != null
                    ? widget.focusColor!
                    : _isHovered && widget.hoverColor != null
                        ? widget.hoverColor!
                        : widget.borderColor!,
                width: widget.borderWidth,
              )
            : null,
      ),
      child: timerContent,
    );

    // Add gesture detector for advanced interactions
    Widget gestureWidget = GestureDetector(
      onTap: widget.onTap,
      onDoubleTap: widget.onDoubleTap,
      onLongPress: widget.onLongPress,
      child: containerWidget,
    );

    // Add mouse region for hover detection
    Widget hoverWidget = MouseRegion(
      onEnter: (_) => _handleHoverChange(true),
      onExit: (_) => _handleHoverChange(false),
      child: gestureWidget,
    );

    // Add focus handling
    Widget focusWidget = Focus(
      focusNode: _focusNode,
      child: hoverWidget,
    );

    // Add tooltip if needed
    if (widget.tooltip != null) {
      focusWidget = Tooltip(
        message: widget.tooltip!,
        child: focusWidget,
      );
    }

    // Add semantics if needed
    if (widget.semanticsLabel != null) {
      focusWidget = Semantics(
        label: widget.semanticsLabel,
        value: _formatTime(),
        child: focusWidget,
      );
    }

    return focusWidget;
  }
}