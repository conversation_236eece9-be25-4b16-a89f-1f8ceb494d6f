import 'package:flutter/material.dart';

class WebPOReviewLeftPanel extends StatefulWidget {
  const WebPOReviewLeftPanel({Key? key}) : super(key: key);

  @override
  State<WebPOReviewLeftPanel> createState() => _WebPOReviewLeftPanelState();
}

class _WebPOReviewLeftPanelState extends State<WebPOReviewLeftPanel> {
  // Checkbox states
  bool budgetApprovalRequired = true;
  bool budgetIndicatorAvailable = true;
  bool approvalAuthorityVerified = true;
  bool approvalAuthorityWithinLimit = true;
  bool vendorInformationVerified = true;
  bool deliveryDateAcceptable = true;
  bool paymentTermsAcceptable = true;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 300,
     decoration: BoxDecoration(
    color: Colors.white,
    border: Border.all(
      color: Color(0xFFF0F0F0), 
      width: 0.5,
    ),
    borderRadius: BorderRadius.circular(12), // 12px radius
    boxShadow: [
       BoxShadow(
        color: Colors.black.withOpacity(0.02), // Very subtle shadow
        offset: Offset(0, 1), // Minimal offset
        blurRadius: 3, // Small blur
        spreadRadius: 0,
      ),
    ],
  ),
  padding: EdgeInsets.all(10), // 
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Section
          _buildHeader(),

          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // PO Number Section
                  _buildPONumberSection(),

                  const SizedBox(height: 16),

                  // Order Summary Section
                  _buildOrderSummarySection(),

                  const SizedBox(height: 16),

                  // Line Items Section
                  _buildLineItemsSection(),

                  const SizedBox(height: 16),

                  // Subtotal Section
                  _buildSubtotalSection(),

                  const SizedBox(height: 16),

                  // Compliance Validation Section
                  _buildComplianceValidationSection(),

                  const SizedBox(height: 16),

                  // Final Review Checklist Section
                  _buildFinalReviewChecklistSection(),

                  const SizedBox(height: 16),

                  // Action Buttons
                  // _buildActionButtons(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      child: Row(
        children: [
          Container(
            width: 18,
            height: 18,
            decoration: BoxDecoration(
              color: Colors.green.shade600,
              borderRadius: BorderRadius.circular(2),
            ),
            child: const Icon(
              Icons.check,
              size: 12,
              color: Colors.white,
            ),
          ),
          const SizedBox(width: 8),
          const Text(
            'PURCHASE ORDER REVIEW',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPONumberSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFE3F2FD),
        borderRadius: BorderRadius.circular(4),
      ),
      child: const Column(
        children: [
          Text(
            'PO-2024-0145',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF1976D2),
            ),
          ),
          SizedBox(height: 4),
          Text(
            'Ready for Approval • Created Jan 15, 2024',
            style: TextStyle(
              fontSize: 11,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderSummarySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Order Summary Header
        Container(
          padding: const EdgeInsets.only(bottom: 8),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(color: Colors.grey.shade300, width: 1),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.assignment_outlined,
                size: 16,
                color: Colors.grey.shade700,
              ),
              const SizedBox(width: 8),
              const Text(
                'Order Summary',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 12),

        // Summary Details in 2x2 grid
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard('VENDOR', 'TechSupply Corp'),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildSummaryCard('EXPECTED DELIVERY', 'Feb 1, 2024'),
            ),
          ],
        ),

        const SizedBox(height: 8),

        Row(
          children: [
            Expanded(
              child: _buildSummaryCard('PAYMENT TERMS', 'Net 30'),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildSummaryCard('PRIORITY', 'Standard'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSummaryCard(String label, String value) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border(
          left: BorderSide(color: const Color(0xFF2196F3), width: 4),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: Colors.grey.shade600,
              letterSpacing: 0.5,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLineItemsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.only(bottom: 8),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(color: Colors.grey.shade300, width: 1),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.folder_outlined,
                size: 16,
                color: Colors.brown.shade600,
              ),
              const SizedBox(width: 8),
              const Text(
                'Line Items (3 items)',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Color(0xFFf8f9fa),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              _buildLineItem('Laptop - Dell XPS 13', 'Qty: 5 × \$1,200.00', '\$6,000.00'),
              _buildLineItem('Monitor - 24 inch', 'Qty: 10 × \$300.00', '\$3,000.00'),
              _buildLineItem('Wireless Mouse', 'Qty: 15 × \$35.00', '\$525.00'),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildLineItem(String title, String quantity, String amount) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  quantity,
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          Text(
            amount,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Color(0xFF4285F4),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubtotalSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
         color: Colors.green.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green.shade400, width: 2),
      ),
      child: Column(
        children: [
          _buildSubtotalRow('Subtotal:', '\$9,525.00'),
          const SizedBox(height: 4),
          _buildSubtotalRow('Tax (8%):', '\$762.00'),
          const SizedBox(height: 4),
          _buildSubtotalRow('Shipping:', '\$213.00'),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.only(top: 8),
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(color: Colors.green.shade400, width: 1),
              ),
            ),
            child: _buildSubtotalRow('Total Amount:', '\$10,500.00', isTotal: true),
          ),
        ],
      ),
    );
  }

  Widget _buildSubtotalRow(String label, String amount, {bool isTotal = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isTotal ? 14 : 13,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
            color: isTotal ? Colors.green.shade700 : Colors.black87,
          ),
        ),
        Text(
          amount,
          style: TextStyle(
            fontSize: isTotal ? 14 : 13,
            fontWeight: FontWeight.w600,
            color: isTotal ? Colors.green.shade700 : Colors.black87,
          ),
        ),
      ],
    );
  }

  Widget _buildComplianceValidationSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green.shade300, width: 2),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.verified_user,
                size: 16,
                color: Colors.green.shade700,
              ),
              const SizedBox(width: 8),
              Text(
                'Compliance Validation',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.green.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildCheckboxItem(
            'Budget Approval: Approved (21% of Q1 remaining budget)',
            budgetApprovalRequired,
            (value) => setState(() => budgetApprovalRequired = value ?? false),
          ),
          _buildCheckboxItem(
            'Vendor Verification: TechSupply Corp verified and active',
            budgetIndicatorAvailable,
            (value) => setState(() => budgetIndicatorAvailable = value ?? false),
          ),
          _buildCheckboxItem(
            'Policy Compliance: All procurement policies satisfied',
            approvalAuthorityVerified,
            (value) => setState(() => approvalAuthorityVerified = value ?? false),
          ),
          _buildCheckboxItem(
            'Approval Authority: Within your approval limit (\$50K)',
            approvalAuthorityWithinLimit,
            (value) => setState(() => approvalAuthorityWithinLimit = value ?? false),
          ),
        ],
      ),
    );
  }

  Widget _buildFinalReviewChecklistSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.orange.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.orange.shade300, width: 2),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.checklist,
                size: 16,
                color: Colors.orange.shade700,
              ),
              const SizedBox(width: 8),
              Text(
                'Final Review Checklist',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.orange.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildBlueCheckboxItem(
            'Vendor information is correct',
            vendorInformationVerified,
            (value) => setState(() => vendorInformationVerified = value ?? false),
          ),
          _buildBlueCheckboxItem(
            'Line items and quantities are accurate',
            deliveryDateAcceptable,
            (value) => setState(() => deliveryDateAcceptable = value ?? false),
          ),
          _buildBlueCheckboxItem(
            'Delivery date is acceptable',
            paymentTermsAcceptable,
            (value) => setState(() => paymentTermsAcceptable = value ?? false),
          ),
          _buildBlueCheckboxItem(
            'Total amount is within budget',
            true,
            (value) => {},
          ),
          _buildBlueCheckboxItem(
            'Business justification is clear',
            true,
            (value) => {},
          ),
        ],
      ),
    );
  }


  Widget _buildCheckboxItem(String text, bool value, ValueChanged<bool?> onChanged, {bool isSmall = false}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: isSmall ? 1 : 2),
      child: InkWell(
        onTap: () => onChanged(!value),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 16,
              height: 16,
              decoration: BoxDecoration(
                color: value ? Colors.green.shade600 : Colors.transparent,
                border: Border.all(
                  color: value ? Colors.green.shade600 : Colors.grey.shade400,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(2),
              ),
              child: value
                  ? const Icon(
                      Icons.check,
                      size: 12,
                      color: Colors.white,
                    )
                  : null,
            ),
            const SizedBox(width: 6),
            Expanded(
              child: Text(
                text,
                style: TextStyle(
                  fontSize: isSmall ? 10 : 11,
                  color: Colors.black87,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBlueCheckboxItem(String text, bool value, ValueChanged<bool?> onChanged, {bool isSmall = false}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: isSmall ? 1 : 2),
      child: InkWell(
        onTap: () => onChanged(!value),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 16,
              height: 16,
              decoration: BoxDecoration(
                color: value ? const Color(0xFF2196F3) : Colors.transparent,
                border: Border.all(
                  color: value ? const Color(0xFF2196F3) : Colors.grey.shade400,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(2),
              ),
              child: value
                  ? const Icon(
                      Icons.check,
                      size: 12,
                      color: Colors.white,
                    )
                  : null,
            ),
            const SizedBox(width: 6),
            Expanded(
              child: Text(
                text,
                style: TextStyle(
                  fontSize: isSmall ? 10 : 11,
                  color: Colors.black87,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Widget _buildActionButtons() {
  //   return Row(
  //     children: [
  //       // Edit Details Button
  //       Expanded(
  //         child: OutlinedButton(
  //           onPressed: () {},
  //           style: OutlinedButton.styleFrom(
  //             padding: const EdgeInsets.symmetric(vertical: 8),
  //             side: BorderSide(color: Colors.grey.shade400),
  //             shape: RoundedRectangleBorder(
  //               borderRadius: BorderRadius.circular(4),
  //             ),
  //           ),
  //           child: const Text(
  //             'Edit Details',
  //             style: TextStyle(
  //               fontSize: 10,
  //               fontWeight: FontWeight.w500,
  //               color: Colors.black87,
  //             ),
  //           ),
  //         ),
  //       ),

  //       const SizedBox(width: 4),

  //       // Save as Draft Button
  //       Expanded(
  //         child: OutlinedButton(
  //           onPressed: () {},
  //           style: OutlinedButton.styleFrom(
  //             padding: const EdgeInsets.symmetric(vertical: 8),
  //             side: BorderSide(color: Colors.grey.shade400),
  //             shape: RoundedRectangleBorder(
  //               borderRadius: BorderRadius.circular(4),
  //             ),
  //           ),
  //           child: const Text(
  //             'Save as Draft',
  //             style: TextStyle(
  //               fontSize: 10,
  //               fontWeight: FontWeight.w500,
  //               color: Colors.black87,
  //             ),
  //           ),
  //         ),
  //       ),

  //       const SizedBox(width: 4),

  //       // Submit for Approval Button
  //       Expanded(
  //         child: ElevatedButton(
  //           onPressed: () {},
  //           style: ElevatedButton.styleFrom(
  //             backgroundColor: Colors.green.shade600,
  //             padding: const EdgeInsets.symmetric(vertical: 8),
  //             shape: RoundedRectangleBorder(
  //               borderRadius: BorderRadius.circular(4),
  //             ),
  //           ),
  //           child: const Text(
  //             'Submit for Approval',
  //             style: TextStyle(
  //               fontSize: 10,
  //               fontWeight: FontWeight.w500,
  //               color: Colors.white,
  //             ),
  //           ),
  //         ),
  //       ),
  //     ],
  //   );
  // }
}
