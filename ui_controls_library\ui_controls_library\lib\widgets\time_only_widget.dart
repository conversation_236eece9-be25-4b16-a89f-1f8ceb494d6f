import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';

/// A customizable time-only widget that displays the current time.
///
/// This widget provides a time display with various customization options
/// including format, appearance, and behavior.
class TimeOnlyWidget extends StatefulWidget {
  /// Format of the time display
  final TimeFormat format;

  /// Whether to use 24-hour format (military time)
  final bool use24HourFormat;

  /// Whether to show seconds
  final bool showSeconds;

  /// Whether to show AM/PM indicator
  final bool showAmPm;

  /// Whether to update the time automatically
  final bool autoUpdate;

  /// Update interval in seconds
  final int updateIntervalSeconds;

  /// Size of the time text
  final double fontSize;

  /// Font weight of the time text
  final FontWeight fontWeight;

  /// Font family of the time text
  final String? fontFamily;

  /// Color of the time text
  final Color textColor;

  /// Background color of the widget
  final Color backgroundColor;

  /// Border color of the widget
  final Color? borderColor;

  /// Border width of the widget
  final double borderWidth;

  /// Border radius of the widget
  final double borderRadius;

  /// Width of the widget
  final double? width;

  /// Height of the widget
  final double? height;

  /// Padding around the time text
  final EdgeInsetsGeometry padding;

  /// Alignment of the time within its container
  final Alignment alignment;

  /// Whether to show a separator between hours, minutes, and seconds
  final bool showSeparator;

  /// Separator character (e.g., ":", "-", ".")
  final String separator;

  /// Whether to show a blinking separator
  final bool blinkSeparator;

  /// Whether to show the time zone
  final bool showTimeZone;

  /// Custom time zone (e.g., "EST", "GMT+1")
  final String? timeZone;

  /// Whether to show a digital clock style
  final bool digitalStyle;

  /// Whether to show a shadow under the text
  final bool showShadow;

  /// Shadow color
  final Color shadowColor;

  /// Shadow offset
  final Offset shadowOffset;

  /// Shadow blur radius
  final double shadowBlurRadius;

  // Advanced Interaction Properties

  /// Callback for mouse hover
  ///
  /// This function is called when the mouse pointer enters or exits the widget.
  /// The parameter is true when the mouse enters and false when it exits.
  final void Function(bool)? onHover;

  /// Callback for keyboard focus
  ///
  /// This function is called when the widget gains or loses keyboard focus.
  /// The parameter is true when focus is gained and false when it is lost.
  final void Function(bool)? onFocus;

  /// Focus node for manual focus control
  ///
  /// This allows for programmatic control of the widget's focus state.
  /// If null, the widget will create and manage its own focus node.
  final FocusNode? focusNode;

  /// Whether the widget should be focused automatically when it appears
  ///
  /// If true, the widget will request focus when it is first built.
  final bool autofocus;

  /// Color when the widget is hovered
  final Color? hoverColor;

  /// Color when the widget is focused
  final Color? focusColor;

  /// Tooltip text to display on hover
  final String? tooltip;

  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Whether to enable haptic/audio feedback
  ///
  /// If true, the widget will provide haptic and/or audio feedback when interacted with.
  final bool enableFeedback;

  /// Callback for tap gesture
  ///
  /// This function is called when the widget is tapped.
  final VoidCallback? onTap;

  /// Callback for double tap gesture
  ///
  /// This function is called when the widget is double-tapped.
  final VoidCallback? onDoubleTap;

  /// Callback for long press gesture
  ///
  /// This function is called when the widget is long-pressed.
  final VoidCallback? onLongPress;

  const TimeOnlyWidget({
    super.key,
    this.format = TimeFormat.standard,
    this.use24HourFormat = false,
    this.showSeconds = true,
    this.showAmPm = true,
    this.autoUpdate = true,
    this.updateIntervalSeconds = 1,
    this.fontSize = 24.0,
    this.fontWeight = FontWeight.normal,
    this.fontFamily,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.transparent,
    this.borderColor,
    this.borderWidth = 1.0,
    this.borderRadius = 8.0,
    this.width,
    this.height,
    this.padding = const EdgeInsets.all(16.0),
    this.alignment = Alignment.center,
    this.showSeparator = true,
    this.separator = ":",
    this.blinkSeparator = false,
    this.showTimeZone = false,
    this.timeZone,
    this.digitalStyle = false,
    this.showShadow = false,
    this.shadowColor = Colors.black26,
    this.shadowOffset = const Offset(2, 2),
    this.shadowBlurRadius = 4.0,
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
    this.hoverColor,
    this.focusColor,
    this.tooltip,
    this.semanticsLabel,
    this.enableFeedback = true,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
  });

  /// Creates a TimeOnlyWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the TimeOnlyWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "format": "standard",
  ///   "use24HourFormat": true,
  ///   "showSeconds": true,
  ///   "textColor": "white",
  ///   "backgroundColor": "black",
  ///   "digitalStyle": true
  /// }
  /// ```
  factory TimeOnlyWidget.fromJson(Map<String, dynamic> json) {
    // Parse colors
    Color? parseColor(dynamic colorValue) {
      if (colorValue == null) return null;

      if (colorValue is String) {
        // Handle named colors
        switch (colorValue.toLowerCase()) {
          case 'red': return Colors.red;
          case 'blue': return Colors.blue;
          case 'green': return Colors.green;
          case 'yellow': return Colors.yellow;
          case 'orange': return Colors.orange;
          case 'purple': return Colors.purple;
          case 'pink': return Colors.pink;
          case 'brown': return Colors.brown;
          case 'grey':
          case 'gray': return Colors.grey;
          case 'black': return Colors.black;
          case 'white': return Colors.white;
          case 'transparent': return Colors.transparent;
          case 'teal': return Colors.teal;
          case 'cyan': return Colors.cyan;
          case 'amber': return Colors.amber;
          case 'indigo': return Colors.indigo;
          case 'lime': return Colors.lime;
          default:
            // Handle hex colors
            if (colorValue.startsWith('#')) {
              try {
                final hexColor = colorValue.substring(1);
                final hexValue = int.parse('0xFF${hexColor.padRight(8, 'F').substring(0, 8)}');
                return Color(hexValue);
              } catch (e) {
                return null;
              }
            }
            return null;
        }
      } else if (colorValue is int) {
        return Color(colorValue);
      }

      return null;
    }

    // Parse font weight
    FontWeight parseFontWeight(dynamic weightValue) {
      if (weightValue == null) return FontWeight.normal;

      if (weightValue is String) {
        switch (weightValue.toLowerCase()) {
          case 'bold': return FontWeight.bold;
          case 'normal': return FontWeight.normal;
          case 'light': return FontWeight.w300;
          case 'thin': return FontWeight.w200;
          case 'medium': return FontWeight.w500;
          case 'semibold': return FontWeight.w600;
          case 'extrabold': return FontWeight.w800;
          case 'black': return FontWeight.w900;
          default: return FontWeight.normal;
        }
      } else if (weightValue is int) {
        switch (weightValue) {
          case 100: return FontWeight.w100;
          case 200: return FontWeight.w200;
          case 300: return FontWeight.w300;
          case 400: return FontWeight.w400;
          case 500: return FontWeight.w500;
          case 600: return FontWeight.w600;
          case 700: return FontWeight.w700;
          case 800: return FontWeight.w800;
          case 900: return FontWeight.w900;
          default: return FontWeight.normal;
        }
      }

      return FontWeight.normal;
    }

    // Parse alignment
    Alignment parseAlignment(dynamic alignmentValue) {
      if (alignmentValue == null) return Alignment.center;

      if (alignmentValue is String) {
        switch (alignmentValue.toLowerCase()) {
          case 'center': return Alignment.center;
          case 'topleft':
          case 'top_left': return Alignment.topLeft;
          case 'topright':
          case 'top_right': return Alignment.topRight;
          case 'bottomleft':
          case 'bottom_left': return Alignment.bottomLeft;
          case 'bottomright':
          case 'bottom_right': return Alignment.bottomRight;
          case 'top':
          case 'topcenter':
          case 'top_center': return Alignment.topCenter;
          case 'bottom':
          case 'bottomcenter':
          case 'bottom_center': return Alignment.bottomCenter;
          case 'left':
          case 'centerleft':
          case 'center_left': return Alignment.centerLeft;
          case 'right':
          case 'centerright':
          case 'center_right': return Alignment.centerRight;
          default: return Alignment.center;
        }
      }

      return Alignment.center;
    }

    // Parse offset
    Offset parseOffset(dynamic offsetValue) {
      if (offsetValue == null) return const Offset(2, 2);

      if (offsetValue is Map<String, dynamic>) {
        final dx = (offsetValue['dx'] as num?)?.toDouble() ?? 2.0;
        final dy = (offsetValue['dy'] as num?)?.toDouble() ?? 2.0;
        return Offset(dx, dy);
      } else if (offsetValue is List && offsetValue.length >= 2) {
        final dx = (offsetValue[0] as num?)?.toDouble() ?? 2.0;
        final dy = (offsetValue[1] as num?)?.toDouble() ?? 2.0;
        return Offset(dx, dy);
      }

      return const Offset(2, 2);
    }

    // Parse time format
    TimeFormat parseTimeFormat(dynamic formatValue) {
      if (formatValue == null) return TimeFormat.standard;

      if (formatValue is String) {
        switch (formatValue.toLowerCase()) {
          case 'standard': return TimeFormat.standard;
          case 'short': return TimeFormat.short;
          case 'long': return TimeFormat.long;
          case 'compact': return TimeFormat.compact;
          case 'custom': return TimeFormat.custom;
          default: return TimeFormat.standard;
        }
      } else if (formatValue is int) {
        switch (formatValue) {
          case 0: return TimeFormat.standard;
          case 1: return TimeFormat.short;
          case 2: return TimeFormat.long;
          case 3: return TimeFormat.compact;
          case 4: return TimeFormat.custom;
          default: return TimeFormat.standard;
        }
      }

      return TimeFormat.standard;
    }

    // Parse padding
    EdgeInsetsGeometry parsePadding(dynamic paddingValue) {
      if (paddingValue == null) return const EdgeInsets.all(16.0);

      if (paddingValue is num) {
        return EdgeInsets.all(paddingValue.toDouble());
      } else if (paddingValue is Map<String, dynamic>) {
        final left = (paddingValue['left'] as num?)?.toDouble() ?? 0.0;
        final top = (paddingValue['top'] as num?)?.toDouble() ?? 0.0;
        final right = (paddingValue['right'] as num?)?.toDouble() ?? 0.0;
        final bottom = (paddingValue['bottom'] as num?)?.toDouble() ?? 0.0;

        if (left == right && top == bottom && left == top) {
          return EdgeInsets.all(left);
        } else if (left == right && top == bottom) {
          return EdgeInsets.symmetric(horizontal: left, vertical: top);
        } else {
          return EdgeInsets.fromLTRB(left, top, right, bottom);
        }
      } else if (paddingValue is String) {
        switch (paddingValue.toLowerCase()) {
          case 'none':
          case 'zero': return EdgeInsets.zero;
          case 'small': return const EdgeInsets.all(8.0);
          case 'medium': return const EdgeInsets.all(16.0);
          case 'large': return const EdgeInsets.all(24.0);
          default: return const EdgeInsets.all(16.0);
        }
      }

      return const EdgeInsets.all(16.0);
    }

    return TimeOnlyWidget(
      format: parseTimeFormat(json['format']),
      use24HourFormat: json['use24HourFormat'] as bool? ?? false,
      showSeconds: json['showSeconds'] as bool? ?? true,
      showAmPm: json['showAmPm'] as bool? ?? true,
      autoUpdate: json['autoUpdate'] as bool? ?? true,
      updateIntervalSeconds: json['updateIntervalSeconds'] as int? ?? 1,
      fontSize: (json['fontSize'] as num?)?.toDouble() ?? 24.0,
      fontWeight: parseFontWeight(json['fontWeight']),
      fontFamily: json['fontFamily'] as String?,
      textColor: parseColor(json['textColor']) ?? Colors.black,
      backgroundColor: parseColor(json['backgroundColor']) ?? Colors.transparent,
      borderColor: parseColor(json['borderColor']),
      borderWidth: (json['borderWidth'] as num?)?.toDouble() ?? 1.0,
      borderRadius: (json['borderRadius'] as num?)?.toDouble() ?? 8.0,
      width: (json['width'] as num?)?.toDouble(),
      height: (json['height'] as num?)?.toDouble(),
      padding: parsePadding(json['padding']),
      alignment: parseAlignment(json['alignment']),
      showSeparator: json['showSeparator'] as bool? ?? true,
      separator: json['separator'] as String? ?? ":",
      blinkSeparator: json['blinkSeparator'] as bool? ?? false,
      showTimeZone: json['showTimeZone'] as bool? ?? false,
      timeZone: json['timeZone'] as String?,
      digitalStyle: json['digitalStyle'] as bool? ?? false,
      showShadow: json['showShadow'] as bool? ?? false,
      shadowColor: parseColor(json['shadowColor']) ?? Colors.black26,
      shadowOffset: parseOffset(json['shadowOffset']),
      shadowBlurRadius: (json['shadowBlurRadius'] as num?)?.toDouble() ?? 4.0,
      onHover: json['onHover'] == true ? (isHovered) {
        debugPrint('Time widget hover: $isHovered');
      } : null,
      onFocus: json['onFocus'] == true ? (isFocused) {
        debugPrint('Time widget focus: $isFocused');
      } : null,
      focusNode: null, // Cannot be created from JSON
      autofocus: json['autofocus'] as bool? ?? false,
      hoverColor: parseColor(json['hoverColor']),
      focusColor: parseColor(json['focusColor']),
      tooltip: json['tooltip'] as String?,
      semanticsLabel: json['semanticsLabel'] as String?,
      enableFeedback: json['enableFeedback'] as bool? ?? true,
      onTap: json['onTap'] == true ? () {
        debugPrint('Time widget tapped');
      } : null,
      onDoubleTap: json['onDoubleTap'] == true ? () {
        debugPrint('Time widget double-tapped');
      } : null,
      onLongPress: json['onLongPress'] == true ? () {
        debugPrint('Time widget long-pressed');
      } : null,
    );
  }

  /// Converts the TimeOnlyWidget to a JSON map
  ///
  /// This method allows for serializing the widget configuration to JSON,
  /// which can be useful for saving configurations or sharing them.
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {
      'format': _formatToString(format),
      'use24HourFormat': use24HourFormat,
      'showSeconds': showSeconds,
      'showAmPm': showAmPm,
      'autoUpdate': autoUpdate,
      'updateIntervalSeconds': updateIntervalSeconds,
      'fontSize': fontSize,
      'fontWeight': _fontWeightToString(fontWeight),
      'textColor': _colorToString(textColor),
      'backgroundColor': _colorToString(backgroundColor),
      'borderWidth': borderWidth,
      'borderRadius': borderRadius,
      'showSeparator': showSeparator,
      'separator': separator,
      'blinkSeparator': blinkSeparator,
      'showTimeZone': showTimeZone,
      'digitalStyle': digitalStyle,
      'showShadow': showShadow,
      'shadowColor': _colorToString(shadowColor),
      'shadowBlurRadius': shadowBlurRadius,
      'autofocus': autofocus,
      'enableFeedback': enableFeedback,
    };

    // Add optional properties
    if (fontFamily != null) json['fontFamily'] = fontFamily;
    if (borderColor != null) json['borderColor'] = _colorToString(borderColor!);
    if (width != null) json['width'] = width;
    if (height != null) json['height'] = height;
    if (timeZone != null) json['timeZone'] = timeZone;
    if (hoverColor != null) json['hoverColor'] = _colorToString(hoverColor!);
    if (focusColor != null) json['focusColor'] = _colorToString(focusColor!);
    if (tooltip != null) json['tooltip'] = tooltip;
    if (semanticsLabel != null) json['semanticsLabel'] = semanticsLabel;

    // Add shadow offset
    json['shadowOffset'] = {
      'dx': shadowOffset.dx,
      'dy': shadowOffset.dy,
    };

    // Add padding
    if (padding is EdgeInsets) {
      final EdgeInsets p = padding as EdgeInsets;
      if (p.left == p.right && p.top == p.bottom && p.left == p.top) {
        json['padding'] = p.left;
      } else {
        json['padding'] = {
          'left': p.left,
          'top': p.top,
          'right': p.right,
          'bottom': p.bottom,
        };
      }
    }

    // Add alignment
    json['alignment'] = _alignmentToString(alignment);

    // Add callback flags
    if (onHover != null) json['onHover'] = true;
    if (onFocus != null) json['onFocus'] = true;
    if (onTap != null) json['onTap'] = true;
    if (onDoubleTap != null) json['onDoubleTap'] = true;
    if (onLongPress != null) json['onLongPress'] = true;

    return json;
  }

  /// Helper method to convert a Color to a string
  static String _colorToString(Color color) {
    // Handle standard colors by name for better readability
    if (color == Colors.red) return 'red';
    if (color == Colors.blue) return 'blue';
    if (color == Colors.green) return 'green';
    if (color == Colors.yellow) return 'yellow';
    if (color == Colors.orange) return 'orange';
    if (color == Colors.purple) return 'purple';
    if (color == Colors.pink) return 'pink';
    if (color == Colors.brown) return 'brown';
    if (color == Colors.grey) return 'grey';
    if (color == Colors.black) return 'black';
    if (color == Colors.white) return 'white';
    if (color == Colors.transparent) return 'transparent';
    if (color == Colors.teal) return 'teal';
    if (color == Colors.cyan) return 'cyan';
    if (color == Colors.amber) return 'amber';
    if (color == Colors.indigo) return 'indigo';
    if (color == Colors.lime) return 'lime';

    // Convert to hex string
    final int colorValue = color.toARGB32();
    final r = ((colorValue >> 16) & 0xFF).toRadixString(16).padLeft(2, '0');
    final g = ((colorValue >> 8) & 0xFF).toRadixString(16).padLeft(2, '0');
    final b = (colorValue & 0xFF).toRadixString(16).padLeft(2, '0');
    return '#$r$g$b';
  }

  /// Helper method to convert a FontWeight to a string
  static String _fontWeightToString(FontWeight weight) {
    if (weight == FontWeight.bold) return 'bold';
    if (weight == FontWeight.normal) return 'normal';
    if (weight == FontWeight.w100) return '100';
    if (weight == FontWeight.w200) return '200';
    if (weight == FontWeight.w300) return 'light';
    if (weight == FontWeight.w400) return 'normal';
    if (weight == FontWeight.w500) return 'medium';
    if (weight == FontWeight.w600) return 'semibold';
    if (weight == FontWeight.w700) return 'bold';
    if (weight == FontWeight.w800) return 'extrabold';
    if (weight == FontWeight.w900) return 'black';

    return 'normal';
  }

  /// Helper method to convert a TimeFormat to a string
  static String _formatToString(TimeFormat format) {
    switch (format) {
      case TimeFormat.standard:
        return 'standard';
      case TimeFormat.short:
        return 'short';
      case TimeFormat.long:
        return 'long';
      case TimeFormat.compact:
        return 'compact';
      case TimeFormat.custom:
        return 'custom';
    }
  }

  /// Helper method to convert an Alignment to a string
  static String _alignmentToString(Alignment alignment) {
    if (alignment == Alignment.center) return 'center';
    if (alignment == Alignment.topLeft) return 'topLeft';
    if (alignment == Alignment.topCenter) return 'top';
    if (alignment == Alignment.topRight) return 'topRight';
    if (alignment == Alignment.centerLeft) return 'left';
    if (alignment == Alignment.centerRight) return 'right';
    if (alignment == Alignment.bottomLeft) return 'bottomLeft';
    if (alignment == Alignment.bottomCenter) return 'bottom';
    if (alignment == Alignment.bottomRight) return 'bottomRight';

    return 'center';
  }

  @override
  State<TimeOnlyWidget> createState() => _TimeOnlyWidgetState();
}

/// Format options for the time display
enum TimeFormat {
  /// Standard format (e.g., "3:45:30 PM" or "15:45:30")
  standard,

  /// Short format (e.g., "3:45 PM" or "15:45")
  short,

  /// Long format (e.g., "3 hours 45 minutes 30 seconds")
  long,

  /// Compact format (e.g., "3h 45m 30s")
  compact,

  /// Custom format using DateFormat pattern
  custom,
}

class _TimeOnlyWidgetState extends State<TimeOnlyWidget> {
  late DateTime _currentTime;
  Timer? _timer;
  bool _separatorVisible = true;
  bool _isHovered = false;
  bool _isFocused = false;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _currentTime = DateTime.now();

    // Initialize focus node
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_handleFocusChange);

    // Request focus if autofocus is enabled
    if (widget.autofocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }

    if (widget.autoUpdate) {
      _startTimer();
    }
  }

  // Handle focus changes
  void _handleFocusChange() {
    if (_focusNode.hasFocus != _isFocused) {
      setState(() {
        _isFocused = _focusNode.hasFocus;
      });

      if (widget.onFocus != null) {
        widget.onFocus!(_isFocused);
      }
    }
  }

  // Handle hover changes
  void _handleHoverChange(bool isHovered) {
    if (_isHovered != isHovered) {
      setState(() {
        _isHovered = isHovered;
      });

      if (widget.onHover != null) {
        widget.onHover!(isHovered);
      }
    }
  }

  @override
  void dispose() {
    _timer?.cancel();

    // Clean up focus node
    if (widget.focusNode == null) {
      // Only dispose the focus node if we created it
      _focusNode.removeListener(_handleFocusChange);
      _focusNode.dispose();
    } else {
      // Just remove our listener
      _focusNode.removeListener(_handleFocusChange);
    }

    super.dispose();
  }

  void _startTimer() {
    // Cancel any existing timer
    _timer?.cancel();

    // Create a new timer that updates the time at the specified interval
    _timer = Timer.periodic(
      Duration(seconds: widget.updateIntervalSeconds),
      (timer) {
        setState(() {
          _currentTime = DateTime.now();

          // Toggle separator visibility if blinking is enabled
          if (widget.blinkSeparator) {
            _separatorVisible = !_separatorVisible;
          }
        });
      },
    );
  }

  String _formatTime() {
    final hour = _currentTime.hour;
    final minute = _currentTime.minute;
    final second = _currentTime.second;

    // Adjust hour for 12-hour format if needed
    final displayHour = widget.use24HourFormat ? hour : (hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour));

    // Determine AM/PM
    final amPm = hour < 12 ? 'AM' : 'PM';

    // Format based on the selected format
    switch (widget.format) {
      case TimeFormat.standard:
        final hourStr = displayHour.toString();
        final minuteStr = minute.toString().padLeft(2, '0');
        final secondStr = second.toString().padLeft(2, '0');

        final separator = widget.showSeparator
            ? (widget.blinkSeparator ? (_separatorVisible ? widget.separator : ' ') : widget.separator)
            : ' ';

        String timeStr = '';
        if (widget.showSeconds) {
          timeStr = '$hourStr$separator$minuteStr$separator$secondStr';
        } else {
          timeStr = '$hourStr$separator$minuteStr';
        }

        if (!widget.use24HourFormat && widget.showAmPm) {
          timeStr += ' $amPm';
        }

        if (widget.showTimeZone && widget.timeZone != null) {
          timeStr += ' ${widget.timeZone}';
        }

        return timeStr;

      case TimeFormat.short:
        final hourStr = displayHour.toString();
        final minuteStr = minute.toString().padLeft(2, '0');

        final separator = widget.showSeparator
            ? (widget.blinkSeparator ? (_separatorVisible ? widget.separator : ' ') : widget.separator)
            : ' ';

        String timeStr = '$hourStr$separator$minuteStr';

        if (!widget.use24HourFormat && widget.showAmPm) {
          timeStr += ' $amPm';
        }

        if (widget.showTimeZone && widget.timeZone != null) {
          timeStr += ' ${widget.timeZone}';
        }

        return timeStr;

      case TimeFormat.long:
        final hourStr = '$displayHour ${displayHour == 1 ? 'hour' : 'hours'}';
        final minuteStr = '$minute ${minute == 1 ? 'minute' : 'minutes'}';

        String timeStr = '$hourStr $minuteStr';

        if (widget.showSeconds) {
          final secondStr = '$second ${second == 1 ? 'second' : 'seconds'}';
          timeStr += ' $secondStr';
        }

        if (!widget.use24HourFormat && widget.showAmPm) {
          timeStr += ' $amPm';
        }

        if (widget.showTimeZone && widget.timeZone != null) {
          timeStr += ' ${widget.timeZone}';
        }

        return timeStr;

      case TimeFormat.compact:
        final hourStr = '${displayHour}h';
        final minuteStr = '${minute}m';

        String timeStr = '$hourStr $minuteStr';

        if (widget.showSeconds) {
          final secondStr = '${second}s';
          timeStr += ' $secondStr';
        }

        if (!widget.use24HourFormat && widget.showAmPm) {
          timeStr += ' $amPm';
        }

        if (widget.showTimeZone && widget.timeZone != null) {
          timeStr += ' ${widget.timeZone}';
        }

        return timeStr;

      case TimeFormat.custom:
        // Use IntlDateFormat for custom formatting
        String pattern = widget.use24HourFormat ? 'HH:mm' : 'h:mm';

        if (widget.showSeconds) {
          pattern += ':ss';
        }

        if (!widget.use24HourFormat && widget.showAmPm) {
          pattern += ' a';
        }

        if (widget.showTimeZone) {
          pattern += ' z';
        }

        return DateFormat(pattern).format(_currentTime);
    }
  }

  @override
  Widget build(BuildContext context) {
    final timeText = _formatTime();

    // Create text style with or without shadow
    final textStyle = TextStyle(
      fontSize: widget.fontSize,
      fontWeight: widget.fontWeight,
      fontFamily: widget.fontFamily,
      color: _isFocused && widget.focusColor != null
          ? widget.focusColor!
          : _isHovered && widget.hoverColor != null
              ? widget.hoverColor!
              : widget.textColor,
      shadows: widget.showShadow
          ? [
              Shadow(
                color: widget.shadowColor,
                offset: widget.shadowOffset,
                blurRadius: widget.shadowBlurRadius,
              ),
            ]
          : null,
    );

    // Digital clock style uses a monospace font and different background
    final digitalTextStyle = widget.digitalStyle
        ? textStyle.copyWith(
            fontFamily: 'monospace',
            letterSpacing: 2.0,
          )
        : textStyle;

    // Create the base container
    Widget containerWidget = Container(
      width: widget.width,
      height: widget.height,
      padding: widget.padding,
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.borderColor != null
            ? Border.all(
                color: _isFocused && widget.focusColor != null
                    ? widget.focusColor!
                    : _isHovered && widget.hoverColor != null
                        ? widget.hoverColor!
                        : widget.borderColor!,
                width: widget.borderWidth,
              )
            : null,
        boxShadow: widget.digitalStyle
            ? [
                BoxShadow(
                  color: Colors.black.withAlpha(51), // 0.2 * 255 = ~51
                  blurRadius: 5.0,
                  spreadRadius: 1.0,
                ),
              ]
            : null,
      ),
      child: Align(
        alignment: widget.alignment,
        child: Text(
          timeText,
          style: digitalTextStyle,
        ),
      ),
    );

    // Add gesture detector for advanced interactions
    Widget gestureWidget = GestureDetector(
      onTap: () {
        if (widget.enableFeedback) {
          HapticFeedback.selectionClick();
        }
        if (widget.onTap != null) {
          widget.onTap!();
        }
      },
      onDoubleTap: widget.onDoubleTap != null
          ? () {
              if (widget.enableFeedback) {
                HapticFeedback.selectionClick();
              }
              widget.onDoubleTap!();
            }
          : null,
      onLongPress: widget.onLongPress != null
          ? () {
              if (widget.enableFeedback) {
                HapticFeedback.heavyImpact();
              }
              widget.onLongPress!();
            }
          : null,
      child: containerWidget,
    );

    // Add mouse region for hover detection
    Widget hoverWidget = MouseRegion(
      onEnter: (_) => _handleHoverChange(true),
      onExit: (_) => _handleHoverChange(false),
      child: gestureWidget,
    );

    // Add focus handling
    Widget focusWidget = Focus(
      focusNode: _focusNode,
      child: hoverWidget,
    );

    // Add tooltip if needed
    if (widget.tooltip != null) {
      focusWidget = Tooltip(
        message: widget.tooltip!,
        child: focusWidget,
      );
    }

    // Add semantics if needed
    if (widget.semanticsLabel != null) {
      focusWidget = Semantics(
        label: widget.semanticsLabel,
        value: timeText,
        child: focusWidget,
      );
    }

    return focusWidget;
  }
}