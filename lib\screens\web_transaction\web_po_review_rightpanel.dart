import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:convert';
import '../../theme/app_colors.dart';

// ==================== DATA MODELS ====================

class PurchaseOrderWorkflow {
  final String version;
  final String description;
  final int workflowStep;
  final String implementationDate;
  final UserContext userContext;
  final Level2ContextualInformation level2ContextualInformation;

  PurchaseOrderWorkflow({
    required this.version,
    required this.description,
    required this.workflowStep,
    required this.implementationDate,
    required this.userContext,
    required this.level2ContextualInformation,
  });

  factory PurchaseOrderWorkflow.fromJson(Map<String, dynamic> json) {
    final workflowData = json['purchase_order_review_workflow'];
    return PurchaseOrderWorkflow(
      version: workflowData['version'] ?? '',
      description: workflowData['description'] ?? '',
      workflowStep: workflowData['workflow_step'] ?? 0,
      implementationDate: workflowData['implementation_date'] ?? '',
      userContext: UserContext.fromJson(workflowData['user_context'] ?? {}),
      level2ContextualInformation: Level2ContextualInformation.fromJson(
        workflowData['level_2_contextual_information'] ?? {}),
    );
  }
}

class UserContext {
  final String currentUser;
  final String role;
  final String sessionId;
  final String requestTimestamp;
  final String department;
  final String poNumber;
  final String workflowState;

  UserContext({
    required this.currentUser,
    required this.role,
    required this.sessionId,
    required this.requestTimestamp,
    required this.department,
    required this.poNumber,
    required this.workflowState,
  });

  factory UserContext.fromJson(Map<String, dynamic> json) {
    return UserContext(
      currentUser: json['current_user'] ?? '',
      role: json['role'] ?? '',
      sessionId: json['session_id'] ?? '',
      requestTimestamp: json['request_timestamp'] ?? '',
      department: json['department'] ?? '',
      poNumber: json['po_number'] ?? '',
      workflowState: json['workflow_state'] ?? '',
    );
  }
}

class Level2ContextualInformation {
  final SidePanelExtended sidePanelExtended;

  Level2ContextualInformation({
    required this.sidePanelExtended,
  });

  factory Level2ContextualInformation.fromJson(Map<String, dynamic> json) {
    return Level2ContextualInformation(
      sidePanelExtended: SidePanelExtended.fromJson(
        json['side_panel_extended'] ?? {}),
    );
  }
}

class SidePanelExtended {
  final ApprovalWorkflowCard approvalWorkflowCard;
  final RiskAssessmentCard riskAssessmentCard;
  final CostBreakdownCard costBreakdownCard;
  final BudgetImpactCard? budgetImpactCard;

  SidePanelExtended({
    required this.approvalWorkflowCard,
    required this.riskAssessmentCard,
    required this.costBreakdownCard,
    this.budgetImpactCard,
  });

  factory SidePanelExtended.fromJson(Map<String, dynamic> json) {
    return SidePanelExtended(
      approvalWorkflowCard: ApprovalWorkflowCard.fromJson(
        json['approval_workflow_card'] ?? {}),
      riskAssessmentCard: RiskAssessmentCard.fromJson(
        json['risk_assessment_card'] ?? {}),
      costBreakdownCard: CostBreakdownCard.fromJson(
        json['cost_breakdown_card'] ?? {}),
      budgetImpactCard: json['budget_impact_card'] != null 
        ? BudgetImpactCard.fromJson(json['budget_impact_card'])
        : null,
    );
  }
}

class ApprovalWorkflowCard {
  final CardStructure cardStructure;

  ApprovalWorkflowCard({
    required this.cardStructure,
  });

  factory ApprovalWorkflowCard.fromJson(Map<String, dynamic> json) {
    return ApprovalWorkflowCard(
      cardStructure: CardStructure.fromJson(json['card_structure'] ?? {}),
    );
  }
}

class CardStructure {
  final CardHeader cardHeader;
  final ApprovalChain approvalChain;

  CardStructure({
    required this.cardHeader,
    required this.approvalChain,
  });

  factory CardStructure.fromJson(Map<String, dynamic> json) {
    return CardStructure(
      cardHeader: CardHeader.fromJson(json['card_header'] ?? {}),
      approvalChain: ApprovalChain.fromJson(json['approval_chain'] ?? {}),
    );
  }
}

class CardHeader {
  final String primaryTitle;
  final String workflowStatus;

  CardHeader({
    required this.primaryTitle,
    required this.workflowStatus,
  });

  factory CardHeader.fromJson(Map<String, dynamic> json) {
    return CardHeader(
      primaryTitle: json['primary_title']?['text'] ?? '',
      workflowStatus: json['workflow_status']?['text'] ?? '',
    );
  }
}

class ApprovalChain {
  final List<ApprovalStep> steps;

  ApprovalChain({
    required this.steps,
  });

  factory ApprovalChain.fromJson(Map<String, dynamic> json) {
    final stepsList = json['steps'] as List? ?? [];
    return ApprovalChain(
      steps: stepsList.map((step) => ApprovalStep.fromJson(step)).toList(),
    );
  }
}

class ApprovalStep {
  final int step;
  final String approver;
  final String role;
  final String status;
  final String estimatedTime;
  final String color;

  ApprovalStep({
    required this.step,
    required this.approver,
    required this.role,
    required this.status,
    required this.estimatedTime,
    required this.color,
  });

  factory ApprovalStep.fromJson(Map<String, dynamic> json) {
    return ApprovalStep(
      step: json['step'] ?? 0,
      approver: json['approver'] ?? '',
      role: json['role'] ?? '',
      status: json['status'] ?? '',
      estimatedTime: json['estimated_time'] ?? '',
      color: json['color'] ?? '',
    );
  }
}

class RiskAssessmentCard {
  final RiskCardStructure cardStructure;

  RiskAssessmentCard({
    required this.cardStructure,
  });

  factory RiskAssessmentCard.fromJson(Map<String, dynamic> json) {
    return RiskAssessmentCard(
      cardStructure: RiskCardStructure.fromJson(json['card_structure'] ?? {}),
    );
  }
}

class RiskCardStructure {
  final RiskCardHeader cardHeader;
  final List<RiskFactor> riskFactors;

  RiskCardStructure({
    required this.cardHeader,
    required this.riskFactors,
  });

  factory RiskCardStructure.fromJson(Map<String, dynamic> json) {
    final factorsList = json['risk_factors'] as List? ?? [];
    return RiskCardStructure(
      cardHeader: RiskCardHeader.fromJson(json['card_header'] ?? {}),
      riskFactors: factorsList.map((factor) => RiskFactor.fromJson(factor)).toList(),
    );
  }
}

class RiskCardHeader {
  final String primaryTitle;

  RiskCardHeader({
    required this.primaryTitle,
  });

  factory RiskCardHeader.fromJson(Map<String, dynamic> json) {
    return RiskCardHeader(
      primaryTitle: json['primary_title']?['text'] ?? '',
    );
  }
}

class RiskFactor {
  final String factor;
  final String level;
  final String reason;
  final String color;

  RiskFactor({
    required this.factor,
    required this.level,
    required this.reason,
    required this.color,
  });

  factory RiskFactor.fromJson(Map<String, dynamic> json) {
    return RiskFactor(
      factor: json['factor'] ?? '',
      level: json['level'] ?? '',
      reason: json['reason'] ?? '',
      color: json['color'] ?? '',
    );
  }
}

class CostBreakdownCard {
  final CostCardStructure cardStructure;

  CostBreakdownCard({
    required this.cardStructure,
  });

  factory CostBreakdownCard.fromJson(Map<String, dynamic> json) {
    return CostBreakdownCard(
      cardStructure: CostCardStructure.fromJson(json['card_structure'] ?? {}),
    );
  }
}

class CostCardStructure {
  final CostCardHeader cardHeader;
  final CostDetails costDetails;

  CostCardStructure({
    required this.cardHeader,
    required this.costDetails,
  });

  factory CostCardStructure.fromJson(Map<String, dynamic> json) {
    return CostCardStructure(
      cardHeader: CostCardHeader.fromJson(json['card_header'] ?? {}),
      costDetails: CostDetails.fromJson(json['cost_details'] ?? {}),
    );
  }
}

class CostCardHeader {
  final String primaryTitle;

  CostCardHeader({
    required this.primaryTitle,
  });

  factory CostCardHeader.fromJson(Map<String, dynamic> json) {
    return CostCardHeader(
      primaryTitle: json['primary_title']?['text'] ?? '',
    );
  }
}

class CostDetails {
  final String subtotal;
  final String shipping;
  final String tax;
  final String total;
  final String paymentTerms;
  final String currency;

  CostDetails({
    required this.subtotal,
    required this.shipping,
    required this.tax,
    required this.total,
    required this.paymentTerms,
    required this.currency,
  });

  factory CostDetails.fromJson(Map<String, dynamic> json) {
    return CostDetails(
      subtotal: json['subtotal'] ?? '',
      shipping: json['shipping'] ?? '',
      tax: json['tax'] ?? '',
      total: json['total'] ?? '',
      paymentTerms: json['payment_terms'] ?? '',
      currency: json['currency'] ?? '',
    );
  }
}

class BudgetImpactCard {
  final BudgetCardStructure cardStructure;

  BudgetImpactCard({
    required this.cardStructure,
  });

  factory BudgetImpactCard.fromJson(Map<String, dynamic> json) {
    return BudgetImpactCard(
      cardStructure: BudgetCardStructure.fromJson(json['card_structure'] ?? {}),
    );
  }
}

class BudgetCardStructure {
  final BudgetCardHeader cardHeader;
  final BudgetDetails budgetDetails;

  BudgetCardStructure({
    required this.cardHeader,
    required this.budgetDetails,
  });

  factory BudgetCardStructure.fromJson(Map<String, dynamic> json) {
    return BudgetCardStructure(
      cardHeader: BudgetCardHeader.fromJson(json['card_header'] ?? {}),
      budgetDetails: BudgetDetails.fromJson(json['budget_details'] ?? {}),
    );
  }
}

class BudgetCardHeader {
  final String primaryTitle;

  BudgetCardHeader({
    required this.primaryTitle,
  });

  factory BudgetCardHeader.fromJson(Map<String, dynamic> json) {
    return BudgetCardHeader(
      primaryTitle: json['primary_title']?['text'] ?? '',
    );
  }
}

class BudgetDetails {
  final String quarter;
  final String totalBudget;
  final BudgetUsage previousUsage;
  final BudgetUsage currentPurchase;
  final BudgetUsage remainingAfter;
  final BudgetAlert budgetAlert;

  BudgetDetails({
    required this.quarter,
    required this.totalBudget,
    required this.previousUsage,
    required this.currentPurchase,
    required this.remainingAfter,
    required this.budgetAlert,
  });

  factory BudgetDetails.fromJson(Map<String, dynamic> json) {
    return BudgetDetails(
      quarter: json['quarter'] ?? '',
      totalBudget: json['total_budget'] ?? '',
      previousUsage: BudgetUsage.fromJson(json['previous_usage'] ?? {}),
      currentPurchase: BudgetUsage.fromJson(json['current_purchase'] ?? {}),
      remainingAfter: BudgetUsage.fromJson(json['remaining_after'] ?? {}),
      budgetAlert: BudgetAlert.fromJson(json['budget_alert'] ?? {}),
    );
  }
}

class BudgetUsage {
  final String amount;
  final String percentage;

  BudgetUsage({
    required this.amount,
    required this.percentage,
  });

  factory BudgetUsage.fromJson(Map<String, dynamic> json) {
    return BudgetUsage(
      amount: json['amount'] ?? '',
      percentage: json['percentage'] ?? '',
    );
  }
}

class BudgetAlert {
  final bool showAlert;
  final String message;
  final String severity;

  BudgetAlert({
    required this.showAlert,
    required this.message,
    required this.severity,
  });

  factory BudgetAlert.fromJson(Map<String, dynamic> json) {
    return BudgetAlert(
      showAlert: json['show_alert'] ?? false,
      message: json['message'] ?? '',
      severity: json['severity'] ?? '',
    );
  }
}

// ==================== MAIN WIDGET ====================

class WebPoReviewRightPanel extends StatefulWidget {
  const WebPoReviewRightPanel({super.key});

  @override
  State<WebPoReviewRightPanel> createState() => _WebPoReviewRightPanelState();
}

class _WebPoReviewRightPanelState extends State<WebPoReviewRightPanel> {
  PurchaseOrderWorkflow? workflowData;
  bool isLoading = true;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _loadJsonData();
  }

  Future<void> _loadJsonData() async {
    try {
      // Load JSON file from assets - update path as needed
      String jsonString = await rootBundle.loadString('assets/data/po_review.json');
      
      // Parse JSON
      Map<String, dynamic> jsonData = json.decode(jsonString);
      
      // Convert to data model
      setState(() {
        workflowData = PurchaseOrderWorkflow.fromJson(jsonData);
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        errorMessage = 'Error loading workflow data: $e';
        isLoading = false;
      });
      print('Error loading JSON: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return Container(
        color: AppColors.backgroundLight,
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (errorMessage != null || workflowData == null) {
      return Container(
        color: AppColors.backgroundLight,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 48,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                errorMessage ?? 'No workflow data available',
                textAlign: TextAlign.center,
                style: const TextStyle(
                  color: Colors.red,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    isLoading = true;
                    errorMessage = null;
                  });
                  _loadJsonData();
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    return Container(
      color: AppColors.backgroundLight,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildApprovalWorkflowSection(),
            const SizedBox(height: 24),
            _buildRiskAssessmentSection(),
            const SizedBox(height: 24),
            _buildCostBreakdownSection(),
            const SizedBox(height: 24),
            _buildBudgetImpactSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildApprovalWorkflowSection() {
    final approvalCard = workflowData!.level2ContextualInformation
        .sidePanelExtended.approvalWorkflowCard;
    final cardHeader = approvalCard.cardStructure.cardHeader;
    final approvalSteps = approvalCard.cardStructure.approvalChain.steps;

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            offset: const Offset(0, 2),
            blurRadius: 4,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                cardHeader.primaryTitle,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimaryLight,
                  letterSpacing: 0.5,
                ),
              ),
              const Spacer(),
              Text(
                cardHeader.workflowStatus,
                style: const TextStyle(
                  fontSize: 10,
                  color: AppColors.textSecondaryLight,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...approvalSteps.map((step) => _buildApprovalStep(step)).toList(),
        ],
      ),
    );
  }

  Widget _buildApprovalStep(ApprovalStep step) {
    Color stepColor = _parseColor(step.color);
    Color backgroundColor = stepColor.withOpacity(0.1);
    Color borderColor = stepColor;

    // Determine if step is active/pending based on status
    bool isPending = step.status.toLowerCase().contains('pending');
    bool isWaiting = step.status.toLowerCase().contains('waiting');

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(6.0),
        border: Border.all(color: borderColor),
      ),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: stepColor,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                step.step.toString(),
                style: const TextStyle(
                  color: AppColors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  step.approver,
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimaryLight,
                  ),
                ),
                Text(
                  '${step.role} • ${step.status}',
                  style: const TextStyle(
                    fontSize: 11,
                    color: AppColors.textSecondaryLight,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      Icons.access_time,
                      size: 12,
                      color: isPending ? const Color(0xFFFF9500) : AppColors.textSecondaryLight,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      isPending 
                          ? 'Usually approves within ${step.estimatedTime}'
                          : 'Expected: ${step.estimatedTime}',
                      style: TextStyle(
                        fontSize: 10,
                        color: isPending ? const Color(0xFFFF9500) : AppColors.textSecondaryLight,
                        fontWeight: isPending ? FontWeight.w500 : FontWeight.normal,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRiskAssessmentSection() {
    final riskCard = workflowData!.level2ContextualInformation
        .sidePanelExtended.riskAssessmentCard;
    
    // Get the card header title from JSON
    final cardHeader = riskCard.cardStructure.cardHeader;
    
    // Get risk factors directly from the card structure
    final riskFactors = riskCard.cardStructure.riskFactors;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20.0),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            offset: const Offset(0, 2),
            blurRadius: 4,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                cardHeader.primaryTitle.isNotEmpty ? cardHeader.primaryTitle : 'RISK ASSESSMENT',
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimaryLight,
                  letterSpacing: 0.5,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          // Use risk factors from JSON
          Column(
            children: riskFactors.asMap().entries.map((entry) {
              int index = entry.key;
              RiskFactor factor = entry.value;
              return Column(
                children: [
                  _buildRiskItem(factor.factor, factor.reason, factor.level, _parseColor(factor.color)),
                  if (index < riskFactors.length - 1) 
                    const Divider(height: 14, color: Color(0xFFE5E7EB)),
                ],
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildRiskItem(String title, String description, String level, Color levelColor) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimaryLight,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 13,
                    color: AppColors.textSecondaryLight,
                    height: 1.3,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: levelColor.withOpacity(0.15),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              level,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: levelColor,
                letterSpacing: 0.2,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCostBreakdownSection() {
    final costCard = workflowData!.level2ContextualInformation
        .sidePanelExtended.costBreakdownCard;
    final costDetails = costCard.cardStructure.costDetails;
    final cardHeader = costCard.cardStructure.cardHeader;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            offset: const Offset(0, 2),
            blurRadius: 4,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                cardHeader.primaryTitle.isNotEmpty ? cardHeader.primaryTitle : 'COST BREAKDOWN',
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimaryLight,
                  letterSpacing: 0.5,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildCostItem('Equipment Subtotal:', costDetails.subtotal),
          const SizedBox(height: 12),
          _buildCostItem('Tax:', costDetails.tax),
          const SizedBox(height: 12),
          _buildCostItem('Shipping & Handling:', costDetails.shipping),
          const SizedBox(height: 6),
          const Divider(color: Color(0xFFE5E7EB)),
          const SizedBox(height: 6),
          _buildCostItem('Total Amount:', costDetails.total, isTotal: true),
          const SizedBox(height: 10),
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: const Color(0xFFF8F9FA),
              borderRadius: BorderRadius.circular(8.0),
              border: Border.all(color: const Color(0xFFE9ECEF), width: 1),
            ),
            child: Column(
              children: [
                _buildPaymentItem('Payment Terms:', costDetails.paymentTerms),
                const SizedBox(height: 12),
                _buildPaymentItem('Currency:', costDetails.currency),
                const SizedBox(height: 12),
                // Cost center would need to be added to JSON structure if needed
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCostItem(String label, String amount, {bool isTotal = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: isTotal ? FontWeight.w500 : FontWeight.normal,
            color: isTotal ? const Color(0xFF4CAF50) : AppColors.black,
          ),
        ),
        Text(
          amount,
          style: TextStyle(
            fontSize: 16,
            fontWeight: isTotal ? FontWeight.w600 : FontWeight.w600,
            color: isTotal ? const Color(0xFF4CAF50) : AppColors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentItem(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: AppColors.black,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: AppColors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildBudgetImpactSection() {
    final budgetCard = workflowData!.level2ContextualInformation
        .sidePanelExtended.budgetImpactCard;

    // Use dynamic data if available, otherwise fall back to static
    if (budgetCard != null) {
      return _buildDynamicBudgetImpact(budgetCard);
    } else {
      return _buildStaticBudgetImpact();
    }
  }

  Widget _buildDynamicBudgetImpact(BudgetImpactCard budgetCard) {
    final budgetDetails = budgetCard.cardStructure.budgetDetails;
    
    // Resolve dynamic template values
    final currentPurchaseAmount = _resolveTemplateValue(budgetDetails.currentPurchase.amount);
    final currentPurchasePercentage = _resolveTemplateValue(budgetDetails.currentPurchase.percentage);
    final alertMessage = _resolveTemplateValue(budgetDetails.budgetAlert.message);
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            offset: const Offset(0, 2),
            blurRadius: 4,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 20,
                height: 20,
                decoration: const BoxDecoration(
                  color: Color(0xFFF59E0B),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.trending_up,
                  color: AppColors.white,
                  size: 14,
                ),
              ),
              const SizedBox(width: 8),
              const Text(
                'BUDGET IMPACT',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimaryLight,
                  letterSpacing: 0.5,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: const Color(0xFFE3F2FD),
              borderRadius: BorderRadius.circular(8.0),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${budgetDetails.quarter} Budget Status:',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF6B7280),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 12),
                _buildBudgetItem(
                  'Previous Usage:', 
                  '${budgetDetails.previousUsage.amount} (${budgetDetails.previousUsage.percentage})', 
                  const Color(0xFF374151)
                ),
                const SizedBox(height: 8),
                _buildBudgetItem(
                  'This Purchase:', 
                  '$currentPurchaseAmount ($currentPurchasePercentage)', 
                  const Color(0xFF2563EB)
                ),
                const SizedBox(height: 8),
                _buildBudgetItem(
                  'Remaining After:', 
                  '${budgetDetails.remainingAfter.amount} (${budgetDetails.remainingAfter.percentage})', 
                  const Color(0xFF10B981)
                ),
              ],
            ),
          ),
          if (budgetDetails.budgetAlert.showAlert) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                color: _getBudgetAlertBackgroundColor(budgetDetails.budgetAlert.severity),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: _getBudgetAlertBorderColor(budgetDetails.budgetAlert.severity)),
              ),
              child: Row(
                children: [
                  Icon(
                    _getBudgetAlertIcon(budgetDetails.budgetAlert.severity),
                    color: _getBudgetAlertIconColor(budgetDetails.budgetAlert.severity),
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      alertMessage,
                      style: TextStyle(
                        fontSize: 12,
                        color: _getBudgetAlertTextColor(budgetDetails.budgetAlert.severity),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStaticBudgetImpact() {
    // Get the total cost from JSON data for consistency
    final totalCost = workflowData!.level2ContextualInformation
        .sidePanelExtended.costBreakdownCard.cardStructure.costDetails.total;
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            offset: const Offset(0, 2),
            blurRadius: 4,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 20,
                height: 20,
                decoration: const BoxDecoration(
                  color: Color(0xFFF59E0B),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.trending_up,
                  color: AppColors.white,
                  size: 14,
                ),
              ),
              const SizedBox(width: 8),
              const Text(
                'BUDGET IMPACT',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimaryLight,
                  letterSpacing: 0.5,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: const Color(0xFFE3F2FD),
              borderRadius: BorderRadius.circular(8.0),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Q1 2024 Budget Status:',
                  style: TextStyle(
                    fontSize: 12,
                    color: Color(0xFF6B7280),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 12),
                _buildBudgetItem('Previous Usage:', '\$35,000 (70%)', const Color(0xFF374151)),
                const SizedBox(height: 8),
                _buildBudgetItem('This Purchase:', '$totalCost (21%)', const Color(0xFF2563EB)),
                const SizedBox(height: 8),
                _buildBudgetItem('Remaining After:', '\$4,500 (9%)', const Color(0xFF10B981)),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(8.0),
            decoration: BoxDecoration(
              color: const Color(0xFFFEF3C7),
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: const Color(0xFFFDE68A)),
            ),
            child: const Row(
              children: [
                Icon(
                  Icons.warning,
                  color: Color(0xFFF59E0B),
                  size: 16,
                ),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Budget Alert: This purchase will leave only 9% of Q1 budget remaining.',
                    style: TextStyle(
                      fontSize: 12,
                      color: Color(0xFFF57C00),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBudgetItem(String label, String value, Color valueColor) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            color: AppColors.textPrimaryLight,
            fontWeight: FontWeight.w500,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: valueColor,
          ),
        ),
      ],
    );
  }

  // ==================== HELPER METHODS ====================

  // Helper method to resolve template values from JSON
  String _resolveTemplateValue(String value) {
    if (!value.contains('{{') || !value.contains('}}')) {
      return value; // Return as-is if no template syntax
    }

    // Handle specific template references
    if (value.contains('{{summary_values.total_amount}}')) {
      // Get total amount from cost breakdown (which matches summary values)
      final totalAmount = workflowData!.level2ContextualInformation
          .sidePanelExtended.costBreakdownCard.cardStructure.costDetails.total;
      return value.replaceAll('{{summary_values.total_amount}}', totalAmount);
    }

    if (value.contains('{{financial_impact.budget_utilization}}')) {
      // Return the budget utilization percentage
      return value.replaceAll('{{financial_impact.budget_utilization}}', '25% of remaining Q1 budget');
    }

    if (value.contains('{{compliance_validation.budget_approval.detail_text}}')) {
      // Return the budget approval detail text
      return value.replaceAll('{{compliance_validation.budget_approval.detail_text}}', 'Approved (21% of Q1 remaining budget)');
    }

    // If no specific template found, return the original value
    return value;
  }

  // Helper method to parse color strings from JSON
  Color _parseColor(String colorString) {
    if (colorString.startsWith('#')) {
      return Color(int.parse(colorString.substring(1), radix: 16) + 0xFF000000);
    }
    
    // Fallback colors based on common color names
    switch (colorString.toLowerCase()) {
      case 'green':
        return const Color(0xFF10B981);
      case 'orange':
        return const Color(0xFFF59E0B);
      case 'red':
        return const Color(0xFFEF4444);
      case 'blue':
        return const Color(0xFF3B82F6);
      default:
        return const Color(0xFF6B7280); // Default gray
    }
  }

  // Helper methods for budget alert styling
  Color _getBudgetAlertBackgroundColor(String severity) {
    switch (severity.toLowerCase()) {
      case 'error':
      case 'critical':
        return const Color(0xFFFEE2E2);
      case 'warning':
        return const Color(0xFFFEF3C7);
      case 'info':
        return const Color(0xFFE0F2FE);
      default:
        return const Color(0xFFF3F4F6);
    }
  }

  Color _getBudgetAlertBorderColor(String severity) {
    switch (severity.toLowerCase()) {
      case 'error':
      case 'critical':
        return const Color(0xFFFECACA);
      case 'warning':
        return const Color(0xFFFDE68A);
      case 'info':
        return const Color(0xFFBAE6FD);
      default:
        return const Color(0xFFD1D5DB);
    }
  }

  Color _getBudgetAlertIconColor(String severity) {
    switch (severity.toLowerCase()) {
      case 'error':
      case 'critical':
        return const Color(0xFFDC2626);
      case 'warning':
        return const Color(0xFFF59E0B);
      case 'info':
        return const Color(0xFF0284C7);
      default:
        return const Color(0xFF6B7280);
    }
  }

  Color _getBudgetAlertTextColor(String severity) {
    switch (severity.toLowerCase()) {
      case 'error':
      case 'critical':
        return const Color(0xFFB91C1C);
      case 'warning':
        return const Color(0xFFF57C00);
      case 'info':
        return const Color(0xFF0369A1);
      default:
        return const Color(0xFF374151);
    }
  }

  IconData _getBudgetAlertIcon(String severity) {
    switch (severity.toLowerCase()) {
      case 'error':
      case 'critical':
        return Icons.error;
      case 'warning':
        return Icons.warning;
      case 'info':
        return Icons.info;
      default:
        return Icons.notification_important;
    }
  }
}
