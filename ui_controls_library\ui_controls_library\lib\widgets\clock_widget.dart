import 'dart:math';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'dart:async';

/// A customizable clock widget that displays the current time with various configuration options.
class ClockWidget extends StatefulWidget {
  /// The format of the time display (using Intl DateFormat patterns).
  final String format;

  /// Whether to show seconds.
  final bool showSeconds;

  /// Whether to use 24-hour format.
  final bool use24HourFormat;

  /// Whether to show AM/PM indicator.
  final bool showAmPm;

  /// Whether to show date along with time.
  final bool showDate;

  /// Whether to show day of week.
  final bool showDayOfWeek;

  /// The locale for formatting (e.g., 'en_US', 'fr_FR').
  final String locale;

  /// Whether the clock should update in real-time.
  final bool isLiveUpdate;

  /// The interval in seconds for updating the clock when live update is enabled.
  final int updateIntervalSeconds;

  /// The text style for the clock display.
  final TextStyle? textStyle;

  /// The color of the clock text.
  final Color textColor;

  /// The background color of the clock widget.
  final Color backgroundColor;

  /// The font size of the clock text.
  final double fontSize;

  /// The font weight of the clock text.
  final FontWeight fontWeight;

  /// The text alignment of the clock display.
  final TextAlign textAlign;

  /// Whether the clock has a border.
  final bool hasBorder;

  /// The border radius of the clock widget.
  final double borderRadius;

  /// The border color of the clock widget.
  final Color borderColor;

  /// The border width of the clock widget.
  final double borderWidth;

  /// Whether the clock has a shadow.
  final bool hasShadow;

  /// The elevation of the shadow when hasShadow is true.
  final double elevation;

  /// Whether the clock is displayed in a compact format.
  final bool isCompact;

  /// An optional label for the clock.
  final String? label;

  /// An optional prefix text to display before the time.
  final String? prefix;

  /// An optional suffix text to display after the time.
  final String? suffix;

  /// An optional icon to display before the time.
  final IconData? prefixIcon;

  /// An optional icon to display after the time.
  final IconData? suffixIcon;

  /// Whether to use dark theme colors.
  final bool isDarkTheme;

  /// Whether to show an icon (usually a clock icon).
  final bool showIcon;

  /// The icon to display when showIcon is true.
  final IconData? icon;

  /// Whether the clock should have a pulsing animation.
  final bool isAnimated;

  /// Whether to show a digital clock display.
  final bool isDigital;

  /// Whether to show an analog clock display.
  final bool isAnalog;

  /// The color of the hour hand (for analog clock).
  final Color hourHandColor;

  /// The color of the minute hand (for analog clock).
  final Color minuteHandColor;

  /// The color of the second hand (for analog clock).
  final Color secondHandColor;

  /// The width of the hour hand (for analog clock).
  final double hourHandWidth;

  /// The width of the minute hand (for analog clock).
  final double minuteHandWidth;

  /// The width of the second hand (for analog clock).
  final double secondHandWidth;

  /// The color of the clock face (for analog clock).
  final Color clockFaceColor;

  /// The color of the clock border (for analog clock).
  final Color clockBorderColor;

  /// The width of the clock border (for analog clock).
  final double clockBorderWidth;

  /// Whether to show hour markers (for analog clock).
  final bool showHourMarkers;

  /// Whether to show minute markers (for analog clock).
  final bool showMinuteMarkers;

  /// The color of the hour markers (for analog clock).
  final Color hourMarkersColor;

  /// The color of the minute markers (for analog clock).
  final Color minuteMarkersColor;

  /// Whether to show numbers for hours (for analog clock).
  final bool showNumbers;

  /// The color of the numbers (for analog clock).
  final Color numbersColor;

  /// The size of the analog clock.
  final double analogClockSize;

  /// Whether to show a separator between hours, minutes, and seconds.
  final bool showSeparator;

  /// The separator character to use (default is ':').
  final String separator;

  /// Whether the separator should blink (for digital clock).
  final bool blinkSeparator;

  /// Whether to show the time zone.
  final bool showTimeZone;

  /// The time zone to display (if null, uses local time zone).
  final String? timeZone;

  /// Whether to show the clock in uppercase.
  final bool isUpperCase;

  /// Whether to show the clock in lowercase.
  final bool isLowerCase;

  /// Whether to capitalize the first letter of each word.
  final bool isCapitalized;

  /// Whether to show the clock text in italic.
  final bool isItalic;

  /// Whether to show the clock text in bold.
  final bool isBold;

  /// Whether to underline the clock text.
  final bool isUnderlined;

  /// The padding around the clock widget.
  final double padding;

  /// The width of the clock widget.
  final double width;

  /// The height of the clock widget.
  final double height;

  /// The main axis alignment of the clock content.
  final MainAxisAlignment mainAxisAlignment;

  /// The cross axis alignment of the clock content.
  final CrossAxisAlignment crossAxisAlignment;

  // Advanced Interaction Properties

  /// Callback for mouse hover
  ///
  /// This function is called when the mouse pointer enters or exits the clock.
  /// The parameter is true when the mouse enters and false when it exits.
  final void Function(bool)? onHover;

  /// Callback for keyboard focus
  ///
  /// This function is called when the clock gains or loses keyboard focus.
  /// The parameter is true when focus is gained and false when it is lost.
  final void Function(bool)? onFocus;

  /// Focus node for manual focus control
  ///
  /// This allows for programmatic control of the clock's focus state.
  /// If null, the clock will create and manage its own focus node.
  final FocusNode? focusNode;

  /// Whether the clock should be focused automatically when it appears
  ///
  /// If true, the clock will request focus when it is first built.
  final bool autofocus;

  /// Color when the clock is hovered
  final Color? hoverColor;

  /// Color when the clock is focused
  final Color? focusColor;

  /// Tooltip text to display on hover
  final String? tooltip;

  /// Semantic label for accessibility
  final String? semanticsLabel;

  // Additional Advanced Interaction Properties

  /// Callback for tap gesture
  ///
  /// This function is called when the clock is tapped.
  final VoidCallback? onTap;

  /// Callback for double tap gesture
  ///
  /// This function is called when the clock is double-tapped.
  final VoidCallback? onDoubleTap;

  /// Callback for long press gesture
  ///
  /// This function is called when the clock is long-pressed.
  final VoidCallback? onLongPress;

  /// More general gesture callback
  ///
  /// This function is called with the tap details when the clock is tapped.
  final GestureTapCallback? gestureTapCallback;

  /// Whether to enable haptic/audio feedback
  ///
  /// If true, the clock will provide haptic and/or audio feedback when interacted with.
  final bool enableFeedback;

  /// Creates a clock widget.
  const ClockWidget({
    super.key,
    this.format = '',
    this.showSeconds = true,
    this.use24HourFormat = false,
    this.showAmPm = true,
    this.showDate = false,
    this.showDayOfWeek = false,
    this.locale = 'en_US',
    this.isLiveUpdate = true,
    this.updateIntervalSeconds = 1,
    this.textStyle,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.textAlign = TextAlign.center,
    this.hasBorder = false,
    this.borderRadius = 4.0,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.isCompact = false,
    this.label,
    this.prefix,
    this.suffix,
    this.prefixIcon,
    this.suffixIcon,
    this.isDarkTheme = false,
    this.showIcon = false,
    this.icon = Icons.access_time,
    this.isAnimated = false,
    this.isDigital = true,
    this.isAnalog = false,
    this.hourHandColor = Colors.black,
    this.minuteHandColor = Colors.black,
    this.secondHandColor = Colors.red,
    this.hourHandWidth = 4.0,
    this.minuteHandWidth = 3.0,
    this.secondHandWidth = 1.5,
    this.clockFaceColor = Colors.white,
    this.clockBorderColor = Colors.black,
    this.clockBorderWidth = 2.0,
    this.showHourMarkers = true,
    this.showMinuteMarkers = true,
    this.hourMarkersColor = Colors.black,
    this.minuteMarkersColor = Colors.grey,
    this.showNumbers = true,
    this.numbersColor = Colors.black,
    this.analogClockSize = 150.0,
    this.showSeparator = true,
    this.separator = ':',
    this.blinkSeparator = false,
    this.showTimeZone = false,
    this.timeZone,
    this.isUpperCase = false,
    this.isLowerCase = false,
    this.isCapitalized = false,
    this.isItalic = false,
    this.isBold = false,
    this.isUnderlined = false,
    this.padding = 16.0,
    this.width = double.infinity,
    this.height = 0,
    this.mainAxisAlignment = MainAxisAlignment.center,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    // Advanced Interaction Properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
    this.hoverColor,
    this.focusColor,
    this.tooltip,
    this.semanticsLabel,
    // Additional Advanced Interaction Properties
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    this.gestureTapCallback,
    this.enableFeedback = true,
  });

  /// Creates a ClockWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the ClockWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "showSeconds": true,
  ///   "use24HourFormat": false,
  ///   "showAmPm": true,
  ///   "isDigital": true,
  ///   "textColor": "white",
  ///   "backgroundColor": "black"
  /// }
  /// ```
  factory ClockWidget.fromJson(Map<String, dynamic> json) {
    // Handle text style
    TextStyle? textStyle;
    if (json['textStyle'] != null && json['textStyle'] is Map) {
      Map<String, dynamic> textStyleMap = json['textStyle'] as Map<String, dynamic>;
      textStyle = TextStyle(
        color: _colorFromJson(textStyleMap['color']) ?? Colors.black,
        fontSize: (textStyleMap['fontSize'] as num?)?.toDouble() ?? 16.0,
        fontWeight: _fontWeightFromJson(textStyleMap['fontWeight']),
        fontStyle: textStyleMap['isItalic'] == true ? FontStyle.italic : FontStyle.normal,
        decoration: textStyleMap['isUnderlined'] == true ? TextDecoration.underline : TextDecoration.none,
      );
    }

    // Handle font weight
    FontWeight fontWeight = FontWeight.normal;
    if (json['fontWeight'] != null) {
      fontWeight = _fontWeightFromJson(json['fontWeight']);
    }

    // Handle text align
    TextAlign textAlign = TextAlign.center;
    if (json['textAlign'] != null) {
      textAlign = _textAlignFromJson(json['textAlign'].toString());
    }

    // Handle main axis alignment
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.center;
    if (json['mainAxisAlignment'] != null) {
      mainAxisAlignment = _mainAxisAlignmentFromJson(json['mainAxisAlignment'].toString());
    }

    // Handle cross axis alignment
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center;
    if (json['crossAxisAlignment'] != null) {
      crossAxisAlignment = _crossAxisAlignmentFromJson(json['crossAxisAlignment'].toString());
    }

    // Handle icon
    IconData? icon;
    if (json['icon'] != null) {
      icon = _getIconData(json['icon'].toString());
    }

    // Handle prefix icon
    IconData? prefixIcon;
    if (json['prefixIcon'] != null) {
      prefixIcon = _getIconData(json['prefixIcon'].toString());
    }

    // Handle suffix icon
    IconData? suffixIcon;
    if (json['suffixIcon'] != null) {
      suffixIcon = _getIconData(json['suffixIcon'].toString());
    }

    return ClockWidget(
      format: json['format'] as String? ?? '',
      showSeconds: json['showSeconds'] as bool? ?? true,
      use24HourFormat: json['use24HourFormat'] as bool? ?? false,
      showAmPm: json['showAmPm'] as bool? ?? true,
      showDate: json['showDate'] as bool? ?? false,
      showDayOfWeek: json['showDayOfWeek'] as bool? ?? false,
      locale: json['locale'] as String? ?? 'en_US',
      isLiveUpdate: json['isLiveUpdate'] as bool? ?? true,
      updateIntervalSeconds: json['updateIntervalSeconds'] as int? ?? 1,
      textStyle: textStyle,
      textColor: _colorFromJson(json['textColor']) ?? Colors.black,
      backgroundColor: _colorFromJson(json['backgroundColor']) ?? Colors.white,
      fontSize: (json['fontSize'] as num?)?.toDouble() ?? 16.0,
      fontWeight: fontWeight,
      textAlign: textAlign,
      hasBorder: json['hasBorder'] as bool? ?? false,
      borderRadius: (json['borderRadius'] as num?)?.toDouble() ?? 4.0,
      borderColor: _colorFromJson(json['borderColor']) ?? Colors.grey,
      borderWidth: (json['borderWidth'] as num?)?.toDouble() ?? 1.0,
      hasShadow: json['hasShadow'] as bool? ?? false,
      elevation: (json['elevation'] as num?)?.toDouble() ?? 2.0,
      isCompact: json['isCompact'] as bool? ?? false,
      label: json['label'] as String?,
      prefix: json['prefix'] as String?,
      suffix: json['suffix'] as String?,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      isDarkTheme: json['isDarkTheme'] as bool? ?? false,
      showIcon: json['showIcon'] as bool? ?? false,
      icon: icon ?? Icons.access_time,
      isAnimated: json['isAnimated'] as bool? ?? false,
      isDigital: json['isDigital'] as bool? ?? true,
      isAnalog: json['isAnalog'] as bool? ?? false,
      hourHandColor: _colorFromJson(json['hourHandColor']) ?? Colors.black,
      minuteHandColor: _colorFromJson(json['minuteHandColor']) ?? Colors.black,
      secondHandColor: _colorFromJson(json['secondHandColor']) ?? Colors.red,
      hourHandWidth: (json['hourHandWidth'] as num?)?.toDouble() ?? 4.0,
      minuteHandWidth: (json['minuteHandWidth'] as num?)?.toDouble() ?? 3.0,
      secondHandWidth: (json['secondHandWidth'] as num?)?.toDouble() ?? 1.5,
      clockFaceColor: _colorFromJson(json['clockFaceColor']) ?? Colors.white,
      clockBorderColor: _colorFromJson(json['clockBorderColor']) ?? Colors.black,
      clockBorderWidth: (json['clockBorderWidth'] as num?)?.toDouble() ?? 2.0,
      showHourMarkers: json['showHourMarkers'] as bool? ?? true,
      showMinuteMarkers: json['showMinuteMarkers'] as bool? ?? true,
      hourMarkersColor: _colorFromJson(json['hourMarkersColor']) ?? Colors.black,
      minuteMarkersColor: _colorFromJson(json['minuteMarkersColor']) ?? Colors.grey,
      showNumbers: json['showNumbers'] as bool? ?? true,
      numbersColor: _colorFromJson(json['numbersColor']) ?? Colors.black,
      analogClockSize: (json['analogClockSize'] as num?)?.toDouble() ?? 150.0,
      showSeparator: json['showSeparator'] as bool? ?? true,
      separator: json['separator'] as String? ?? ':',
      blinkSeparator: json['blinkSeparator'] as bool? ?? false,
      showTimeZone: json['showTimeZone'] as bool? ?? false,
      timeZone: json['timeZone'] as String?,
      isUpperCase: json['isUpperCase'] as bool? ?? false,
      isLowerCase: json['isLowerCase'] as bool? ?? false,
      isCapitalized: json['isCapitalized'] as bool? ?? false,
      isItalic: json['isItalic'] as bool? ?? false,
      isBold: json['isBold'] as bool? ?? false,
      isUnderlined: json['isUnderlined'] as bool? ?? false,
      padding: (json['padding'] as num?)?.toDouble() ?? 16.0,
      width: (json['width'] as num?)?.toDouble() ?? double.infinity,
      height: (json['height'] as num?)?.toDouble() ?? 0,
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      // Advanced Interaction Properties
      autofocus: json['autofocus'] as bool? ?? false,
      tooltip: json['tooltip'] as String?,
      semanticsLabel: json['semanticsLabel'] as String?,
      hoverColor: _colorFromJson(json['hoverColor']),
      focusColor: _colorFromJson(json['focusColor']),
      onHover: json['onHover'] == true ? (isHovered) {
        debugPrint('Clock hover state changed: isHovered: $isHovered');
      } : null,
      onFocus: json['onFocus'] == true ? (isFocused) {
        debugPrint('Clock focus state changed: isFocused: $isFocused');
      } : null,
      // Additional Advanced Interaction Properties
      onTap: json['onTap'] == true ? () {
        debugPrint('Clock tapped');
      } : null,
      onDoubleTap: json['onDoubleTap'] == true ? () {
        debugPrint('Clock double-tapped');
      } : null,
      onLongPress: json['onLongPress'] == true ? () {
        debugPrint('Clock long-pressed');
      } : null,
      gestureTapCallback: json['gestureTapCallback'] == true ? () {
        debugPrint('Clock tapped with gesture tap callback');
      } : null,
      enableFeedback: json['enableFeedback'] as bool? ?? true,
    );
  }

  /// Converts a JSON color value to a Flutter Color
  static Color? _colorFromJson(dynamic colorValue) {
    if (colorValue == null) return null;

    if (colorValue is String) {
      // Handle hex strings like "#FF0000"
      if (colorValue.startsWith('#')) {
        String hexColor = colorValue.substring(1);

        // Handle shorthand hex like #RGB
        if (hexColor.length == 3) {
          hexColor = hexColor.split('').map((c) => '$c$c').join('');
        }

        // Add alpha channel if missing
        if (hexColor.length == 6) {
          hexColor = 'FF$hexColor';
        }

        // Parse the hex value
        try {
          return Color(int.parse('0x$hexColor'));
        } catch (e) {
          // Silently handle the error and return null
          return null;
        }
      }

      // Handle named colors
      switch (colorValue.toLowerCase()) {
        case 'red': return Colors.red;
        case 'blue': return Colors.blue;
        case 'green': return Colors.green;
        case 'yellow': return Colors.yellow;
        case 'orange': return Colors.orange;
        case 'purple': return Colors.purple;
        case 'pink': return Colors.pink;
        case 'brown': return Colors.brown;
        case 'grey':
        case 'gray': return Colors.grey;
        case 'black': return Colors.black;
        case 'white': return Colors.white;
        case 'amber': return Colors.amber;
        case 'cyan': return Colors.cyan;
        case 'indigo': return Colors.indigo;
        case 'lime': return Colors.lime;
        case 'teal': return Colors.teal;
        default: return null;
      }
    } else if (colorValue is int) {
      // Handle integer color values
      return Color(colorValue);
    }

    return null;
  }

  /// Gets an IconData from a string name
  static IconData? _getIconData(String iconName) {
    switch (iconName.toLowerCase()) {
      case 'access_time': return Icons.access_time;
      case 'alarm': return Icons.alarm;
      case 'alarm_on': return Icons.alarm_on;
      case 'alarm_add': return Icons.alarm_add;
      case 'hourglass_empty': return Icons.hourglass_empty;
      case 'hourglass_full': return Icons.hourglass_full;
      case 'schedule': return Icons.schedule;
      case 'timer': return Icons.timer;
      case 'timer_off': return Icons.timer_off;
      case 'update': return Icons.update;
      case 'watch': return Icons.watch;
      case 'watch_later': return Icons.watch_later;
      case 'date_range': return Icons.date_range;
      case 'calendar_today': return Icons.calendar_today;
      case 'event': return Icons.event;
      case 'event_available': return Icons.event_available;
      case 'event_busy': return Icons.event_busy;
      case 'event_note': return Icons.event_note;
      case 'today': return Icons.today;
      case 'add': return Icons.add;
      case 'remove': return Icons.remove;
      case 'close': return Icons.close;
      case 'check': return Icons.check;
      case 'star': return Icons.star;
      case 'favorite': return Icons.favorite;
      case 'heart': return Icons.favorite;
      case 'info': return Icons.info;
      case 'warning': return Icons.warning;
      case 'error': return Icons.error;
      case 'help': return Icons.help;
      case 'settings': return Icons.settings;
      case 'person': return Icons.person;
      case 'people': return Icons.people;
      case 'home': return Icons.home;
      case 'search': return Icons.search;
      case 'send': return Icons.send;
      case 'mail': return Icons.mail;
      case 'email': return Icons.email;
      case 'phone': return Icons.phone;
      case 'message': return Icons.message;
      case 'chat': return Icons.chat;
      case 'comment': return Icons.comment;
      case 'notifications': return Icons.notifications;
      default: return null;
    }
  }

  /// Converts a string to a FontWeight
  static FontWeight _fontWeightFromJson(dynamic fontWeightValue) {
    if (fontWeightValue == null) return FontWeight.normal;

    if (fontWeightValue is String) {
      switch (fontWeightValue.toLowerCase()) {
        case 'bold': return FontWeight.bold;
        case 'normal': return FontWeight.normal;
        case 'light': return FontWeight.w300;
        case 'thin': return FontWeight.w100;
        case 'medium': return FontWeight.w500;
        case 'semibold': return FontWeight.w600;
        case 'black': return FontWeight.w900;
        default: return FontWeight.normal;
      }
    } else if (fontWeightValue is int) {
      switch (fontWeightValue) {
        case 100: return FontWeight.w100;
        case 200: return FontWeight.w200;
        case 300: return FontWeight.w300;
        case 400: return FontWeight.w400;
        case 500: return FontWeight.w500;
        case 600: return FontWeight.w600;
        case 700: return FontWeight.w700;
        case 800: return FontWeight.w800;
        case 900: return FontWeight.w900;
        default: return FontWeight.normal;
      }
    } else if (fontWeightValue is bool && fontWeightValue) {
      return FontWeight.bold;
    }

    return FontWeight.normal;
  }

  /// Converts a string to a TextAlign
  static TextAlign _textAlignFromJson(String textAlignStr) {
    switch (textAlignStr.toLowerCase()) {
      case 'left': return TextAlign.left;
      case 'right': return TextAlign.right;
      case 'center': return TextAlign.center;
      case 'justify': return TextAlign.justify;
      case 'start': return TextAlign.start;
      case 'end': return TextAlign.end;
      default: return TextAlign.center;
    }
  }

  /// Converts a string to a MainAxisAlignment
  static MainAxisAlignment _mainAxisAlignmentFromJson(String alignmentStr) {
    switch (alignmentStr.toLowerCase()) {
      case 'start': return MainAxisAlignment.start;
      case 'end': return MainAxisAlignment.end;
      case 'center': return MainAxisAlignment.center;
      case 'spacearound':
      case 'space_around':
      case 'space around': return MainAxisAlignment.spaceAround;
      case 'spacebetween':
      case 'space_between':
      case 'space between': return MainAxisAlignment.spaceBetween;
      case 'spaceevenly':
      case 'space_evenly':
      case 'space evenly': return MainAxisAlignment.spaceEvenly;
      default: return MainAxisAlignment.center;
    }
  }

  /// Converts a string to a CrossAxisAlignment
  static CrossAxisAlignment _crossAxisAlignmentFromJson(String alignmentStr) {
    switch (alignmentStr.toLowerCase()) {
      case 'start': return CrossAxisAlignment.start;
      case 'end': return CrossAxisAlignment.end;
      case 'center': return CrossAxisAlignment.center;
      case 'stretch': return CrossAxisAlignment.stretch;
      case 'baseline': return CrossAxisAlignment.baseline;
      default: return CrossAxisAlignment.center;
    }
  }

  /// Converts a Flutter Color to a JSON representation
  static String _colorToJson(Color color) {
    // Handle standard colors by name for better readability
    if (color == Colors.red) return 'red';
    if (color == Colors.blue) return 'blue';
    if (color == Colors.green) return 'green';
    if (color == Colors.yellow) return 'yellow';
    if (color == Colors.orange) return 'orange';
    if (color == Colors.purple) return 'purple';
    if (color == Colors.pink) return 'pink';
    if (color == Colors.brown) return 'brown';
    if (color == Colors.grey) return 'grey';
    if (color == Colors.black) return 'black';
    if (color == Colors.white) return 'white';
    if (color == Colors.amber) return 'amber';
    if (color == Colors.cyan) return 'cyan';
    if (color == Colors.indigo) return 'indigo';
    if (color == Colors.lime) return 'lime';
    if (color == Colors.teal) return 'teal';

    // For MaterialColor, preserve the original color name if possible
    if (color is MaterialColor) {
      if (color == Colors.red) return 'red';
      if (color == Colors.blue) return 'blue';
      if (color == Colors.green) return 'green';
      if (color == Colors.yellow) return 'yellow';
      if (color == Colors.orange) return 'orange';
      if (color == Colors.purple) return 'purple';
      if (color == Colors.pink) return 'pink';
      if (color == Colors.brown) return 'brown';
      if (color == Colors.grey) return 'grey';
      if (color == Colors.amber) return 'amber';
      if (color == Colors.cyan) return 'cyan';
      if (color == Colors.indigo) return 'indigo';
      if (color == Colors.lime) return 'lime';
      if (color == Colors.teal) return 'teal';

      // If it's a MaterialColor but not one of the standard ones,
      // fall back to the hex representation of the primary value
      color = color.shade500;
    }

    // Convert to RGB format and create a hex string for other colors
    final int colorValue = color.toARGB32();
    final r = ((colorValue >> 16) & 0xFF).toRadixString(16).padLeft(2, '0');
    final g = ((colorValue >> 8) & 0xFF).toRadixString(16).padLeft(2, '0');
    final b = (colorValue & 0xFF).toRadixString(16).padLeft(2, '0');
    return '#$r$g$b';
  }

  /// Gets a string name from an IconData
  static String? _getIconName(IconData? icon) {
    if (icon == null) return null;

    if (icon == Icons.access_time) return 'access_time';
    if (icon == Icons.alarm) return 'alarm';
    if (icon == Icons.alarm_on) return 'alarm_on';
    if (icon == Icons.alarm_add) return 'alarm_add';
    if (icon == Icons.hourglass_empty) return 'hourglass_empty';
    if (icon == Icons.hourglass_full) return 'hourglass_full';
    if (icon == Icons.schedule) return 'schedule';
    if (icon == Icons.timer) return 'timer';
    if (icon == Icons.timer_off) return 'timer_off';
    if (icon == Icons.update) return 'update';
    if (icon == Icons.watch) return 'watch';
    if (icon == Icons.watch_later) return 'watch_later';
    if (icon == Icons.date_range) return 'date_range';
    if (icon == Icons.calendar_today) return 'calendar_today';
    if (icon == Icons.event) return 'event';
    if (icon == Icons.event_available) return 'event_available';
    if (icon == Icons.event_busy) return 'event_busy';
    if (icon == Icons.event_note) return 'event_note';
    if (icon == Icons.today) return 'today';
    if (icon == Icons.add) return 'add';
    if (icon == Icons.remove) return 'remove';
    if (icon == Icons.close) return 'close';
    if (icon == Icons.check) return 'check';
    if (icon == Icons.star) return 'star';
    if (icon == Icons.favorite) return 'favorite';
    if (icon == Icons.info) return 'info';
    if (icon == Icons.warning) return 'warning';
    if (icon == Icons.error) return 'error';
    if (icon == Icons.help) return 'help';
    if (icon == Icons.settings) return 'settings';
    if (icon == Icons.person) return 'person';
    if (icon == Icons.people) return 'people';
    if (icon == Icons.home) return 'home';
    if (icon == Icons.search) return 'search';
    if (icon == Icons.send) return 'send';
    if (icon == Icons.mail) return 'mail';
    if (icon == Icons.email) return 'email';
    if (icon == Icons.phone) return 'phone';
    if (icon == Icons.message) return 'message';
    if (icon == Icons.chat) return 'chat';
    if (icon == Icons.comment) return 'comment';
    if (icon == Icons.notifications) return 'notifications';

    return null;
  }

  /// Converts a FontWeight to a string
  static String _fontWeightToJson(FontWeight fontWeight) {
    if (fontWeight == FontWeight.bold) return 'bold';
    if (fontWeight == FontWeight.normal) return 'normal';
    if (fontWeight == FontWeight.w100) return '100';
    if (fontWeight == FontWeight.w200) return '200';
    if (fontWeight == FontWeight.w300) return 'light';
    if (fontWeight == FontWeight.w400) return 'normal';
    if (fontWeight == FontWeight.w500) return 'medium';
    if (fontWeight == FontWeight.w600) return 'semibold';
    if (fontWeight == FontWeight.w700) return 'bold';
    if (fontWeight == FontWeight.w800) return '800';
    if (fontWeight == FontWeight.w900) return 'black';

    return 'normal';
  }

  /// Converts a TextAlign to a string
  static String _textAlignToJson(TextAlign textAlign) {
    if (textAlign == TextAlign.left) return 'left';
    if (textAlign == TextAlign.right) return 'right';
    if (textAlign == TextAlign.center) return 'center';
    if (textAlign == TextAlign.justify) return 'justify';
    if (textAlign == TextAlign.start) return 'start';
    if (textAlign == TextAlign.end) return 'end';

    return 'center';
  }

  /// Converts a MainAxisAlignment to a string
  static String _mainAxisAlignmentToJson(MainAxisAlignment alignment) {
    if (alignment == MainAxisAlignment.start) return 'start';
    if (alignment == MainAxisAlignment.end) return 'end';
    if (alignment == MainAxisAlignment.center) return 'center';
    if (alignment == MainAxisAlignment.spaceAround) return 'space_around';
    if (alignment == MainAxisAlignment.spaceBetween) return 'space_between';
    if (alignment == MainAxisAlignment.spaceEvenly) return 'space_evenly';

    return 'center';
  }

  /// Converts a CrossAxisAlignment to a string
  static String _crossAxisAlignmentToJson(CrossAxisAlignment alignment) {
    if (alignment == CrossAxisAlignment.start) return 'start';
    if (alignment == CrossAxisAlignment.end) return 'end';
    if (alignment == CrossAxisAlignment.center) return 'center';
    if (alignment == CrossAxisAlignment.stretch) return 'stretch';
    if (alignment == CrossAxisAlignment.baseline) return 'baseline';

    return 'center';
  }

  /// Converts the ClockWidget to a JSON map
  ///
  /// This method allows for serializing the widget configuration to JSON,
  /// which can be useful for saving configurations or sharing them.
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {
      // Basic properties
      'format': format,
      'showSeconds': showSeconds,
      'use24HourFormat': use24HourFormat,
      'showAmPm': showAmPm,
      'showDate': showDate,
      'showDayOfWeek': showDayOfWeek,
      'locale': locale,
      'isLiveUpdate': isLiveUpdate,
      'updateIntervalSeconds': updateIntervalSeconds,
      'textColor': _colorToJson(textColor),
      'backgroundColor': _colorToJson(backgroundColor),
      'fontSize': fontSize,
      'fontWeight': _fontWeightToJson(fontWeight),
      'textAlign': _textAlignToJson(textAlign),
      'hasBorder': hasBorder,
      'borderRadius': borderRadius,
      'borderColor': _colorToJson(borderColor),
      'borderWidth': borderWidth,
      'hasShadow': hasShadow,
      'elevation': elevation,
      'isCompact': isCompact,
      'isDarkTheme': isDarkTheme,
      'showIcon': showIcon,
      'isAnimated': isAnimated,
      'isDigital': isDigital,
      'isAnalog': isAnalog,

      // Analog clock properties
      'hourHandColor': _colorToJson(hourHandColor),
      'minuteHandColor': _colorToJson(minuteHandColor),
      'secondHandColor': _colorToJson(secondHandColor),
      'hourHandWidth': hourHandWidth,
      'minuteHandWidth': minuteHandWidth,
      'secondHandWidth': secondHandWidth,
      'clockFaceColor': _colorToJson(clockFaceColor),
      'clockBorderColor': _colorToJson(clockBorderColor),
      'clockBorderWidth': clockBorderWidth,
      'showHourMarkers': showHourMarkers,
      'showMinuteMarkers': showMinuteMarkers,
      'hourMarkersColor': _colorToJson(hourMarkersColor),
      'minuteMarkersColor': _colorToJson(minuteMarkersColor),
      'showNumbers': showNumbers,
      'numbersColor': _colorToJson(numbersColor),
      'analogClockSize': analogClockSize,

      // Digital clock properties
      'showSeparator': showSeparator,
      'separator': separator,
      'blinkSeparator': blinkSeparator,
      'showTimeZone': showTimeZone,

      // Text formatting properties
      'isUpperCase': isUpperCase,
      'isLowerCase': isLowerCase,
      'isCapitalized': isCapitalized,
      'isItalic': isItalic,
      'isBold': isBold,
      'isUnderlined': isUnderlined,

      // Layout properties
      'padding': padding,
      'width': width == double.infinity ? 'infinity' : width,
      'height': height,
      'mainAxisAlignment': _mainAxisAlignmentToJson(mainAxisAlignment),
      'crossAxisAlignment': _crossAxisAlignmentToJson(crossAxisAlignment),

      // Advanced Interaction Properties
      'autofocus': autofocus,
      'enableFeedback': enableFeedback,
    };

    // Add optional properties only if they are not null
    if (label != null) {
      json['label'] = label;
    }

    if (prefix != null) {
      json['prefix'] = prefix;
    }

    if (suffix != null) {
      json['suffix'] = suffix;
    }

    if (timeZone != null) {
      json['timeZone'] = timeZone;
    }

    if (icon != null) {
      json['icon'] = _getIconName(icon);
    }

    if (prefixIcon != null) {
      json['prefixIcon'] = _getIconName(prefixIcon);
    }

    if (suffixIcon != null) {
      json['suffixIcon'] = _getIconName(suffixIcon);
    }

    if (tooltip != null) {
      json['tooltip'] = tooltip;
    }

    if (semanticsLabel != null) {
      json['semanticsLabel'] = semanticsLabel;
    }

    if (hoverColor != null) {
      json['hoverColor'] = _colorToJson(hoverColor!);
    }

    if (focusColor != null) {
      json['focusColor'] = _colorToJson(focusColor!);
    }

    if (onHover != null) {
      json['onHover'] = true;
    }

    if (onFocus != null) {
      json['onFocus'] = true;
    }

    if (onTap != null) {
      json['onTap'] = true;
    }

    if (onDoubleTap != null) {
      json['onDoubleTap'] = true;
    }

    if (onLongPress != null) {
      json['onLongPress'] = true;
    }

    if (gestureTapCallback != null) {
      json['gestureTapCallback'] = true;
    }

    // Handle textStyle if provided
    if (textStyle != null) {
      json['textStyle'] = {
        'color': _colorToJson(textStyle!.color ?? textColor),
        'fontSize': textStyle!.fontSize ?? fontSize,
        'fontWeight': _fontWeightToJson(textStyle!.fontWeight ?? fontWeight),
        'isItalic': textStyle!.fontStyle == FontStyle.italic,
        'isUnderlined': textStyle!.decoration == TextDecoration.underline,
      };
    }

    return json;
  }

  @override
  State<ClockWidget> createState() => _ClockWidgetState();
}

class _ClockWidgetState extends State<ClockWidget> with TickerProviderStateMixin {
  late DateTime _currentDateTime;
  String _formattedTime = '';
  Timer? _timer;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  bool _showSeparator = true;

  // Advanced interaction state variables
  late FocusNode _focusNode;
  bool _isFocused = false;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _currentDateTime = DateTime.now();
    _updateFormattedTime();

    // Set up animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.6,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    if (widget.isAnimated) {
      _animationController.repeat(reverse: true);
    }

    // Set up timer for live updates
    if (widget.isLiveUpdate) {
      _timer = Timer.periodic(
        Duration(seconds: widget.updateIntervalSeconds),
        (timer) {
          setState(() {
            _currentDateTime = DateTime.now();
            _updateFormattedTime();
            if (widget.blinkSeparator) {
              _showSeparator = !_showSeparator;
            }
          });
        },
      );
    }

    // Initialize focus node
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_handleFocusChange);

    // Request focus if autofocus is enabled
    if (widget.autofocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    _animationController.dispose();

    // Clean up focus node
    if (widget.focusNode == null) {
      // Only dispose the focus node if we created it
      _focusNode.removeListener(_handleFocusChange);
      _focusNode.dispose();
    }

    super.dispose();
  }

  // Handle focus changes
  void _handleFocusChange() {
    if (_focusNode.hasFocus != _isFocused) {
      setState(() {
        _isFocused = _focusNode.hasFocus;
      });

      if (widget.onFocus != null) {
        widget.onFocus!(_isFocused);
      }
    }
  }

  // Handle hover changes
  void _handleHoverChange(bool isHovered) {
    if (isHovered != _isHovered) {
      setState(() {
        _isHovered = isHovered;
      });

      if (widget.onHover != null) {
        widget.onHover!(isHovered);
      }
    }
  }

  void _updateFormattedTime() {
    // Use custom format if provided
    if (widget.format.isNotEmpty) {
      try {
        _formattedTime = DateFormat(widget.format, widget.locale).format(_currentDateTime);
      } catch (e) {
        _formattedTime = 'Invalid format';
      }
      return;
    }

    // Build format based on configuration
    String formatPattern = '';

    // Add date if requested
    if (widget.showDate) {
      formatPattern += 'MMM d, ';
      if (widget.showDayOfWeek) {
        formatPattern = 'EEE, $formatPattern';
      }
    } else if (widget.showDayOfWeek) {
      formatPattern += 'EEE ';
    }

    // Add time format
    if (widget.use24HourFormat) {
      formatPattern += 'HH:mm';
    } else {
      formatPattern += 'h:mm';
    }

    // Add seconds if requested
    if (widget.showSeconds) {
      formatPattern += ':ss';
    }

    // Add AM/PM if using 12-hour format and showAmPm is true
    if (!widget.use24HourFormat && widget.showAmPm) {
      formatPattern += ' a';
    }

    // Add time zone if requested
    if (widget.showTimeZone) {
      formatPattern += ' z';
    }

    try {
      _formattedTime = DateFormat(formatPattern, widget.locale).format(_currentDateTime);
    } catch (e) {
      _formattedTime = 'Invalid format';
    }

    // Apply text transformations
    if (widget.isUpperCase) {
      _formattedTime = _formattedTime.toUpperCase();
    } else if (widget.isLowerCase) {
      _formattedTime = _formattedTime.toLowerCase();
    } else if (widget.isCapitalized) {
      _formattedTime = _formattedTime.split(' ').map((word) {
        if (word.isEmpty) return word;
        return word[0].toUpperCase() + word.substring(1);
      }).join(' ');
    }

    // Handle blinking separator
    if (widget.blinkSeparator && !_showSeparator) {
      _formattedTime = _formattedTime.replaceAll(widget.separator, ' ');
    }
  }

  Widget _buildDigitalClock() {
    final effectiveTextColor = widget.isDarkTheme ? Colors.white : widget.textColor;

    TextStyle timeStyle = widget.textStyle ??
        TextStyle(
          color: effectiveTextColor,
          fontSize: widget.fontSize,
          fontWeight: widget.isBold ? FontWeight.bold : widget.fontWeight,
          fontStyle: widget.isItalic ? FontStyle.italic : FontStyle.normal,
          decoration: widget.isUnderlined ? TextDecoration.underline : TextDecoration.none,
        );

    Widget timeWidget = Text(
      _formattedTime,
      style: timeStyle,
      textAlign: widget.textAlign,
    );

    // Apply animation if enabled
    if (widget.isAnimated) {
      timeWidget = FadeTransition(
        opacity: _fadeAnimation,
        child: timeWidget,
      );
    }

    // Add prefix/suffix if provided
    List<Widget> rowChildren = [];

    if (widget.showIcon && widget.icon != null) {
      rowChildren.add(
        Icon(
          widget.icon,
          color: effectiveTextColor,
          size: widget.fontSize * 1.2,
        ),
      );
      rowChildren.add(const SizedBox(width: 8));
    }

    if (widget.prefixIcon != null) {
      rowChildren.add(
        Icon(
          widget.prefixIcon,
          color: effectiveTextColor,
          size: widget.fontSize,
        ),
      );
      rowChildren.add(const SizedBox(width: 8));
    }

    if (widget.prefix != null) {
      rowChildren.add(
        Text(
          widget.prefix!,
          style: timeStyle.copyWith(
            fontSize: widget.fontSize * 0.8,
          ),
        ),
      );
      rowChildren.add(const SizedBox(width: 4));
    }

    rowChildren.add(timeWidget);

    if (widget.suffix != null) {
      rowChildren.add(const SizedBox(width: 4));
      rowChildren.add(
        Text(
          widget.suffix!,
          style: timeStyle.copyWith(
            fontSize: widget.fontSize * 0.8,
          ),
        ),
      );
    }

    if (widget.suffixIcon != null) {
      rowChildren.add(const SizedBox(width: 8));
      rowChildren.add(
        Icon(
          widget.suffixIcon,
          color: effectiveTextColor,
          size: widget.fontSize,
        ),
      );
    }

    Widget content = Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: widget.mainAxisAlignment,
      crossAxisAlignment: widget.crossAxisAlignment,
      children: rowChildren,
    );

    // Add label if provided
    if (widget.label != null) {
      content = Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          content,
          const SizedBox(height: 4),
          Text(
            widget.label!,
            style: TextStyle(
              color: effectiveTextColor.withAlpha((255 * 0.7).round()),
              fontSize: widget.fontSize * 0.8,
            ),
          ),
        ],
      );
    }

    return content;
  }

  Widget _buildAnalogClock() {
    return SizedBox(
      width: widget.analogClockSize,
      height: widget.analogClockSize,
      child: CustomPaint(
        painter: _AnalogClockPainter(
          dateTime: _currentDateTime,
          hourHandColor: widget.hourHandColor,
          minuteHandColor: widget.minuteHandColor,
          secondHandColor: widget.secondHandColor,
          hourHandWidth: widget.hourHandWidth,
          minuteHandWidth: widget.minuteHandWidth,
          secondHandWidth: widget.secondHandWidth,
          clockFaceColor: widget.clockFaceColor,
          clockBorderColor: widget.clockBorderColor,
          clockBorderWidth: widget.clockBorderWidth,
          showHourMarkers: widget.showHourMarkers,
          showMinuteMarkers: widget.showMinuteMarkers,
          hourMarkersColor: widget.hourMarkersColor,
          minuteMarkersColor: widget.minuteMarkersColor,
          showNumbers: widget.showNumbers,
          numbersColor: widget.numbersColor,
          isDarkTheme: widget.isDarkTheme,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Apply dark theme if specified
    final effectiveBackgroundColor = widget.isDarkTheme ? Colors.grey.shade800 : widget.backgroundColor;

    // Create the clock content
    Widget clockContent;
    if (widget.isAnalog) {
      clockContent = _buildAnalogClock();
    } else {
      clockContent = _buildDigitalClock();
    }

    // Apply container styling
    Widget container = Container(
      width: widget.width != 0 ? widget.width : null,
      height: widget.height != 0 ? widget.height : null,
      padding: EdgeInsets.all(widget.padding),
      decoration: BoxDecoration(
        color: _isHovered && widget.hoverColor != null
            ? widget.hoverColor
            : (_isFocused && widget.focusColor != null
                ? widget.focusColor
                : effectiveBackgroundColor),
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.hasBorder
            ? Border.all(
                color: widget.borderColor,
                width: widget.borderWidth,
              )
            : null,
        boxShadow: widget.hasShadow
            ? [
                BoxShadow(
                  color: Colors.black.withAlpha((255 * 0.2).round()),
                  blurRadius: widget.elevation,
                  offset: Offset(0, widget.elevation / 2),
                ),
              ]
            : null,
      ),
      child: Center(child: clockContent),
    );

    // Apply compact mode if enabled
    if (widget.isCompact) {
      container = Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: _isHovered && widget.hoverColor != null
              ? widget.hoverColor
              : (_isFocused && widget.focusColor != null
                  ? widget.focusColor
                  : (widget.isDarkTheme ? Colors.grey.shade800 : effectiveBackgroundColor)),
          borderRadius: BorderRadius.circular(widget.borderRadius),
          border: widget.hasBorder
              ? Border.all(
                  color: widget.borderColor,
                  width: widget.borderWidth,
                )
              : null,
        ),
        child: clockContent,
      );
    }

    // Wrap with MouseRegion for hover detection
    Widget result = MouseRegion(
      onEnter: (_) => _handleHoverChange(true),
      onExit: (_) => _handleHoverChange(false),
      child: GestureDetector(
        onTap: () {
          // Request focus when tapped
          _focusNode.requestFocus();

          // Call onTap callback if provided
          if (widget.onTap != null) {
            widget.onTap!();
          }

          // Call gestureTapCallback if provided
          if (widget.gestureTapCallback != null) {
            widget.gestureTapCallback!();
          }

          // Provide feedback if enabled
          if (widget.enableFeedback) {
            HapticFeedback.selectionClick();
          }
        },
        onDoubleTap: widget.onDoubleTap,
        onLongPress: widget.onLongPress,
        child: Focus(
          focusNode: _focusNode,
          child: container,
        ),
      ),
    );

    // Wrap with tooltip if provided
    if (widget.tooltip != null) {
      result = Tooltip(
        message: widget.tooltip!,
        child: result,
      );
    }

    // Wrap with Semantics for accessibility if semanticsLabel is provided
    if (widget.semanticsLabel != null) {
      result = Semantics(
        label: widget.semanticsLabel,
        child: result,
      );
    }

    return result;
  }
}

class _AnalogClockPainter extends CustomPainter {
  final DateTime dateTime;
  final Color hourHandColor;
  final Color minuteHandColor;
  final Color secondHandColor;
  final double hourHandWidth;
  final double minuteHandWidth;
  final double secondHandWidth;
  final Color clockFaceColor;
  final Color clockBorderColor;
  final double clockBorderWidth;
  final bool showHourMarkers;
  final bool showMinuteMarkers;
  final Color hourMarkersColor;
  final Color minuteMarkersColor;
  final bool showNumbers;
  final Color numbersColor;
  final bool isDarkTheme;

  _AnalogClockPainter({
    required this.dateTime,
    required this.hourHandColor,
    required this.minuteHandColor,
    required this.secondHandColor,
    required this.hourHandWidth,
    required this.minuteHandWidth,
    required this.secondHandWidth,
    required this.clockFaceColor,
    required this.clockBorderColor,
    required this.clockBorderWidth,
    required this.showHourMarkers,
    required this.showMinuteMarkers,
    required this.hourMarkersColor,
    required this.minuteMarkersColor,
    required this.showNumbers,
    required this.numbersColor,
    required this.isDarkTheme,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // Draw clock face
    final facePaint = Paint()
      ..color = isDarkTheme ? Colors.grey.shade700 : clockFaceColor
      ..style = PaintingStyle.fill;
    canvas.drawCircle(center, radius, facePaint);

    // Draw clock border
    final borderPaint = Paint()
      ..color = clockBorderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = clockBorderWidth;
    canvas.drawCircle(center, radius - clockBorderWidth / 2, borderPaint);

    // Draw hour markers
    if (showHourMarkers) {
      final hourMarkerPaint = Paint()
        ..color = hourMarkersColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.0;

      for (int i = 0; i < 12; i++) {
        final angle = i * 30 * pi / 180;
        final outerPoint = Offset(
          center.dx + (radius - 10) * cos(angle),
          center.dy + (radius - 10) * sin(angle),
        );
        final innerPoint = Offset(
          center.dx + (radius - 20) * cos(angle),
          center.dy + (radius - 20) * sin(angle),
        );
        canvas.drawLine(innerPoint, outerPoint, hourMarkerPaint);
      }
    }

    // Draw minute markers
    if (showMinuteMarkers) {
      final minuteMarkerPaint = Paint()
        ..color = minuteMarkersColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0;

      for (int i = 0; i < 60; i++) {
        // Skip positions where hour markers are drawn
        if (i % 5 == 0 && showHourMarkers) continue;

        final angle = i * 6 * pi / 180;
        final outerPoint = Offset(
          center.dx + (radius - 10) * cos(angle),
          center.dy + (radius - 10) * sin(angle),
        );
        final innerPoint = Offset(
          center.dx + (radius - 15) * cos(angle),
          center.dy + (radius - 15) * sin(angle),
        );
        canvas.drawLine(innerPoint, outerPoint, minuteMarkerPaint);
      }
    }

    // Draw numbers
    if (showNumbers) {
      final textStyle = TextStyle(
        color: numbersColor,
        fontSize: radius / 5,
        fontWeight: FontWeight.bold,
      );
      final textPainter = TextPainter(
        textAlign: TextAlign.center,
        textDirection: ui.TextDirection.ltr,
      );

      for (int i = 1; i <= 12; i++) {
        final angle = (i * 30 - 90) * pi / 180;
        final offset = Offset(
          center.dx + (radius - 35) * cos(angle),
          center.dy + (radius - 35) * sin(angle),
        );

        textPainter.text = TextSpan(
          text: i.toString(),
          style: textStyle,
        );
        textPainter.layout();
        textPainter.paint(
          canvas,
          Offset(
            offset.dx - textPainter.width / 2,
            offset.dy - textPainter.height / 2,
          ),
        );
      }
    }

    // Calculate hand angles
    final hour = dateTime.hour % 12;
    final minute = dateTime.minute;
    final second = dateTime.second;

    final hourAngle = (hour + minute / 60) * 30 * pi / 180 - pi / 2;
    final minuteAngle = minute * 6 * pi / 180 - pi / 2;
    final secondAngle = second * 6 * pi / 180 - pi / 2;

    // Draw hour hand
    final hourHandPaint = Paint()
      ..color = hourHandColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = hourHandWidth
      ..strokeCap = StrokeCap.round;

    canvas.drawLine(
      center,
      Offset(
        center.dx + radius * 0.5 * cos(hourAngle),
        center.dy + radius * 0.5 * sin(hourAngle),
      ),
      hourHandPaint,
    );

    // Draw minute hand
    final minuteHandPaint = Paint()
      ..color = minuteHandColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = minuteHandWidth
      ..strokeCap = StrokeCap.round;

    canvas.drawLine(
      center,
      Offset(
        center.dx + radius * 0.7 * cos(minuteAngle),
        center.dy + radius * 0.7 * sin(minuteAngle),
      ),
      minuteHandPaint,
    );

    // Draw second hand
    final secondHandPaint = Paint()
      ..color = secondHandColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = secondHandWidth
      ..strokeCap = StrokeCap.round;

    canvas.drawLine(
      center,
      Offset(
        center.dx + radius * 0.8 * cos(secondAngle),
        center.dy + radius * 0.8 * sin(secondAngle),
      ),
      secondHandPaint,
    );

    // Draw center dot
    final centerDotPaint = Paint()
      ..color = secondHandColor
      ..style = PaintingStyle.fill;
    canvas.drawCircle(center, hourHandWidth, centerDotPaint);
  }

  @override
  bool shouldRepaint(_AnalogClockPainter oldDelegate) {
    return oldDelegate.dateTime != dateTime;
  }
}


