import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/providers/web_home_provider.dart';

import 'package:nsl/screens/web/new_design/widgets/hover_create_button.dart';
import 'package:nsl/screens/web/new_design/widgets/create_object_popup.dart';
import 'package:nsl/screens/web/new_design/widgets/hover_nav_item.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';

import 'web_agent_screen.dart';

class Book {
  final String title;
  final String subtitle;
  final String imageUrl;
  final String versionNumber;
  final bool isDraft;
  final double imageWidth;
  final double imageHeight;

  Book({
    required this.title,
    this.subtitle = '',
    required this.imageUrl,
    required this.versionNumber,
    this.isDraft = false,
    this.imageWidth = 170.0,
    this.imageHeight = 170.0,
  });

  factory Book.fromJson(Map<String, dynamic> json) {
    return Book(
      title: json['title'] as String,
      subtitle: json['subtitle'] as String? ?? '',
      imageUrl: json['imageUrl'] as String,
      versionNumber: json['versionNumber'] as String? ?? '',
      isDraft: json['isDraft'] as bool? ?? false,
      imageWidth: (json['imageWidth'] as num?)?.toDouble() ?? 170.0,
      imageHeight: (json['imageHeight'] as num?)?.toDouble() ?? 170.0,
    );
  }
}

class WebObjectScreen extends StatefulWidget {
  const WebObjectScreen({super.key});

  @override
  State<WebObjectScreen> createState() => _WebObjectScreenState();
}

class _WebObjectScreenState extends State<WebObjectScreen>
    with TickerProviderStateMixin {
  List<Book> allBooks = [];
  List<Book> books =
      []; // Initialize as empty list to avoid LateInitializationError
  List<Book> _filteredBooks = []; // Store filtered books for display
  bool isLoading = true;
  List<AnimationController> _animationControllers = [];
  List<Animation<double>> _slideAnimations = [];
  List<Animation<double>> _fadeAnimations = [];

  // Search state
  final TextEditingController _searchController = TextEditingController();

  // Pagination state
  int _currentPage = 1;
  int _itemsPerPage = 12; // Will be dynamically calculated based on screen size
  int _totalPages = 1;

  // JSON string containing book data
  static const String booksJsonString = '''
{
   "books": [
    {
      "title": "Customer Customer Customer",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Product Product Product",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Address Address Address",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Home Rentals Home Rentals Home Rentals",
      "versionNumber": "V00172",
       "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": true
    },
    {
      "title": "Online Grocery Online Grocery Online Grocery",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Courier & Logistics Courier & Logistics Courier & Logistics",
      "versionNumber": "V00172",
       "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Automotive Automotive Automotive",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": true
    },
    {
      "title": "Fitness & Wellness Fitness & Wellness Fitness & Wellness",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Real Estate Real Estate Real Estate",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe Restaurant & Cafe Restaurant & Cafe",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Real Estate Real Estate Real Estate",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe Restaurant & Cafe Restaurant & Cafe",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Real Estate Real Estate Real Estate",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe Restaurant & Cafe Restaurant & Cafe",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Real Estate Real Estate Real Estate",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe Restaurant & Cafe Restaurant & Cafe",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Real Estate Real Estate Real Estate",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe Restaurant & Cafe Restaurant & Cafe",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    }
  ]
}
''';

  @override
  void initState() {
    super.initState();
    _loadBooks();
    _searchController.addListener(_onSearchChanged);
  }

  void _initializeAnimations() {
    // Dispose existing controllers first
    if (_animationControllers.isNotEmpty) {
      for (var controller in _animationControllers) {
        controller.dispose();
      }
    }

    // Only initialize if we have books to animate
    if (books.isEmpty) return;

    _animationControllers = List.generate(
      books.length,
      (index) => AnimationController(
        duration: const Duration(milliseconds: 800),
        vsync: this,
      ),
    );

    _slideAnimations = _animationControllers.map((controller) {
      return Tween<double>(begin: 50.0, end: 0.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeOutCubic),
      );
    }).toList();

    _fadeAnimations = _animationControllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeInOut),
      );
    }).toList();

    // Start animations with staggered delays (bottom to top)
    for (int i = 0; i < _animationControllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 100), () {
        if (mounted) {
          _animationControllers[i].forward();
        }
      });
    }
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    for (var controller in _animationControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _loadBooks() {
    try {
      // Parse the JSON string
      final data = json.decode(booksJsonString);

      // Convert to Book objects
      final List<Book> loadedBooks = (data['books'] as List)
          .map((bookJson) => Book.fromJson(bookJson))
          .toList();

      setState(() {
        allBooks = loadedBooks;
        _filteredBooks = loadedBooks;
        isLoading = false;
      });

      // Update current page books after setting allBooks
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _updateCurrentPageBooks();
        // Initialize animations after books are loaded
        _initializeAnimations();
      });
    } catch (e) {
      setState(() {
        allBooks = [];
        books = [];
        _filteredBooks = [];
        isLoading = false;
      });
      // Log error in a production-safe way
      debugPrint('Error loading books: $e');
    }
  }

  // Handle search text changes
  void _onSearchChanged() {
    _filterBooks();
  }

  // Filter books based on search text
  void _filterBooks() {
    final searchText = _searchController.text.toLowerCase();

    setState(() {
      if (searchText.isEmpty) {
        _filteredBooks = List.from(allBooks);
      } else {
        _filteredBooks = allBooks
            .where((book) =>
                book.title.toLowerCase().contains(searchText) ||
                book.versionNumber.toLowerCase().contains(searchText))
            .toList();
      }

      // Update allBooks to use filtered results for pagination
      allBooks = _filteredBooks;
      // Reset to first page when filtering
      _currentPage = 1;
      _updateCurrentPageBooks();
    });
  }

  // Calculate optimal items per page based on screen size for better grid layout
  int _calculateItemsPerPage() {
    // Add safety check for context availability
    if (!mounted) return 12; // Default fallback

    try {
      final screenWidth = MediaQuery.of(context).size.width;

      if (screenWidth >= 1920) {
        // 8 cards per row, use 16 items (2 full rows) for optimal layout
        return 16;
      } else if (screenWidth >= 1366) {
        // 6 cards per row, use 12 items (2 full rows) for optimal layout
        return 12;
      } else if (screenWidth >= 1240) {
        // 6 cards per row, use 12 items (2 full rows) for optimal layout
        return 12;
      } else if (screenWidth >= 1024) {
        // 3 cards per row, use 9 items (3 full rows) for optimal layout
        return 9;
      } else if (screenWidth >= 768) {
        // 2 cards per row, use 8 items (4 full rows) for optimal layout
        return 8;
      } else {
        // 3 cards per row, use 9 items (3 full rows) for optimal layout
        return 9;
      }
    } catch (e) {
      // Fallback to default if MediaQuery is not available
      return 12;
    }
  }

  void _updateCurrentPageBooks() {
    if (allBooks.isEmpty) {
      setState(() {
        books = [];
        _totalPages = 1;
      });
      return;
    }

    // Update items per page based on current screen size
    _itemsPerPage = _calculateItemsPerPage();

    _totalPages = (allBooks.length / _itemsPerPage).ceil();

    // Ensure current page is valid
    if (_currentPage > _totalPages) {
      _currentPage = _totalPages;
    }
    if (_currentPage < 1) {
      _currentPage = 1;
    }

    final startIndex = (_currentPage - 1) * _itemsPerPage;
    final endIndex = (startIndex + _itemsPerPage).clamp(0, allBooks.length);

    setState(() {
      books = allBooks.sublist(startIndex, endIndex);
    });
  }

  void _goToPreviousPage() {
    if (_currentPage > 1) {
      setState(() {
        _currentPage--;
        _updateCurrentPageBooks();
      });
      _initializeAnimations();
    }
  }

  void _goToNextPage() {
    if (_currentPage < _totalPages) {
      setState(() {
        _currentPage++;
        _updateCurrentPageBooks();
      });
      _initializeAnimations();
    }
  }

  void _showCreateObjectPopup(BuildContext context) {
    // Find the button's position
    final RenderBox? buttonBox = context.findRenderObject() as RenderBox?;
    if (buttonBox == null) return;

    final buttonPosition = buttonBox.localToGlobal(Offset.zero);
    final buttonSize = buttonBox.size;

    showDialog(
      context: context,
      barrierDismissible: true,
      barrierColor: Colors.transparent,
      builder: (BuildContext context) {
        return Stack(
          children: [
            // Invisible barrier to close popup when clicking outside
            Positioned.fill(
              child: GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Container(color: Colors.transparent),
              ),
            ),
            // Positioned popup
            Positioned(
              left: buttonPosition.dx +
                  buttonSize.width -
                  200, // Align to right edge of button
              top: buttonPosition.dy +
                  buttonSize.height +
                  8, // Below the button with 8px gap
              child: CreateObjectPopup(
                onAIGeneratedTap: () {
                  Navigator.of(context).pop();
                  // Navigate to AI Generated screen
                  Provider.of<WebHomeProvider>(context, listen: false)
                      .currentScreenIndex = ScreenConstants.aiGeneratedObject;
                },
                onManualCreationTap: () {
                  Navigator.of(context).pop();
                  // Handle manual creation - you can add your logic here
                  // For now, just show a placeholder
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Manual Creation selected')),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xfff6f6f6),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Top navigation bar
          Padding(
            padding:
                const EdgeInsets.only(left: 94, right: 94, bottom: 0.0, top: 0),
            child: Column(
              children: [
                const HoverNavItems(),
                SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // My Books text
                    Expanded(
                      child: Text(
                        AppLocalizations.of(context)
                            .translate('webobject.pageTitle'),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          fontFamily: "TiemposText",
                        ),
                      ),
                    ),
                    // SizedBox(width: 2),

                    // Search bar with filter
                    Expanded(
                      child: Container(
                        width: MediaQuery.of(context).size.width / 3.722,
                        height: 36,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(color: Colors.grey.shade200),
                        ),
                        child: Row(
                          children: [
                            // Search text field
                            Expanded(
                              child: Padding(
                                padding: const EdgeInsets.only(left: 16.0),
                                child: TextField(
                                  controller: _searchController,
                                  decoration: InputDecoration(
                                    enabledBorder: InputBorder.none,
                                    focusedBorder: InputBorder.none,
                                    hintText: 'Search Objects...',
                                    border: InputBorder.none,
                                    hintStyle: TextStyle(
                                        fontSize: 14,
                                        color: Colors.grey[500]),
                                    isDense: true,
                                    contentPadding: EdgeInsets.zero,
                                    filled: false, 
                                           fillColor: Colors.transparent,
                                  ),
                                ),
                              ),
                            ),
                      
                            // Search icon
                            _HoverSvgButton(
                              normalIconPath: 'assets/images/search.svg',
                              hoverIconPath: 'assets/images/search.svg',
                              onPressed: () {
                                _filterBooks();
                              },
                              imageWidth: 20,
                              imageHeight: 20,
                              showBorderOnHover: false,
                            ),
                      
                            // Divider between search and filter
                            Container(
                              height: 24,
                              width: 1,
                              color: Colors.grey.shade200,
                              margin:
                                  const EdgeInsets.symmetric(horizontal: 4),
                            ),
                      
                            // Filter icon - keeping original properties
                            _HoverSvgButton(
                              normalIconPath:
                                  'assets/images/filter-icon.svg',
                              hoverIconPath:
                                  'assets/images/filter-hover.svg',
                              onPressed: () {},
                              imageWidth: 32,
                              imageHeight: 32,
                              showBorderOnHover: false,
                            ),
                            const SizedBox(width: 8),
                          ],
                        ),
                      ),
                    ),

                    // Create book button
                    Expanded(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Builder(
                            builder: (buttonContext) => HoverCreateButton(
                              text: AppLocalizations.of(context)
                                  .translate('webobject.createButtonText'),
                              onPressed: () {
                                _showCreateObjectPopup(buttonContext);
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 12),
              ],
            ),
          ),
          // Main content
          Expanded(
            child: SingleChildScrollView(
              child: Container(
                padding: const EdgeInsets.only(
                    left: 94, right: 94, bottom: 0.0, top: 0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 32),

                    // Book grid
                    isLoading
                        ? const Center(child: CircularProgressIndicator())
                        : books.isEmpty
                            ? const Center(child: Text('No Objects found'))
                            : Center(
                                child: LayoutBuilder(
                                  builder: (context, constraints) {
                                    // Determine number of books per row based on screen width
                                    int booksPerRow = MediaQuery.of(context)
                                                .size
                                                .width >=
                                            1920
                                        ? 8
                                        : MediaQuery.of(context).size.width >=
                                                1366
                                            ? 6
                                            : MediaQuery.of(context)
                                                        .size
                                                        .width >=
                                                    1240
                                                ? 6
                                                : MediaQuery.of(context)
                                                            .size
                                                            .width >=
                                                        1024
                                                    ? 3
                                                    : MediaQuery.of(context)
                                                                .size
                                                                .width >=
                                                            768
                                                        ? 2
                                                        : 3;

                                    // Calculate rows needed
                                    int rowCount =
                                        (books.length / booksPerRow).ceil();

                                    // Calculate the width available for each book
                                    double availableWidth =
                                        constraints.maxWidth;
                                    double bookWidth =
                                        170.0; // Default book width
                                    double spacing = (availableWidth -
                                            (bookWidth * booksPerRow)) /
                                        (booksPerRow - 1);

                                    // Ensure spacing is not negative
                                    spacing = spacing < 0 ? 16.0 : spacing;

                                    return Column(
                                      children:
                                          List.generate(rowCount, (rowIndex) {
                                        // Calculate start and end indices for this row
                                        int startIndex = rowIndex * booksPerRow;
                                        int endIndex =
                                            (startIndex + booksPerRow <=
                                                    books.length)
                                                ? startIndex + booksPerRow
                                                : books.length;

                                        // Create a list of books for this row
                                        List<Book> rowBooks =
                                            books.sublist(startIndex, endIndex);

                                        // For last row with fewer items, calculate positions
                                        bool isLastRowWithFewerItems =
                                            rowBooks.length < booksPerRow &&
                                                rowIndex == rowCount - 1;

                                        return Padding(
                                          padding: const EdgeInsets.only(
                                              bottom: 42.0),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              // Create books for this row
                                              ...rowBooks.map((book) {
                                                return _HoverBookCard(
                                                  onTap: () {
                                                    Provider.of<WebHomeProvider>(
                                                                context,
                                                                listen: false)
                                                            .currentScreenIndex =
                                                        ScreenConstants
                                                            .webBookSolution;
                                                  },
                                                  child: _buildBookCard(
                                                    title: book.title,
                                                    versionNumber:
                                                        book.versionNumber,
                                                    subtitle: book.subtitle,
                                                    imageUrl: book.imageUrl,
                                                    isDraft: book.isDraft,
                                                    imageWidth: book.imageWidth,
                                                    imageHeight:
                                                        book.imageHeight,
                                                    index: books.indexOf(book),
                                                  ),
                                                );
                                              }),
                                              // Add spacers for last row if needed to maintain alignment
                                              if (isLastRowWithFewerItems)
                                                ...List.generate(
                                                  booksPerRow - rowBooks.length,
                                                  (index) =>
                                                      SizedBox(width: 170.0),
                                                ),
                                            ],
                                          ),
                                        );
                                      }),
                                    );
                                  },
                                ),
                              ),
                    // Pagination controls
                    if (allBooks.isNotEmpty && _totalPages > 1)
                      Container(
                        margin: const EdgeInsets.only(bottom: 10),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // Page info
                            Text(
                              'Page $_currentPage of $_totalPages',
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                fontFamily: "TiemposText",
                                color: Colors.grey,
                              ),
                            ),
                            // Navigation buttons
                            Row(
                              children: [
                                // Previous button
                                _HoverPaginationButton(
                                  icon:
                                      const Icon(Icons.chevron_left, size: 20),
                                  onPressed: _currentPage > 1
                                      ? _goToPreviousPage
                                      : null,
                                ),
                                const SizedBox(width: 8),
                                // Next button
                                _HoverPaginationButton(
                                  icon:
                                      const Icon(Icons.chevron_right, size: 20),
                                  onPressed: _currentPage < _totalPages
                                      ? _goToNextPage
                                      : null,
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ),
          //pagination
        ],
      ),
    );
  }

  Widget _buildBookCard({
    required String title,
    String subtitle = '',
    required String versionNumber,
    required String imageUrl,
    bool isDraft = false,
    double imageWidth = 170.0,
    double imageHeight = 170.0,
    int index = 0,
  }) {
    // Check if animations are initialized and index is valid
    if (index >= _animationControllers.length) {
      // Fallback to non-animated version if index is out of bounds
      return _buildStaticBookCard(
        title: title,
        subtitle: subtitle,
        versionNumber: versionNumber,
        imageUrl: imageUrl,
        isDraft: isDraft,
        imageWidth: imageWidth,
        imageHeight: imageHeight,
      );
    }

    return AnimatedBuilder(
      animation: _animationControllers[index],
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _slideAnimations[index].value),
          child: Opacity(
            opacity: _fadeAnimations[index].value,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Book cover with animation
                AnimatedBuilder(
                  animation: _animationControllers[index],
                  builder: (context, child) {
                    double scale =
                        0.9 + (0.1 * _animationControllers[index].value);

                    return Transform.scale(
                      scale: scale,
                      child: Stack(
                        children: [
                          Container(
                            width: imageWidth,
                            height: imageHeight,
                            decoration: BoxDecoration(
                              image: DecorationImage(
                                image: AssetImage(imageUrl),
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                          if (isDraft)
                            Positioned(
                              top: 10,
                              right: 10,
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: Colors.amber,
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: const Text(
                                  'Draft',
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black,
                                    fontFamily: "TiemposText",
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    );
                  },
                ),
                const SizedBox(height: 12),
                SizedBox(
                  width: imageWidth,
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                      color: Colors.black,
                      fontFamily: "TiemposText",
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                // Version Number
                Padding(
                  padding: const EdgeInsets.only(top: 10),
                  child: Text(
                    versionNumber,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 10,
                      fontFamily: "TiemposText",
                      color: Color(0xff222222),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Fallback static version for cases where animations aren't ready
  Widget _buildStaticBookCard({
    required String title,
    String subtitle = '',
    required String versionNumber,
    required String imageUrl,
    bool isDraft = false,
    double imageWidth = 170.0,
    double imageHeight = 170.0,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Stack(
          children: [
            Container(
              width: imageWidth,
              height: imageHeight,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(imageUrl),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            if (isDraft)
              Positioned(
                top: 10,
                right: 10,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.amber,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'Draft',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                      fontFamily: "TiemposText",
                    ),
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: imageWidth,
          child: Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 14,
              color: Colors.black,
              fontFamily: "TiemposText",
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        // Version Number
        Padding(
          padding: const EdgeInsets.only(top: 10),
          child: Text(
            versionNumber,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 10,
              fontFamily: "TiemposText",
              color: Color(0xff222222),
            ),
          ),
        ),
      ],
    );
  }
}

class _HoverSvgButton extends StatefulWidget {
  final String normalIconPath;
  final String hoverIconPath;
  final VoidCallback onPressed;
  final double imageWidth;
  final double imageHeight;
  final bool showBorderOnHover;

  const _HoverSvgButton({
    required this.normalIconPath,
    required this.hoverIconPath,
    required this.onPressed,
    this.imageWidth = 18,
    this.imageHeight = 18,
    this.showBorderOnHover = true,
  });

  @override
  State<_HoverSvgButton> createState() => _HoverSvgButtonState();
}

class _HoverSvgButtonState extends State<_HoverSvgButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Container(
        height: 32,
        width: 32,
        decoration: BoxDecoration(
          border: widget.showBorderOnHover
              ? Border.all(
                  color: isHovered ? Color(0xff0058FF) : Colors.transparent,
                  width: 1.0,
                )
              : null,
          borderRadius: BorderRadius.circular(4),
        ),
        child: IconButton(
          icon: SvgPicture.asset(
            isHovered ? widget.hoverIconPath : widget.normalIconPath,
            width: widget.imageWidth,
            height: widget.imageHeight,
          ),
          onPressed: widget.onPressed,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
      ),
    );
  }
}

class _HoverPaginationButton extends StatefulWidget {
  final Icon icon;
  final VoidCallback? onPressed;

  const _HoverPaginationButton({
    required this.icon,
    required this.onPressed,
  });

  @override
  State<_HoverPaginationButton> createState() => _HoverPaginationButtonState();
}

class _HoverPaginationButtonState extends State<_HoverPaginationButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    bool isDisabled = widget.onPressed == null;

    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Container(
        width: 24,
        height: 24,
        decoration: BoxDecoration(
          border: Border.all(
            color: isDisabled
                ? Colors.grey.shade200
                : isHovered
                    ? Color(0xff0058FF)
                    : Colors.grey.shade300,
            width: 1.0,
          ),
          // No border radius when hovered
          borderRadius: isHovered && !isDisabled ? BorderRadius.zero : null,
          color: isDisabled ? Colors.grey.shade50 : Colors.white,
        ),
        child: IconButton(
          icon: widget.icon,
          onPressed: widget.onPressed,
          padding: EdgeInsets.zero,
          color: isDisabled
              ? Colors.grey.shade400
              : isHovered
                  ? Color(0xff0058FF)
                  : Colors.black,
          constraints: const BoxConstraints(),
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
      ),
    );
  }
}

class _HoverNavItem extends StatefulWidget {
  final String iconPath;
  final String label;
  final VoidCallback onTap;
  final bool isActive;

  const _HoverNavItem({
    required this.iconPath,
    required this.label,
    required this.onTap,
    this.isActive = false,
  });

  @override
  State<_HoverNavItem> createState() => _HoverNavItemState();
}

class _HoverNavItemState extends State<_HoverNavItem> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: isHovered ? Colors.white : Colors.transparent,
            border: Border.all(
              color: isHovered ? Color(0xff0058FF) : Colors.transparent,
              width: 1.0,
            ),
            borderRadius: BorderRadius.circular(2),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SvgPicture.asset(
                widget.iconPath,
                width: 12,
                height: 12,
                colorFilter: ColorFilter.mode(
                  Colors.black,
                  BlendMode.srcIn,
                ),
              ),
              const SizedBox(width: 4),
              Text(
                widget.label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.black,
                  fontFamily: "TiemposText",
                  fontWeight:
                      widget.isActive ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _HoverBookCard extends StatefulWidget {
  final Widget child;
  final VoidCallback onTap;

  const _HoverBookCard({
    required this.child,
    required this.onTap,
  });

  @override
  State<_HoverBookCard> createState() => _HoverBookCardState();
}

class _HoverBookCardState extends State<_HoverBookCard> {
  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: widget.onTap,
        child: widget.child,
      ),
    );
  }
}
