import 'package:flutter/material.dart';
import '../utils/callback_interpreter.dart';

/// Extension on Color to provide hex string conversion
extension RichTextColorExtension on Color {
  /// Converts a Color to a hex string (without the # prefix)
  String toHexString() {
    return '${r.round().toRadixString(16).padLeft(2, '0')}${g.round().toRadixString(16).padLeft(2, '0')}${b.round().toRadixString(16).padLeft(2, '0')}';
  }
}

/// A comprehensive rich text widget that supports various text formatting options.
///
/// This widget allows for displaying and editing text with multiple formatting options
/// such as bold, italic, underline, different font sizes, colors, and alignment.
class RichTextWidget extends StatefulWidget {
  // Content properties
  final String initialText;
  final List<TextSpan>? initialSpans;
  final bool isEditable;

  // Appearance properties
  final Color textColor;
  final Color backgroundColor;
  final double fontSize;
  final FontWeight fontWeight;
  final String? fontFamily;
  final bool isDarkTheme;
  final TextAlign textAlign;

  // Layout properties
  final double? width;
  final double? height;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;
  final double borderRadius;
  final bool hasBorder;
  final Color borderColor;
  final double borderWidth;
  final bool hasShadow;
  final double elevation;

  // Toolbar properties
  final bool showToolbar;
  final bool showBoldButton;
  final bool showItalicButton;
  final bool showUnderlineButton;
  final bool showStrikethroughButton;
  final bool showColorPicker;
  final bool showFontSizePicker;
  final bool showAlignmentButtons;
  final bool showBulletList;
  final bool showNumberedList;
  final bool showLinkButton;
  final bool showClearFormattingButton;

  // Behavior properties
  final bool isReadOnly;
  final bool isDisabled;
  final bool selectable;
  final bool autofocus;
  final bool enableInteractiveSelection;
  final bool showScrollbar;
  final ScrollPhysics? scrollPhysics;
  final double? maxHeight;

  // Callbacks
  final Function(String)? onTextChanged;
  final Function(List<TextSpan>)? onSpansChanged;
  final Function()? onTap;
  final Function(String)? onLinkTap;

  // Advanced interaction properties
  /// Callback for when the widget is hovered
  final void Function(bool)? onHover;

  /// Callback for when the widget is focused
  final void Function(bool)? onFocus;

  /// Focus node for the widget
  final FocusNode? focusNode;

  /// Color to use when the widget is focused
  final Color? focusColor;

  /// Whether to enable feedback when the widget is interacted with
  final bool enableFeedback;

  /// Callback for when the widget is double-tapped
  final VoidCallback? onDoubleTap;

  /// Callback for when the widget is long-pressed
  final VoidCallback? onLongPress;

  // Accessibility properties
  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Whether to exclude the widget from semantics
  final bool excludeFromSemantics;

  // JSON configuration properties
  /// Callbacks defined in JSON
  final Map<String, dynamic>? jsonCallbacks;

  /// Whether to use JSON callbacks
  final bool useJsonCallbacks;

  /// State to pass to the callback interpreter
  final Map<String, dynamic>? callbackState;

  /// Custom callback handlers
  final Map<String, Function>? customCallbackHandlers;

  /// JSON configuration
  final Map<String, dynamic>? jsonConfig;

  /// Whether to use JSON styling
  final bool useJsonStyling;

  /// Whether to use JSON formatting
  final bool useJsonFormatting;

  // Rich text-specific JSON configuration
  /// Whether to use JSON rich text configuration
  final bool useJsonRichTextConfig;

  /// Rich text-specific JSON configuration
  final Map<String, dynamic>? richTextConfig;

  const RichTextWidget({
    super.key,
    this.initialText = '',
    this.initialSpans,
    this.isEditable = true,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.fontFamily,
    this.isDarkTheme = false,
    this.textAlign = TextAlign.start,
    this.width,
    this.height,
    this.padding = const EdgeInsets.all(12.0),
    this.margin = EdgeInsets.zero,
    this.borderRadius = 4.0,
    this.hasBorder = true,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.showToolbar = true,
    this.showBoldButton = true,
    this.showItalicButton = true,
    this.showUnderlineButton = true,
    this.showStrikethroughButton = true,
    this.showColorPicker = true,
    this.showFontSizePicker = true,
    this.showAlignmentButtons = true,
    this.showBulletList = true,
    this.showNumberedList = true,
    this.showLinkButton = true,
    this.showClearFormattingButton = true,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.selectable = true,
    this.autofocus = false,
    this.enableInteractiveSelection = true,
    this.showScrollbar = true,
    this.scrollPhysics,
    this.maxHeight,
    this.onTextChanged,
    this.onSpansChanged,
    this.onTap,
    this.onLinkTap,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.focusColor,
    this.enableFeedback = true,
    this.onDoubleTap,
    this.onLongPress,
    // Accessibility properties
    this.semanticsLabel,
    this.excludeFromSemantics = false,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
    // Rich text-specific JSON configuration
    this.useJsonRichTextConfig = false,
    this.richTextConfig,
  });

  /// Creates a RichTextWidget from a JSON map
  ///
  /// This factory constructor allows for creating a RichTextWidget from a JSON map,
  /// making it easy to configure the widget from API responses or configuration files.
  factory RichTextWidget.fromJson(Map<String, dynamic> json) {
    // Parse text alignment
    TextAlign textAlign = TextAlign.start;
    if (json.containsKey('textAlign')) {
      final String alignStr = json['textAlign'].toString().toLowerCase();
      if (alignStr == 'center') {
        textAlign = TextAlign.center;
      } else if (alignStr == 'end') {
        textAlign = TextAlign.end;
      } else if (alignStr == 'justify') {
        textAlign = TextAlign.justify;
      } else if (alignStr == 'left') {
        textAlign = TextAlign.left;
      } else if (alignStr == 'right') {
        textAlign = TextAlign.right;
      }
    }

    // Parse font weight
    FontWeight fontWeight = FontWeight.normal;
    if (json.containsKey('fontWeight')) {
      if (json['fontWeight'] is String) {
        switch ((json['fontWeight'] as String).toLowerCase()) {
          case 'thin': fontWeight = FontWeight.w100; break;
          case 'extralight': fontWeight = FontWeight.w200; break;
          case 'light': fontWeight = FontWeight.w300; break;
          case 'regular': fontWeight = FontWeight.w400; break;
          case 'medium': fontWeight = FontWeight.w500; break;
          case 'semibold': fontWeight = FontWeight.w600; break;
          case 'bold': fontWeight = FontWeight.w700; break;
          case 'extrabold': fontWeight = FontWeight.w800; break;
          case 'black': fontWeight = FontWeight.w900; break;
        }
      } else if (json['fontWeight'] is int) {
        final int weight = json['fontWeight'] as int;
        switch (weight) {
          case 100: fontWeight = FontWeight.w100; break;
          case 200: fontWeight = FontWeight.w200; break;
          case 300: fontWeight = FontWeight.w300; break;
          case 400: fontWeight = FontWeight.w400; break;
          case 500: fontWeight = FontWeight.w500; break;
          case 600: fontWeight = FontWeight.w600; break;
          case 700: fontWeight = FontWeight.w700; break;
          case 800: fontWeight = FontWeight.w800; break;
          case 900: fontWeight = FontWeight.w900; break;
        }
      }
    }

    // Parse colors
    Color textColor = Colors.black;
    if (json.containsKey('textColor')) {
      textColor = _parseColor(json['textColor']);
    }

    Color backgroundColor = Colors.white;
    if (json.containsKey('backgroundColor')) {
      backgroundColor = _parseColor(json['backgroundColor']);
    }

    Color borderColor = Colors.grey;
    if (json.containsKey('borderColor')) {
      borderColor = _parseColor(json['borderColor']);
    }

    Color? focusColor;
    if (json.containsKey('focusColor')) {
      focusColor = _parseColor(json['focusColor']);
    }

    // Parse padding and margin
    EdgeInsetsGeometry padding = const EdgeInsets.all(12.0);
    if (json.containsKey('padding')) {
      padding = _parseEdgeInsets(json['padding']);
    }

    EdgeInsetsGeometry margin = EdgeInsets.zero;
    if (json.containsKey('margin')) {
      margin = _parseEdgeInsets(json['margin']);
    }

    // Parse scroll physics
    ScrollPhysics? scrollPhysics;
    if (json.containsKey('scrollPhysics')) {
      final String physicsStr = json['scrollPhysics'].toString().toLowerCase();
      if (physicsStr == 'alwaysscrolling') {
        scrollPhysics = const AlwaysScrollableScrollPhysics();
      } else if (physicsStr == 'bouncing') {
        scrollPhysics = const BouncingScrollPhysics();
      } else if (physicsStr == 'clamping') {
        scrollPhysics = const ClampingScrollPhysics();
      } else if (physicsStr == 'neverscrolling') {
        scrollPhysics = const NeverScrollableScrollPhysics();
      }
    }

    // Parse initial spans
    List<TextSpan>? initialSpans;
    if (json.containsKey('initialSpans') && json['initialSpans'] is List) {
      initialSpans = [];
      for (final spanJson in json['initialSpans']) {
        if (spanJson is Map) {
          final String text = spanJson['text'] as String? ?? '';
          final TextStyle style = _parseTextStyle(spanJson['style']) ?? const TextStyle();
          initialSpans.add(TextSpan(text: text, style: style));
        }
      }
    }

    return RichTextWidget(
      // Content properties
      initialText: json['initialText'] as String? ?? '',
      initialSpans: initialSpans,
      isEditable: json['isEditable'] as bool? ?? true,

      // Appearance properties
      textColor: textColor,
      backgroundColor: backgroundColor,
      fontSize: json['fontSize'] != null ? (json['fontSize'] as num).toDouble() : 16.0,
      fontWeight: fontWeight,
      fontFamily: json['fontFamily'] as String?,
      isDarkTheme: json['isDarkTheme'] as bool? ?? false,
      textAlign: textAlign,

      // Layout properties
      width: json['width'] != null ? (json['width'] as num).toDouble() : null,
      height: json['height'] != null ? (json['height'] as num).toDouble() : null,
      padding: padding,
      margin: margin,
      borderRadius: json['borderRadius'] != null ? (json['borderRadius'] as num).toDouble() : 4.0,
      hasBorder: json['hasBorder'] as bool? ?? true,
      borderColor: borderColor,
      borderWidth: json['borderWidth'] != null ? (json['borderWidth'] as num).toDouble() : 1.0,
      hasShadow: json['hasShadow'] as bool? ?? false,
      elevation: json['elevation'] != null ? (json['elevation'] as num).toDouble() : 2.0,

      // Toolbar properties
      showToolbar: json['showToolbar'] as bool? ?? true,
      showBoldButton: json['showBoldButton'] as bool? ?? true,
      showItalicButton: json['showItalicButton'] as bool? ?? true,
      showUnderlineButton: json['showUnderlineButton'] as bool? ?? true,
      showStrikethroughButton: json['showStrikethroughButton'] as bool? ?? true,
      showColorPicker: json['showColorPicker'] as bool? ?? true,
      showFontSizePicker: json['showFontSizePicker'] as bool? ?? true,
      showAlignmentButtons: json['showAlignmentButtons'] as bool? ?? true,
      showBulletList: json['showBulletList'] as bool? ?? true,
      showNumberedList: json['showNumberedList'] as bool? ?? true,
      showLinkButton: json['showLinkButton'] as bool? ?? true,
      showClearFormattingButton: json['showClearFormattingButton'] as bool? ?? true,

      // Behavior properties
      isReadOnly: json['isReadOnly'] as bool? ?? false,
      isDisabled: json['isDisabled'] as bool? ?? false,
      selectable: json['selectable'] as bool? ?? true,
      autofocus: json['autofocus'] as bool? ?? false,
      enableInteractiveSelection: json['enableInteractiveSelection'] as bool? ?? true,
      showScrollbar: json['showScrollbar'] as bool? ?? true,
      scrollPhysics: scrollPhysics,
      maxHeight: json['maxHeight'] != null ? (json['maxHeight'] as num).toDouble() : null,

      // Advanced interaction properties
      focusColor: focusColor,
      enableFeedback: json['enableFeedback'] as bool? ?? true,

      // Accessibility properties
      semanticsLabel: json['semanticsLabel'] as String?,
      excludeFromSemantics: json['excludeFromSemantics'] as bool? ?? false,

      // JSON configuration properties
      useJsonCallbacks: json['useJsonCallbacks'] as bool? ?? false,
      useJsonStyling: json['useJsonStyling'] as bool? ?? false,
      useJsonFormatting: json['useJsonFormatting'] as bool? ?? false,
      useJsonRichTextConfig: json['useJsonRichTextConfig'] as bool? ?? false,
      jsonConfig: json,
      jsonCallbacks: json.containsKey('callbacks') ? json['callbacks'] as Map<String, dynamic> : null,
    );
  }

  /// Parses a color from a string or map
  static Color _parseColor(dynamic colorValue) {
    if (colorValue is String) {
      if (colorValue.startsWith('#')) {
        // Parse hex color
        String hex = colorValue.replaceFirst('#', '');
        if (hex.length == 3) {
          // Convert 3-digit hex to 6-digit
          hex = hex.split('').map((char) => char + char).join('');
        }
        if (hex.length == 6) {
          hex = 'FF$hex'; // Add alpha channel if not present
        }
        return Color(int.parse(hex, radix: 16));
      } else {
        // Parse named color
        switch (colorValue.toLowerCase()) {
          case 'red': return Colors.red;
          case 'blue': return Colors.blue;
          case 'green': return Colors.green;
          case 'yellow': return Colors.yellow;
          case 'orange': return Colors.orange;
          case 'purple': return Colors.purple;
          case 'pink': return Colors.pink;
          case 'brown': return Colors.brown;
          case 'grey':
          case 'gray': return Colors.grey;
          case 'black': return Colors.black;
          case 'white': return Colors.white;
          case 'transparent': return Colors.transparent;
          default: return Colors.black;
        }
      }
    } else if (colorValue is Map) {
      // Parse RGBA color
      final int r = colorValue['r'] as int? ?? 0;
      final int g = colorValue['g'] as int? ?? 0;
      final int b = colorValue['b'] as int? ?? 0;
      final double a = colorValue['a'] != null ? (colorValue['a'] as num).toDouble() : 1.0;
      return Color.fromRGBO(r, g, b, a);
    }
    return Colors.black; // Default color
  }

  /// Parses edge insets from a map or number
  static EdgeInsetsGeometry _parseEdgeInsets(dynamic value) {
    if (value is num) {
      return EdgeInsets.all(value.toDouble());
    } else if (value is Map) {
      if (value.containsKey('all')) {
        return EdgeInsets.all((value['all'] as num).toDouble());
      } else if (value.containsKey('horizontal') || value.containsKey('vertical')) {
        return EdgeInsets.symmetric(
          horizontal: value.containsKey('horizontal') ? (value['horizontal'] as num).toDouble() : 0.0,
          vertical: value.containsKey('vertical') ? (value['vertical'] as num).toDouble() : 0.0,
        );
      } else {
        return EdgeInsets.fromLTRB(
          value.containsKey('left') ? (value['left'] as num).toDouble() : 0.0,
          value.containsKey('top') ? (value['top'] as num).toDouble() : 0.0,
          value.containsKey('right') ? (value['right'] as num).toDouble() : 0.0,
          value.containsKey('bottom') ? (value['bottom'] as num).toDouble() : 0.0,
        );
      }
    }
    return const EdgeInsets.all(0.0); // Default padding
  }

  /// Parses a text style from a map
  static TextStyle? _parseTextStyle(dynamic value) {
    if (value is! Map) return null;

    Color color = Colors.black;
    if (value.containsKey('color')) {
      color = _parseColor(value['color']);
    }

    double fontSize = 14.0;
    if (value.containsKey('fontSize')) {
      fontSize = (value['fontSize'] as num).toDouble();
    }

    FontWeight fontWeight = FontWeight.normal;
    if (value.containsKey('fontWeight')) {
      if (value['fontWeight'] is String) {
        switch ((value['fontWeight'] as String).toLowerCase()) {
          case 'thin': fontWeight = FontWeight.w100; break;
          case 'extralight': fontWeight = FontWeight.w200; break;
          case 'light': fontWeight = FontWeight.w300; break;
          case 'regular': fontWeight = FontWeight.w400; break;
          case 'medium': fontWeight = FontWeight.w500; break;
          case 'semibold': fontWeight = FontWeight.w600; break;
          case 'bold': fontWeight = FontWeight.w700; break;
          case 'extrabold': fontWeight = FontWeight.w800; break;
          case 'black': fontWeight = FontWeight.w900; break;
        }
      } else if (value['fontWeight'] is int) {
        final int weight = value['fontWeight'] as int;
        switch (weight) {
          case 100: fontWeight = FontWeight.w100; break;
          case 200: fontWeight = FontWeight.w200; break;
          case 300: fontWeight = FontWeight.w300; break;
          case 400: fontWeight = FontWeight.w400; break;
          case 500: fontWeight = FontWeight.w500; break;
          case 600: fontWeight = FontWeight.w600; break;
          case 700: fontWeight = FontWeight.w700; break;
          case 800: fontWeight = FontWeight.w800; break;
          case 900: fontWeight = FontWeight.w900; break;
        }
      }
    }

    FontStyle fontStyle = FontStyle.normal;
    if (value.containsKey('fontStyle')) {
      if (value['fontStyle'] is String && (value['fontStyle'] as String).toLowerCase() == 'italic') {
        fontStyle = FontStyle.italic;
      }
    }

    TextDecoration decoration = TextDecoration.none;
    if (value.containsKey('decoration')) {
      if (value['decoration'] is String) {
        switch ((value['decoration'] as String).toLowerCase()) {
          case 'underline': decoration = TextDecoration.underline; break;
          case 'overline': decoration = TextDecoration.overline; break;
          case 'linethrough': decoration = TextDecoration.lineThrough; break;
        }
      }
    }

    return TextStyle(
      color: color,
      fontSize: fontSize,
      fontWeight: fontWeight,
      fontStyle: fontStyle,
      decoration: decoration,
      fontFamily: value['fontFamily'] as String?,
    );
  }

  /// Converts the widget to a JSON map
  ///
  /// This method allows for serialization of the widget's configuration,
  /// making it easy to save and restore widget state.
  Map<String, dynamic> toJson() {
    return {
      // Content properties
      'initialText': initialText,
      'isEditable': isEditable,

      // Appearance properties
      'textColor': '#${textColor.toHexString()}',
      'backgroundColor': '#${backgroundColor.toHexString()}',
      'fontSize': fontSize,
      'fontWeight': fontWeight.index * 100 + 100,
      'fontFamily': fontFamily,
      'isDarkTheme': isDarkTheme,
      'textAlign': textAlign.toString().split('.').last,

      // Layout properties
      'width': width,
      'height': height,
      'padding': _edgeInsetsToJson(padding),
      'margin': _edgeInsetsToJson(margin),
      'borderRadius': borderRadius,
      'hasBorder': hasBorder,
      'borderColor': '#${borderColor.toHexString()}',
      'borderWidth': borderWidth,
      'hasShadow': hasShadow,
      'elevation': elevation,

      // Toolbar properties
      'showToolbar': showToolbar,
      'showBoldButton': showBoldButton,
      'showItalicButton': showItalicButton,
      'showUnderlineButton': showUnderlineButton,
      'showStrikethroughButton': showStrikethroughButton,
      'showColorPicker': showColorPicker,
      'showFontSizePicker': showFontSizePicker,
      'showAlignmentButtons': showAlignmentButtons,
      'showBulletList': showBulletList,
      'showNumberedList': showNumberedList,
      'showLinkButton': showLinkButton,
      'showClearFormattingButton': showClearFormattingButton,

      // Behavior properties
      'isReadOnly': isReadOnly,
      'isDisabled': isDisabled,
      'selectable': selectable,
      'autofocus': autofocus,
      'enableInteractiveSelection': enableInteractiveSelection,
      'showScrollbar': showScrollbar,
      'maxHeight': maxHeight,

      // Advanced interaction properties
      'focusColor': focusColor != null ? '#${focusColor!.toHexString()}' : null,
      'enableFeedback': enableFeedback,

      // Accessibility properties
      'semanticsLabel': semanticsLabel,
      'excludeFromSemantics': excludeFromSemantics,

      // JSON configuration properties
      'useJsonCallbacks': useJsonCallbacks,
      'useJsonStyling': useJsonStyling,
      'useJsonFormatting': useJsonFormatting,
      'useJsonRichTextConfig': useJsonRichTextConfig,
    };
  }

  /// Converts EdgeInsetsGeometry to a JSON representation
  static Map<String, dynamic> _edgeInsetsToJson(EdgeInsetsGeometry edgeInsets) {
    if (edgeInsets is EdgeInsets) {
      if (edgeInsets.left == edgeInsets.top && edgeInsets.left == edgeInsets.right && edgeInsets.left == edgeInsets.bottom) {
        return {'all': edgeInsets.left};
      } else if (edgeInsets.left == edgeInsets.right && edgeInsets.top == edgeInsets.bottom) {
        return {
          'horizontal': edgeInsets.left,
          'vertical': edgeInsets.top,
        };
      } else {
        return {
          'left': edgeInsets.left,
          'top': edgeInsets.top,
          'right': edgeInsets.right,
          'bottom': edgeInsets.bottom,
        };
      }
    }
    return {'all': 0.0}; // Default
  }

  @override
  State<RichTextWidget> createState() => _RichTextWidgetState();
}

class _RichTextWidgetState extends State<RichTextWidget> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  late String _currentText;
  late List<TextSpan> _currentSpans;

  // Current formatting state
  bool _isBold = false;
  bool _isItalic = false;
  bool _isUnderlined = false;
  bool _isStrikethrough = false;
  Color _currentColor = Colors.black;
  double _currentFontSize = 16.0;
  TextAlign _currentTextAlign = TextAlign.start;
  bool _isBulletList = false;
  bool _isNumberedList = false;

  // Selection state
  int _selectionStart = 0;
  int _selectionEnd = 0;

  // Interaction state
  bool _isFocused = false;

  // Callback state
  Map<String, dynamic> _callbackState = {};

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialText);
    _focusNode = widget.focusNode ?? FocusNode();
    _currentText = widget.initialText;
    _currentSpans = widget.initialSpans ?? [
      TextSpan(
        text: widget.initialText,
        style: TextStyle(
          color: widget.isDarkTheme ? Colors.white : widget.textColor,
          fontSize: widget.fontSize,
          fontWeight: widget.fontWeight,
          fontFamily: widget.fontFamily,
        ),
      ),
    ];
    _currentColor = widget.isDarkTheme ? Colors.white : widget.textColor;
    _currentFontSize = widget.fontSize;
    _currentTextAlign = widget.textAlign;

    _controller.addListener(_handleTextChange);
    _focusNode.addListener(_onFocusChange);

    // Initialize callback state
    if (widget.callbackState != null) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
      if (_isFocused && widget.onFocus != null) {
        widget.onFocus!(_isFocused);
      }
    });
  }

  @override
  void didUpdateWidget(RichTextWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update focus node if changed
    if (widget.focusNode != oldWidget.focusNode) {
      _focusNode.removeListener(_onFocusChange);
      _focusNode = widget.focusNode ?? _focusNode;
      _focusNode.addListener(_onFocusChange);
    }

    // Update callback state if provided
    if (widget.callbackState != null && widget.callbackState != oldWidget.callbackState) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }
  }

  @override
  void dispose() {
    _controller.removeListener(_handleTextChange);
    _controller.dispose();
    _focusNode.removeListener(_onFocusChange);
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  /// Executes a callback defined in JSON
  void _executeJsonCallback(String callbackName, [dynamic value]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    final callback = widget.jsonCallbacks![callbackName];
    if (callback == null) return;

    CallbackInterpreter.executeCallback(
      callback,
      context,
      value: value,
      state: _callbackState,
      customHandlers: widget.customCallbackHandlers,
    );
  }

  void _handleTextChange() {
    final newText = _controller.text;
    if (_currentText != newText) {
      setState(() {
        _currentText = newText;
      });

      // Call standard callback
      if (widget.onTextChanged != null) {
        widget.onTextChanged!(newText);
      }

      // Execute JSON callback if defined
      if (widget.useJsonCallbacks && widget.jsonCallbacks != null &&
          widget.jsonCallbacks!.containsKey('onTextChanged')) {
        _executeJsonCallback('onTextChanged', newText);
      }
    }
  }



  void _toggleBold() {
    setState(() {
      _isBold = !_isBold;
      _applyFormatting();
    });
  }

  void _toggleItalic() {
    setState(() {
      _isItalic = !_isItalic;
      _applyFormatting();
    });
  }

  void _toggleUnderline() {
    setState(() {
      _isUnderlined = !_isUnderlined;
      _applyFormatting();
    });
  }

  void _toggleStrikethrough() {
    setState(() {
      _isStrikethrough = !_isStrikethrough;
      _applyFormatting();
    });
  }

  void _changeColor(Color color) {
    setState(() {
      _currentColor = color;
      _applyFormatting();
    });
  }

  void _changeFontSize(double size) {
    setState(() {
      _currentFontSize = size;
      _applyFormatting();
    });
  }

  void _changeAlignment(TextAlign align) {
    setState(() {
      _currentTextAlign = align;
    });
  }

  void _toggleBulletList() {
    setState(() {
      _isBulletList = !_isBulletList;
      _isNumberedList = false;
      _applyFormatting();
    });
  }

  void _toggleNumberedList() {
    setState(() {
      _isNumberedList = !_isNumberedList;
      _isBulletList = false;
      _applyFormatting();
    });
  }

  void _addLink() {
    // This would be implemented to add a link to the selected text
    // For simplicity, we're just showing a placeholder implementation
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Link'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: const InputDecoration(
                labelText: 'Text',
                hintText: 'Enter link text',
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              decoration: const InputDecoration(
                labelText: 'URL',
                hintText: 'Enter URL',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Add link implementation would go here
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  void _clearFormatting() {
    setState(() {
      _isBold = false;
      _isItalic = false;
      _isUnderlined = false;
      _isStrikethrough = false;
      _currentColor = widget.isDarkTheme ? Colors.white : widget.textColor;
      _currentFontSize = widget.fontSize;
      _isBulletList = false;
      _isNumberedList = false;
      _applyFormatting();
    });
  }

  void _applyFormatting() {
    // This is a simplified implementation
    // In a real implementation, this would apply formatting only to the selected text
    final selectedText = _currentText.substring(_selectionStart, _selectionEnd);

    if (selectedText.isNotEmpty) {
      final TextStyle style = TextStyle(
        color: _currentColor,
        fontSize: _currentFontSize,
        fontWeight: _isBold ? FontWeight.bold : widget.fontWeight,
        fontStyle: _isItalic ? FontStyle.italic : FontStyle.normal,
        decoration: _getTextDecoration(),
        fontFamily: widget.fontFamily,
      );

      final formattedSpan = TextSpan(
        text: selectedText,
        style: style,
      );

      // Update spans (simplified implementation)
      setState(() {
        _currentSpans = [
          if (_selectionStart > 0)
            TextSpan(
              text: _currentText.substring(0, _selectionStart),
              style: TextStyle(
                color: widget.isDarkTheme ? Colors.white : widget.textColor,
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
                fontFamily: widget.fontFamily,
              ),
            ),
          formattedSpan,
          if (_selectionEnd < _currentText.length)
            TextSpan(
              text: _currentText.substring(_selectionEnd),
              style: TextStyle(
                color: widget.isDarkTheme ? Colors.white : widget.textColor,
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
                fontFamily: widget.fontFamily,
              ),
            ),
        ];
      });

      // Call standard callback
      if (widget.onSpansChanged != null) {
        widget.onSpansChanged!(_currentSpans);
      }

      // Execute JSON callback if defined
      if (widget.useJsonCallbacks && widget.jsonCallbacks != null &&
          widget.jsonCallbacks!.containsKey('onSpansChanged')) {
        _executeJsonCallback('onSpansChanged', _currentSpans.map((span) => span.text).toList());
      }
    }
  }

  TextDecoration _getTextDecoration() {
    if (_isUnderlined && _isStrikethrough) {
      return TextDecoration.combine([
        TextDecoration.underline,
        TextDecoration.lineThrough,
      ]);
    } else if (_isUnderlined) {
      return TextDecoration.underline;
    } else if (_isStrikethrough) {
      return TextDecoration.lineThrough;
    }
    return TextDecoration.none;
  }

  Widget _buildToolbar() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
      decoration: BoxDecoration(
        color: widget.isDarkTheme ? Colors.grey.shade800 : Colors.grey.shade200,
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: Wrap(
        spacing: 4.0,
        runSpacing: 4.0,
        children: [
          if (widget.showBoldButton)
            IconButton(
              icon: const Icon(Icons.format_bold),
              onPressed: widget.isDisabled ? null : _toggleBold,
              color: _isBold ? Colors.blue : null,
              tooltip: 'Bold',
              iconSize: 20.0,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(
                minWidth: 36.0,
                minHeight: 36.0,
              ),
            ),
          if (widget.showItalicButton)
            IconButton(
              icon: const Icon(Icons.format_italic),
              onPressed: widget.isDisabled ? null : _toggleItalic,
              color: _isItalic ? Colors.blue : null,
              tooltip: 'Italic',
              iconSize: 20.0,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(
                minWidth: 36.0,
                minHeight: 36.0,
              ),
            ),
          if (widget.showUnderlineButton)
            IconButton(
              icon: const Icon(Icons.format_underlined),
              onPressed: widget.isDisabled ? null : _toggleUnderline,
              color: _isUnderlined ? Colors.blue : null,
              tooltip: 'Underline',
              iconSize: 20.0,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(
                minWidth: 36.0,
                minHeight: 36.0,
              ),
            ),
          if (widget.showStrikethroughButton)
            IconButton(
              icon: const Icon(Icons.strikethrough_s),
              onPressed: widget.isDisabled ? null : _toggleStrikethrough,
              color: _isStrikethrough ? Colors.blue : null,
              tooltip: 'Strikethrough',
              iconSize: 20.0,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(
                minWidth: 36.0,
                minHeight: 36.0,
              ),
            ),
          if (widget.showColorPicker)
            PopupMenuButton<Color>(
              icon: Icon(
                Icons.format_color_text,
                color: _currentColor,
              ),
              tooltip: 'Text Color',
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: Colors.black,
                  child: _ColorItem(color: Colors.black, name: 'Black'),
                ),
                PopupMenuItem(
                  value: Colors.red,
                  child: _ColorItem(color: Colors.red, name: 'Red'),
                ),
                PopupMenuItem(
                  value: Colors.blue,
                  child: _ColorItem(color: Colors.blue, name: 'Blue'),
                ),
                PopupMenuItem(
                  value: Colors.green,
                  child: _ColorItem(color: Colors.green, name: 'Green'),
                ),
                PopupMenuItem(
                  value: Colors.orange,
                  child: _ColorItem(color: Colors.orange, name: 'Orange'),
                ),
                PopupMenuItem(
                  value: Colors.purple,
                  child: _ColorItem(color: Colors.purple, name: 'Purple'),
                ),
              ],
              onSelected: _changeColor,
            ),
          if (widget.showFontSizePicker)
            PopupMenuButton<double>(
              icon: const Icon(Icons.format_size),
              tooltip: 'Font Size',
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 12.0,
                  child: Text('Small', style: TextStyle(fontSize: 12.0)),
                ),
                const PopupMenuItem(
                  value: 16.0,
                  child: Text('Normal', style: TextStyle(fontSize: 16.0)),
                ),
                const PopupMenuItem(
                  value: 20.0,
                  child: Text('Large', style: TextStyle(fontSize: 20.0)),
                ),
                const PopupMenuItem(
                  value: 24.0,
                  child: Text('Extra Large', style: TextStyle(fontSize: 24.0)),
                ),
              ],
              onSelected: _changeFontSize,
            ),
          if (widget.showAlignmentButtons)
            PopupMenuButton<TextAlign>(
              icon: const Icon(Icons.format_align_left),
              tooltip: 'Text Alignment',
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: TextAlign.left,
                  child: Row(
                    children: [
                      Icon(Icons.format_align_left),
                      SizedBox(width: 8.0),
                      Text('Left'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: TextAlign.center,
                  child: Row(
                    children: [
                      Icon(Icons.format_align_center),
                      SizedBox(width: 8.0),
                      Text('Center'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: TextAlign.right,
                  child: Row(
                    children: [
                      Icon(Icons.format_align_right),
                      SizedBox(width: 8.0),
                      Text('Right'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: TextAlign.justify,
                  child: Row(
                    children: [
                      Icon(Icons.format_align_justify),
                      SizedBox(width: 8.0),
                      Text('Justify'),
                    ],
                  ),
                ),
              ],
              onSelected: _changeAlignment,
            ),
          if (widget.showBulletList)
            IconButton(
              icon: const Icon(Icons.format_list_bulleted),
              onPressed: widget.isDisabled ? null : _toggleBulletList,
              color: _isBulletList ? Colors.blue : null,
              tooltip: 'Bullet List',
              iconSize: 20.0,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(
                minWidth: 36.0,
                minHeight: 36.0,
              ),
            ),
          if (widget.showNumberedList)
            IconButton(
              icon: const Icon(Icons.format_list_numbered),
              onPressed: widget.isDisabled ? null : _toggleNumberedList,
              color: _isNumberedList ? Colors.blue : null,
              tooltip: 'Numbered List',
              iconSize: 20.0,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(
                minWidth: 36.0,
                minHeight: 36.0,
              ),
            ),
          if (widget.showLinkButton)
            IconButton(
              icon: const Icon(Icons.link),
              onPressed: widget.isDisabled ? null : _addLink,
              tooltip: 'Insert Link',
              iconSize: 20.0,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(
                minWidth: 36.0,
                minHeight: 36.0,
              ),
            ),
          if (widget.showClearFormattingButton)
            IconButton(
              icon: const Icon(Icons.format_clear),
              onPressed: widget.isDisabled ? null : _clearFormatting,
              tooltip: 'Clear Formatting',
              iconSize: 20.0,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(
                minWidth: 36.0,
                minHeight: 36.0,
              ),
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final effectiveTextColor = widget.isDarkTheme ? Colors.white : widget.textColor;
    final effectiveBackgroundColor = widget.isDarkTheme ? Colors.grey.shade900 : widget.backgroundColor;
    final effectiveBorderColor = widget.isDarkTheme ? Colors.grey.shade600 : widget.borderColor;
    final effectiveShadowColor = widget.isDarkTheme ? Colors.black : Colors.black26;

    Widget content;

    if (widget.isEditable && !widget.isReadOnly && !widget.isDisabled) {
      // Editable rich text field
      content = TextField(
        controller: _controller,
        focusNode: _focusNode,
        style: TextStyle(
          color: effectiveTextColor,
          fontSize: widget.fontSize,
          fontWeight: widget.fontWeight,
          fontFamily: widget.fontFamily,
        ),
        textAlign: _currentTextAlign,
        maxLines: null,
        minLines: 5,
        decoration: InputDecoration(
          filled: true,
          fillColor: effectiveBackgroundColor,
          contentPadding: const EdgeInsets.all(12.0),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            borderSide: widget.hasBorder
                ? BorderSide(
                    color: effectiveBorderColor,
                    width: widget.borderWidth,
                  )
                : BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            borderSide: widget.hasBorder
                ? BorderSide(
                    color: effectiveBorderColor,
                    width: widget.borderWidth,
                  )
                : BorderSide.none,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            borderSide: widget.hasBorder
                ? BorderSide(
                    color: Colors.blue,
                    width: widget.borderWidth * 1.5,
                  )
                : BorderSide.none,
          ),
          disabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            borderSide: widget.hasBorder
                ? BorderSide(
                    color: Colors.grey.shade400,
                    width: widget.borderWidth,
                  )
                : BorderSide.none,
          ),
        ),
        readOnly: widget.isReadOnly,
        enabled: !widget.isDisabled,
        autofocus: widget.autofocus,
        enableInteractiveSelection: widget.enableInteractiveSelection,
        onTap: () {
          // Get the current selection
          final selection = _controller.selection;
          setState(() {
            _selectionStart = selection.start;
            _selectionEnd = selection.end;
          });
          // Call standard callback
          if (widget.onTap != null) {
            widget.onTap!();
          }

          // Execute JSON callback if defined
          if (widget.useJsonCallbacks && widget.jsonCallbacks != null &&
              widget.jsonCallbacks!.containsKey('onTap')) {
            _executeJsonCallback('onTap');
          }
        },
        onChanged: (value) {
          // Call standard callback
          if (widget.onTextChanged != null) {
            widget.onTextChanged!(value);
          }

          // Execute JSON callback if defined
          if (widget.useJsonCallbacks && widget.jsonCallbacks != null &&
              widget.jsonCallbacks!.containsKey('onTextChanged')) {
            _executeJsonCallback('onTextChanged', value);
          }
        },
        onSubmitted: (value) {
          // Handle submission if needed
        },
      );
    } else {
      // Read-only rich text display
      content = SelectableText.rich(
        TextSpan(
          children: _currentSpans,
        ),
        textAlign: _currentTextAlign,
        style: TextStyle(
          color: effectiveTextColor,
          fontSize: widget.fontSize,
          fontWeight: widget.fontWeight,
          fontFamily: widget.fontFamily,
        ),
        onTap: widget.onTap != null || (widget.useJsonCallbacks && widget.jsonCallbacks != null &&
                widget.jsonCallbacks!.containsKey('onTap'))
            ? () {
                // Call standard callback
                if (widget.onTap != null) {
                  widget.onTap!();
                }

                // Execute JSON callback if defined
                if (widget.useJsonCallbacks && widget.jsonCallbacks != null &&
                    widget.jsonCallbacks!.containsKey('onTap')) {
                  _executeJsonCallback('onTap');
                }
              }
            : null,
      );
    }

    Widget mainContent = Container(
      width: widget.width,
      height: widget.height,
      margin: widget.margin,
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.hasBorder
            ? Border.all(
                color: effectiveBorderColor,
                width: widget.borderWidth,
              )
            : null,
        boxShadow: widget.hasShadow
            ? [
                BoxShadow(
                  color: effectiveShadowColor.withAlpha(26), // 0.1 opacity = 26 alpha
                  blurRadius: widget.elevation,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          if (widget.showToolbar && widget.isEditable && !widget.isDisabled)
            _buildToolbar(),
          Expanded(
            child: Padding(
              padding: widget.padding,
              child: widget.showScrollbar
                  ? Scrollbar(
                      child: SingleChildScrollView(
                        physics: widget.scrollPhysics ?? const ClampingScrollPhysics(),
                        child: content,
                      ),
                    )
                  : SingleChildScrollView(
                      physics: widget.scrollPhysics ?? const ClampingScrollPhysics(),
                      child: content,
                    ),
            ),
          ),
        ],
      ),
    );

    // Apply hover detection
    if (widget.onHover != null) {
      mainContent = MouseRegion(
        onEnter: (event) {
          widget.onHover!(true);
        },
        onExit: (event) {
          widget.onHover!(false);
        },
        child: mainContent,
      );
    }

    // Apply double tap detection
    if (widget.onDoubleTap != null) {
      mainContent = GestureDetector(
        onDoubleTap: () {
          // Execute onDoubleTap callback if defined in JSON
          if (widget.useJsonCallbacks && widget.jsonCallbacks != null &&
              widget.jsonCallbacks!.containsKey('onDoubleTap')) {
            _executeJsonCallback('onDoubleTap');
          }

          // Call standard callback
          widget.onDoubleTap!();
        },
        child: mainContent,
      );
    }

    // Apply long press detection
    if (widget.onLongPress != null) {
      mainContent = GestureDetector(
        onLongPress: () {
          // Execute onLongPress callback if defined in JSON
          if (widget.useJsonCallbacks && widget.jsonCallbacks != null &&
              widget.jsonCallbacks!.containsKey('onLongPress')) {
            _executeJsonCallback('onLongPress');
          }

          // Call standard callback
          widget.onLongPress!();
        },
        child: mainContent,
      );
    }

    // Apply accessibility
    if (widget.semanticsLabel != null && !widget.excludeFromSemantics) {
      mainContent = Semantics(
        label: widget.semanticsLabel,
        excludeSemantics: false,
        child: mainContent,
      );
    }

    return mainContent;
  }
}

class _ColorItem extends StatelessWidget {
  final Color color;
  final String name;

  const _ColorItem({
    required this.color,
    required this.name,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: Colors.grey),
          ),
        ),
        const SizedBox(width: 8),
        Text(name),
      ],
    );
  }
}