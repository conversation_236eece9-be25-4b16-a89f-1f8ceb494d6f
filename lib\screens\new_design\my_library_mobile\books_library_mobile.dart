import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';

import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/screens/new_design/my_library_mobile/add_modules_mobileview.dart';
import 'package:nsl/screens/new_design/my_library_mobile/create_book_mobile.dart';
import 'package:nsl/widgets/common/nsl_knowledge_loader.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';
import 'package:nsl/widgets/mobile/library_navbar_mobile.dart';
import 'package:nsl/providers/library_counts_provider.dart';

class BookMobile {
  final String title;
  final String subtitle;
  final String imageUrl;
  final bool isDraft;
  final double imageWidth;
  final double imageHeight;
  final DateTime lastUpdated;

  BookMobile({
    required this.title,
    required this.subtitle,
    required this.imageUrl,
    required this.lastUpdated,
    this.isDraft = false,
    this.imageWidth = 101.0,
    this.imageHeight = 156.0,
  });

  factory BookMobile.fromJson(Map<String, dynamic> json) {
    return BookMobile(
      title: json['title'] as String,
      subtitle: json['subtitle'] as String,
      imageUrl: json['imageUrl'] as String,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      isDraft: json['isDraft'] as bool? ?? false,
      imageWidth: (json['imageWidth'] as num?)?.toDouble() ?? 101.0,
      imageHeight: (json['imageHeight'] as num?)?.toDouble() ?? 156.0,
    );
  }
}

class BooksLibraryMobile extends StatefulWidget {
  const BooksLibraryMobile({
    super.key,
    this.showNavigationBar = true,
  });

  final bool showNavigationBar;

  @override
  State<BooksLibraryMobile> createState() => _BooksLibraryMobileState();
}

class _BooksLibraryMobileState extends State<BooksLibraryMobile>
    with TickerProviderStateMixin {
  // Constants
  static const double _booksPerViewNormal = 2.25;
  static const double _booksPerViewCompact = 3.0;
  static const double _bookAspectRatio = 101.0 / 156.0;
  static const double _titleHeight = 32.0;
  static const double _subtitleHeight = 16.0;
  static const double _verticalSpacing = 12.0;
  static const double _bookSpacing = 30.0;
  static const double _horizontalPadding = 24.0;
  static const int _recentBooksLimit = 10;

  // Text Styles
  static const TextStyle _sectionHeadingStyle = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: Colors.black,
    fontFamily: "TiemposText",
  );

  static const TextStyle _bookTitleStyle = TextStyle(
    fontWeight: FontWeight.w500,
    fontSize: 12,
    height: 1.334,
    color: Colors.black,
    fontFamily: "TiemposText",
  );

  static const TextStyle _bookSubtitleStyle = TextStyle(
    fontWeight: FontWeight.w400,
    fontSize: 11,
    color: Colors.black,
    fontFamily: "TiemposText",
  );

  static const TextStyle _emptyStateStyle = TextStyle(
    fontSize: 16,
    color: Colors.grey,
    fontFamily: "TiemposText",
  );

  // Data
  List<BookMobile> books = [];
  List<BookMobile> recentBooks = [];
  List<BookMobile> allBooks = [];
  List<BookMobile> filteredRecentBooks = [];
  List<BookMobile> filteredAllBooks = [];
  bool isLoading = true;

  // Navigation
  int selectedTabIndex = 0;

  // Controllers
  late CarouselController _recentBooksController;
  late CarouselController _allBooksController;
  final FocusNode _searchFocusNode = FocusNode();
  final TextEditingController _searchController = TextEditingController();
  late AnimationController _loadingAnimationController;

  // Animations
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // UI state
  bool _isKeyboardVisible = false;
  String _searchQuery = '';

  // JSON data with lastUpdated dates
  static const String booksJsonString = '''
{
  "books": [
    {
      "title": "Ecommerce Platform Solutions",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book_01.png",
      "isDraft": false,
      "lastUpdated": "2024-12-15T10:30:00Z"
    },
    {
      "title": "Fashion & Apparel Store",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-02.png",
      "isDraft": false,
      "lastUpdated": "2024-12-18T14:22:00Z"
    },
    {
      "title": "Financial Advisory Platform",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-03.png",
      "isDraft": false,
      "lastUpdated": "2024-12-10T09:15:00Z"
    },
    {
      "title": "Home Rentals App",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": true,
      "lastUpdated": "2024-12-20T16:45:00Z"
    },
    {
      "title": "Online Grocery Store",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-05.png",
      "isDraft": false,
      "lastUpdated": "2024-12-08T11:30:00Z"
    },
    {
      "title": "Courier & Logistics",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-06.png",
      "isDraft": false,
      "lastUpdated": "2024-12-19T13:20:00Z"
    },
    {
      "title": "Automotive Marketplace",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-07.png",
      "isDraft": true,
      "lastUpdated": "2024-12-12T08:45:00Z"
    },
    {
      "title": "Fitness & Wellness App",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-08.png",
      "isDraft": false,
      "lastUpdated": "2024-12-21T15:10:00Z"
    },
    {
      "title": "Real Estate Platform",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-09.png",
      "isDraft": false,
      "lastUpdated": "2024-12-07T12:00:00Z"
    },
    {
      "title": "Restaurant & Cafe",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-16T17:30:00Z"
    },
    {
      "title": "Healthcare Platform",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-22T09:25:00Z"
    },
    {
      "title": "Education Portal",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-05T14:40:00Z"
    },
    {
      "title": "Travel Booking App",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-17T11:15:00Z"
    },
    {
      "title": "Music Streaming",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-11T16:20:00Z"
    },
    {
      "title": "Social Media Platform",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-23T10:50:00Z"
    },
    {
      "title": "Gaming Platform",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-09T13:35:00Z"
    },
    {
      "title": "News & Media App",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-14T15:45:00Z"
    },
    {
      "title": "Banking App",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-24T08:30:00Z"
    },
    {
      "title": "Investment Platform",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-06T12:10:00Z"
    },
    {
      "title": "Delivery Service",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-13T14:55:00Z"
    },
    {
      "title": "Job Portal",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-04T09:40:00Z"
    },
    {
      "title": "Event Management",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-25T16:15:00Z"
    },
    {
      "title": "Video Streaming",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-03T11:25:00Z"
    },
    {
      "title": "Smart Home IoT",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-02T13:50:00Z"
    },
    {
      "title": "Cryptocurrency Exchange",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-01T10:05:00Z"
    }
  ]
}
''';

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeAnimations();
    _loadBooks();
  }

  void _initializeControllers() {
    _recentBooksController = CarouselController();
    _allBooksController = CarouselController();
    _searchFocusNode.addListener(_onSearchFocusChange);
    _searchController.addListener(_onSearchChanged);
  }

  void _initializeAnimations() {
    _loadingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _loadingAnimationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _loadingAnimationController,
      curve: Curves.easeOutCubic,
    ));
  }

  void _onSearchFocusChange() {
    setState(() {
      // Trigger rebuild to check keyboard visibility in build method
    });
  }

  void _onSearchChanged() {
    final query = _searchController.text.toLowerCase().trim();
    setState(() {
      _searchQuery = query;
      _filterBooks();
    });
  }

  void _filterBooks() {
    if (_searchQuery.isEmpty) {
      filteredRecentBooks = recentBooks;
      filteredAllBooks = allBooks;
    } else {
      filteredRecentBooks = recentBooks.where((book) {
        return book.title.toLowerCase().contains(_searchQuery) ||
               book.subtitle.toLowerCase().contains(_searchQuery);
      }).toList();
      
      filteredAllBooks = allBooks.where((book) {
        return book.title.toLowerCase().contains(_searchQuery) ||
               book.subtitle.toLowerCase().contains(_searchQuery);
      }).toList();
    }
  }

  void _performSearch() {
    final query = _searchController.text.toLowerCase().trim();
    setState(() {
      _searchQuery = query;
      _filterBooks();
    });
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _searchQuery = '';
      _filterBooks();
    });
  }

  void _loadBooks() {
    try {
      final data = json.decode(booksJsonString);
      final loadedBooks = (data['books'] as List<dynamic>)
          .map((bookJson) =>
              BookMobile.fromJson(bookJson as Map<String, dynamic>))
          .toList();

      // Keep original order for allBooks (as received from API/JSON)
      final originalOrderBooks = List<BookMobile>.from(loadedBooks);

      // Sort books by lastUpdated date (most recent first) for recentBooks
      loadedBooks.sort((a, b) => b.lastUpdated.compareTo(a.lastUpdated));

      setState(() {
        books = originalOrderBooks; // Original order
        recentBooks =
            loadedBooks.take(_recentBooksLimit).toList(); // Recent sorted
        allBooks = originalOrderBooks; // Original order (as from API/JSON)
        filteredRecentBooks = recentBooks; // Initialize filtered lists
        filteredAllBooks = allBooks;
        isLoading = false;
      });

      // Update the library counts provider with actual count
      if (mounted) {
        Provider.of<LibraryCountsProvider>(context, listen: false)
            .updateBooksCount(books.length);
      }

      // Debug: Print recent books order
      debugPrint('Recent Books (sorted by lastUpdated):');
      for (int i = 0; i < recentBooks.length; i++) {
        debugPrint(
            '${i + 1}. ${recentBooks[i].title} - ${recentBooks[i].lastUpdated}');
      }

      // Debug: Print all books order (original)
      debugPrint('\nAll Books (original API/JSON order):');
      for (int i = 0; i < allBooks.length && i < 5; i++) {
        debugPrint(
            '${i + 1}. ${allBooks[i].title} - ${allBooks[i].lastUpdated}');
      }

      _loadingAnimationController.forward();
    } catch (e) {
      setState(() {
        books = <BookMobile>[];
        recentBooks = <BookMobile>[];
        allBooks = <BookMobile>[];
        isLoading = false;
      });
      debugPrint('Error loading books: $e');
    }
  }

  @override
  void dispose() {
    _recentBooksController.dispose();
    _allBooksController.dispose();
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _searchFocusNode.removeListener(_onSearchFocusChange);
    _searchFocusNode.dispose();
    _loadingAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _updateKeyboardVisibility();
    return _buildBooksLibraryView();
  }

  void _updateKeyboardVisibility() {
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = keyboardHeight > 0;

    if (_isKeyboardVisible != isKeyboardVisible) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            _isKeyboardVisible = isKeyboardVisible;
          });
        }
      });
    }
  }

  Widget _buildBooksLibraryView() {
    return Scaffold(
      backgroundColor: widget.showNavigationBar
          ? const Color(0xfff6f6f6)
          : Colors.transparent,
      drawer: widget.showNavigationBar ? const CustomDrawer() : null,
      appBar: widget.showNavigationBar ? _buildAppBar() : null,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.showNavigationBar) _buildTopNavigation(),
          if (widget.showNavigationBar) _buildSearchAndCreateSection(),
          _buildBooksContent(),
        ],
      ),
      floatingActionButton:
          widget.showNavigationBar ? _buildFloatingActionButton() : null,
    );
  }

  Widget _buildBooksContent() {
    return Expanded(
      child: NSLKnowledgeLoaderWrapper(
        isLoading: isLoading,
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16, 8, 0, 16),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Recent Books Section
                _buildSectionHeading("Recent Books"),
                const SizedBox(height: 12),
                SizedBox(
                  height: _calculateCarouselHeight(),
                  child: _buildRecentBooksCarousel(),
                ),
                const SizedBox(height: 24),
                // All Books Section
                _buildSectionHeading("All Books"),
                const SizedBox(height: 12),
                SizedBox(
                  height: _calculateCarouselHeight(),
                  child: _buildAllBooksCarousel(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Builds section heading
  Widget _buildSectionHeading(String title) {
    return Text(title, style: _sectionHeadingStyle);
  }

  /// Builds Recent Books carousel
  Widget _buildRecentBooksCarousel() {
    final booksToShow = _searchQuery.isEmpty ? recentBooks : filteredRecentBooks;
    return _buildCarousel(
      books: booksToShow,
      controller: _recentBooksController,
      emptyMessage: _searchQuery.isEmpty ? 'No recent books found' : 'No recent books match your search',
    );
  }

  /// Builds All Books carousel
  Widget _buildAllBooksCarousel() {
    final booksToShow = _searchQuery.isEmpty ? allBooks : filteredAllBooks;
    return _buildCarousel(
      books: booksToShow,
      controller: _allBooksController,
      emptyMessage: _searchQuery.isEmpty ? 'No books found' : 'No books match your search',
    );
  }

  /// Builds a generic carousel widget
  Widget _buildCarousel({
    required List<BookMobile> books,
    required CarouselController controller,
    required String emptyMessage,
  }) {
    if (books.isEmpty) {
      return Center(
        child: Text(emptyMessage, style: _emptyStateStyle),
      );
    }

    final itemExtent = _calculateItemExtent();
    return CarouselView(
      padding: EdgeInsets.zero,
      backgroundColor: Colors.transparent,
      controller: controller,
      itemExtent: itemExtent,
      enableSplash: false,
      shape: const RoundedRectangleBorder(borderRadius: BorderRadius.zero),
      shrinkExtent: itemExtent,
      children: books.asMap().entries.map((entry) {
        return _buildBookItem(entry.value, entry.key);
      }).toList(),
    );
  }

  /// Calculate item extent for CarouselView
  double _calculateItemExtent() {
    final screenWidth = MediaQuery.of(context).size.width;
    final booksPerView = _getBooksPerView();
    final availableWidth = screenWidth - (_horizontalPadding * 2);
    return availableWidth / booksPerView;
  }

  /// Builds individual book item widget
  Widget _buildBookItem(BookMobile book, int bookIndex) {
    return GestureDetector(
      onTap: () => _navigateToBookDetails(bookIndex),
      child: AnimatedBuilder(
        animation: _loadingAnimationController,
        builder: (context, child) => FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: _buildBookContent(book),
          ),
        ),
      ),
    );
  }

  /// Builds the book content (cover, title, subtitle)
  Widget _buildBookContent(BookMobile book) {
    final bookDimensions = _calculateBookDimensions(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildBookCover(book, bookDimensions),
        const SizedBox(height: 8),
        _buildBookTitle(book.title, bookDimensions['width']!),
        const SizedBox(height: 4),
        _buildBookSubtitle(book.subtitle, bookDimensions['width']!),
      ],
    );
  }

  /// Builds book cover with SVG background and draft badge
  Widget _buildBookCover(BookMobile book, Map<String, double> dimensions) {
    final bookWidth = dimensions['width']!;
    final bookHeight = dimensions['height']!;

    return Stack(
      clipBehavior: Clip.none,
      children: [
        // Background SVG shape
        Positioned(
          right: -8,
          bottom: 0,
          child: SvgPicture.asset(
            'assets/images/home-lib-shape.svg',
            width: bookWidth * 0.925,
            height: bookHeight * 0.95,
            fit: BoxFit.contain,
          ),
        ),
        // Main book image
        AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          width: bookWidth,
          height: bookHeight,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            image: DecorationImage(
              image: AssetImage(book.imageUrl),
              fit: BoxFit.cover,
            ),
          ),
        ),
        // Draft badge
        if (book.isDraft) _buildDraftBadge(bookWidth, bookHeight),
      ],
    );
  }

  /// Builds draft badge for books
  Widget _buildDraftBadge(double bookWidth, double bookHeight) {
    return Positioned(
      top: bookHeight * 0.08,
      right: bookWidth * 0.14,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        decoration: BoxDecoration(
          color: Colors.amber,
          borderRadius: BorderRadius.circular(4),
          boxShadow: [
            BoxShadow(
              color: Colors.amber.withValues(alpha: 0.3),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: const Text(
          'Draft',
          style: TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.bold,
            color: Colors.black,
            fontFamily: "TiemposText",
          ),
        ),
      ),
    );
  }

  /// Builds book title
  Widget _buildBookTitle(String title, double bookWidth) {
    return SizedBox(
      width: bookWidth,
      child: Text(
        title,
        style: _bookTitleStyle.copyWith(
      height: 1.2,
    ),
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  /// Builds book subtitle
  Widget _buildBookSubtitle(String subtitle, double bookWidth) {
    return SizedBox(
      width: bookWidth,
      child: Text(
        subtitle,
        style: _bookSubtitleStyle,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  /// Navigates to book details screen
  void _navigateToBookDetails(int bookIndex) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AddModulesMobileView(),
      ),
    );
  }

  /// Get books per view based on keyboard visibility (for smooth scrolling)
  double _getBooksPerView() {
    return _isKeyboardVisible ? _booksPerViewCompact : _booksPerViewNormal;
  }

  /// Calculate dynamic book dimensions based on available space
  Map<String, double> _calculateBookDimensions(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final booksPerView = _getBooksPerView();
    final availableWidth = screenWidth - (_horizontalPadding * 2);
    final itemExtent = availableWidth / booksPerView;

    double bookWidth = itemExtent - _bookSpacing;

    // Size constraints for different modes
    if (_isKeyboardVisible) {
      bookWidth = bookWidth.clamp(85.0, 140.0);
    } else {
      bookWidth = bookWidth.clamp(110.0, 170.0);
    }

    final bookHeight = bookWidth / _bookAspectRatio;

    return {
      'width': bookWidth,
      'height': bookHeight,
      'spacing': _bookSpacing,
    };
  }

  /// Calculate dynamic height for CarouselView based on book content
  double _calculateCarouselHeight() {
    final bookDimensions = _calculateBookDimensions(context);
    final bookHeight = bookDimensions['height']!;
    return bookHeight + _verticalSpacing + _titleHeight + _subtitleHeight + 6;
  }

  /// Builds floating action button
  Widget _buildFloatingActionButton() {
    return SizedBox(
      width: 46,
      height: 46,
      child: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const CreateBookMobile(),
            ),
          );
        },
        backgroundColor: const Color(0xff0058FF),
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30),
        ),
        child: const Icon(Icons.add),
      ),
    );
  }

  /// Builds app bar for the screen
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: const Color(0xfff6f6f6),
      surfaceTintColor: Colors.transparent,
      foregroundColor: Colors.black,
      elevation: 0,
      automaticallyImplyLeading: false,
      titleSpacing: 0,
      title: Row(
        children: [
          Builder(
            builder: (context) => IconButton(
              icon: const Icon(Icons.menu, color: Colors.black, size: 24),
              onPressed: () => Scaffold.of(context).openDrawer(),
              padding: const EdgeInsets.symmetric(horizontal: 16),
            ),
          ),
          Expanded(
            child: Text(
              AppLocalizations.of(context).translate('library.pageTitle'),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                fontFamily: 'TiemposText',
                color: Colors.black,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(width: 56),
        ],
      ),
    );
  }

  /// Builds top navigation tabs
  Widget _buildTopNavigation() {
    return Consumer<LibraryCountsProvider>(
      builder: (context, countsProvider, child) {
        return LibraryNavbarMobile(
          selectedTabIndex: selectedTabIndex,
          booksCount: countsProvider.booksCount,
          solutionsCount: countsProvider.solutionsCount,
          objectsCount: countsProvider.objectsCount,
          agentsCount: countsProvider.agentsCount,
        );
      },
    );
  }

  /// Builds search and filter section
  Widget _buildSearchAndCreateSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Container(
        height: 40,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: Row(
          children: [
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(left: 16.0),
                child: TextField(
                  controller: _searchController,
                  focusNode: _searchFocusNode,
                  decoration: InputDecoration(
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    hintText: 'Search books...',
                    border: InputBorder.none,
                    hintStyle: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[500],
                    ),
                    isDense: true,
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ),
            ),
            _MobileSvgButton(
              iconPath: 'assets/images/search.svg',
              onPressed: () {},
              size: 20,
            ),
            Container(
              height: 24,
              width: 1,
              color: Colors.grey.shade200,
              margin: const EdgeInsets.symmetric(horizontal: 4),
            ),
            _MobileSvgButton(
              iconPath: 'assets/images/filter-icon.svg',
              onPressed: () {},
              size: 24,
            ),
            const SizedBox(width: 8),
          ],
        ),
      ),
    );
  }
}

// Mobile SVG Button Widget
class _MobileSvgButton extends StatefulWidget {
  final String iconPath;
  final VoidCallback onPressed;
  final double size;

  const _MobileSvgButton({
    required this.iconPath,
    required this.onPressed,
    this.size = 18,
  });

  @override
  State<_MobileSvgButton> createState() => _MobileSvgButtonState();
}

class _MobileSvgButtonState extends State<_MobileSvgButton> {
  bool isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => isPressed = true),
      onTapUp: (_) => setState(() => isPressed = false),
      onTapCancel: () => setState(() => isPressed = false),
      onTap: widget.onPressed,
      child: Container(
        height: 32,
        width: 32,
        decoration: BoxDecoration(
          border: Border.all(
            color: isPressed ? const Color(0xff0058FF) : Colors.transparent,
            width: 1.0,
          ),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Center(
          child: SvgPicture.asset(
            widget.iconPath,
            width: widget.size,
            height: widget.size,
          ),
        ),
      ),
    );
  }
}
