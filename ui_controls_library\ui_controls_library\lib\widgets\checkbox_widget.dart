import 'package:flutter/material.dart';

class CheckboxWidget extends StatefulWidget {
  // Configurable properties
  final bool initialValue;
  final Color checkColor;
  final Color activeColor;
  final Color checkOutlineColor;
  final Function(bool)? onChanged; // Callback for checkbox state change
  final double size;
  final double checkBoxWidth;
  final String? label;
  final String? displayName;

  final bool showLabel;
  final bool isDisabled;
  final bool isRounded;
  final double borderRadius;
  final double borderWidth;
  final TextAlign labelPosition;
  final EdgeInsetsGeometry padding;
  final bool isVerticalLayout;
  final bool? testValue; // For testing purposes only

  // New properties for multiple options support
  final List<Map<String, dynamic>>? options; // List of checkbox options
  final List<String>? initialSelectedValues; // For multiple selections
  final Function(List<String>)?
  onMultipleChanged; // Callback for multiple selections
  final bool allowMultiple; // Whether to allow multiple selections

  const CheckboxWidget({
    super.key,
    this.initialValue = false, // Default value is false
    this.checkColor = Colors.white, // Default checkmark color is white
    this.activeColor = Colors.blue, // Default active color is blue
    this.checkOutlineColor = Colors.black, // Default outline color is black
    this.onChanged, // Callback for state change
    this.size = 40.0,
    this.checkBoxWidth = 22.0, // Default size is 40
    this.label,
    this.displayName, // Optional label text
    this.showLabel = true, // Whether to show the label
    this.isDisabled = false, // Whether the checkbox is disabled
    this.isRounded = false, // Whether to use rounded corners
    this.borderRadius = 4.0, // Border radius for rounded corners
    this.borderWidth = 1.5, // Border width
    this.labelPosition =
        TextAlign.end, // Position of the label (start=left, end=right)
    this.padding = const EdgeInsets.all(8.0), // Padding around the widget
    this.isVerticalLayout =
        false, // Whether to arrange checkbox and label vertically
    this.testValue, // For testing purposes only
    // New properties for multiple options support
    this.options, // List of checkbox options
    this.initialSelectedValues, // For multiple selections
    this.onMultipleChanged, // Callback for multiple selections
    this.allowMultiple = false, // Whether to allow multiple selections
  });

  /// Creates a CheckboxWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the CheckboxWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Single Checkbox Example JSON:
  /// ```json
  /// {
  ///   "initialValue": true,
  ///   "checkColor": "white",
  ///   "activeColor": "blue",
  ///   "checkOutlineColor": "black",
  ///   "size": 40.0,
  ///   "label": "Accept Terms",
  ///   "displayName": "Terms and Conditions",
  ///   "showLabel": true,
  ///   "isDisabled": false,
  ///   "isRounded": true,
  ///   "borderRadius": 8.0,
  ///   "borderWidth": 1.5,
  ///   "labelPosition": "start",
  ///   "padding": 8.0,
  ///   "isVerticalLayout": false
  /// }
  /// ```
  ///
  /// Multiple Checkboxes Example JSON:
  /// ```json
  /// {
  ///   "displayName": "Select Your Preferences",
  ///   "options": [
  ///     {"value": "option1", "label": "Option 1", "isDisabled": false},
  ///     {"value": "option2", "label": "Option 2", "isDisabled": false},
  ///     {"value": "option3", "label": "Option 3", "isDisabled": true}
  ///   ],
  ///   "initialSelectedValues": ["option1"],
  ///   "allowMultiple": true,
  ///   "activeColor": "blue",
  ///   "checkColor": "white",
  ///   "showLabel": true,
  ///   "isDisabled": false
  /// }
  /// ```
  factory CheckboxWidget.fromJson(Map<String, dynamic> json) {
    // Parse label position
    TextAlign labelPosition = TextAlign.end;
    if (json['labelPosition'] != null) {
      switch (json['labelPosition'].toString().toLowerCase()) {
        case 'start':
        case 'left':
        case 'leading':
          labelPosition = TextAlign.start;
          break;
        case 'end':
        case 'right':
        case 'trailing':
          labelPosition = TextAlign.end;
          break;
      }
    }

    // Parse padding
    EdgeInsetsGeometry padding = const EdgeInsets.all(8.0);
    if (json['padding'] != null) {
      if (json['padding'] is num) {
        double paddingValue = (json['padding'] as num).toDouble();
        padding = EdgeInsets.all(paddingValue);
      }
    }

    // Parse options for multiple checkboxes
    List<Map<String, dynamic>>? options;
    if (json['options'] != null && json['options'] is List) {
      options = List<Map<String, dynamic>>.from(json['options']);
    }

    // Parse initial selected values
    List<String>? initialSelectedValues;
    if (json['initialSelectedValues'] != null &&
        json['initialSelectedValues'] is List) {
      initialSelectedValues = List<String>.from(json['initialSelectedValues']);
    }

    return CheckboxWidget(
      initialValue: json['initialValue'] ?? false,
      checkColor: _colorFromJson(json['checkColor']) ?? Colors.white,
      activeColor: _colorFromJson(json['activeColor']) ?? Color(0xFF0058FF),
      checkOutlineColor:
          _colorFromJson(json['checkOutlineColor']) ?? Colors.black,
      size: (json['size'] as num?)?.toDouble() ?? 40.0,
      checkBoxWidth: (json['checkBoxWidth'] as num?)?.toDouble() ?? 22.0,
      label: json['label'] as String?,
      displayName: json['displayName'] as String?,
      showLabel: json['showLabel'] ?? true,
      isDisabled: json['isDisabled'] ?? false,
      isRounded: json['isRounded'] ?? false,
      borderRadius: (json['borderRadius'] as num?)?.toDouble() ?? 4.0,
      borderWidth: (json['borderWidth'] as num?)?.toDouble() ?? 1.5,
      labelPosition: labelPosition,
      padding: padding,
      isVerticalLayout: json['isVerticalLayout'] ?? false,
      // New properties for multiple options
      options: options,
      initialSelectedValues: initialSelectedValues,
      allowMultiple: json['allowMultiple'] ?? false,
      onChanged: (value) {
        // This would be handled by the app in a real implementation
      },
      onMultipleChanged: (values) {
        // This would be handled by the app in a real implementation
      },
    );
  }

  /// Converts the CheckboxWidget to a JSON map
  ///
  /// This method allows for serializing the widget configuration to JSON,
  /// which can be useful for saving configurations or sharing them.
  Map<String, dynamic> toJson() {
    String labelPositionString = 'end';
    if (labelPosition == TextAlign.start) {
      labelPositionString = 'start';
    }

    double paddingValue = 8.0;
    if (padding is EdgeInsets) {
      final edgeInsets = padding as EdgeInsets;
      if (edgeInsets.left == edgeInsets.top &&
          edgeInsets.left == edgeInsets.right &&
          edgeInsets.left == edgeInsets.bottom) {
        paddingValue = edgeInsets.left;
      }
    }

    Map<String, dynamic> json = {
      'initialValue': initialValue,
      'checkColor': _colorToJson(checkColor),
      'activeColor': _colorToJson(activeColor),
      'checkOutlineColor': _colorToJson(checkOutlineColor),
      'size': size,
      'checkBoxWidth': checkBoxWidth,
      'label': label,
      'displayName': displayName,
      'showLabel': showLabel,
      'isDisabled': isDisabled,
      'isRounded': isRounded,
      'borderRadius': borderRadius,
      'borderWidth': borderWidth,
      'labelPosition': labelPositionString,
      'padding': paddingValue,
      'isVerticalLayout': isVerticalLayout,
      'allowMultiple': allowMultiple,
    };

    // Add multiple options properties if they exist
    if (options != null) {
      json['options'] = options;
    }
    if (initialSelectedValues != null) {
      json['initialSelectedValues'] = initialSelectedValues;
    }

    return json;
  }

  /// Converts a JSON color value to a Flutter Color
  ///
  /// Accepts hex strings (e.g., "#FF0000"), color names (e.g., "red"),
  /// or integer values (e.g., 0xFFFF0000)
  static Color? _colorFromJson(dynamic colorValue) {
    if (colorValue == null) return null;

    if (colorValue is String) {
      // Handle hex strings like "#FF0000"
      if (colorValue.startsWith('#')) {
        String hexColor = colorValue.substring(1);

        // Handle shorthand hex like #RGB
        if (hexColor.length == 3) {
          hexColor = hexColor.split('').map((c) => '$c$c').join('');
        }

        // Add alpha channel if missing
        if (hexColor.length == 6) {
          hexColor = 'FF$hexColor';
        }

        // Parse the hex value
        try {
          return Color(int.parse('0x$hexColor'));
        } catch (e) {
          // Silently handle the error and return null
          return null;
        }
      }

      // Handle named colors - return MaterialColor for standard colors
      // to ensure round-trip conversion works correctly
      switch (colorValue.toLowerCase()) {
        case 'red':
          return Colors.red;
        case 'blue':
          return Color(0xFF0058FF);
        case 'green':
          return Colors.green;
        case 'yellow':
          return Colors.yellow;
        case 'orange':
          return Colors.orange;
        case 'purple':
          return Colors.purple;
        case 'pink':
          return Colors.pink;
        case 'brown':
          return Colors.brown;
        case 'grey':
        case 'gray':
          return Colors.grey;
        case 'black':
          return Colors.black;
        case 'white':
          return Colors.white;
        case 'amber':
          return Colors.amber;
        case 'cyan':
          return Colors.cyan;
        case 'indigo':
          return Colors.indigo;
        case 'lime':
          return Colors.lime;
        case 'teal':
          return Colors.teal;
        default:
          return null;
      }
    } else if (colorValue is int) {
      // Handle integer color values
      return Color(colorValue);
    }

    return null;
  }

  /// Converts a Flutter Color to a JSON representation
  ///
  /// Returns a hex string (e.g., "#FF0000") or a color name for standard colors
  static String _colorToJson(Color color) {
    // Handle standard colors by name for better readability and test compatibility
    if (color == Colors.red) return 'red';
    if (color == Color(0xFF0058FF)) return 'blue';
    if (color == Colors.green) return 'green';
    if (color == Colors.yellow) return 'yellow';
    if (color == Colors.orange) return 'orange';
    if (color == Colors.purple) return 'purple';
    if (color == Colors.pink) return 'pink';
    if (color == Colors.brown) return 'brown';
    if (color == Colors.grey) return 'grey';
    if (color == Colors.black) return 'black';
    if (color == Colors.white) return 'white';
    if (color == Colors.amber) return 'amber';
    if (color == Colors.cyan) return 'cyan';
    if (color == Colors.indigo) return 'indigo';
    if (color == Colors.lime) return 'lime';
    if (color == Colors.teal) return 'teal';

    // For MaterialColor, preserve the original color name if possible
    if (color is MaterialColor) {
      // We'll keep the original MaterialColor in the round-trip conversion
      // by using a special format that our fromJson method can recognize
      if (color == Colors.red) return 'red';
      if (color == Color(0xFF0058FF)) return 'blue';
      if (color == Colors.green) return 'green';
      if (color == Colors.yellow) return 'yellow';
      if (color == Colors.orange) return 'orange';
      if (color == Colors.purple) return 'purple';
      if (color == Colors.pink) return 'pink';
      if (color == Colors.brown) return 'brown';
      if (color == Colors.grey) return 'grey';
      if (color == Colors.amber) return 'amber';
      if (color == Colors.cyan) return 'cyan';
      if (color == Colors.indigo) return 'indigo';
      if (color == Colors.lime) return 'lime';
      if (color == Colors.teal) return 'teal';

      // If it's a MaterialColor but not one of the standard ones,
      // fall back to the hex representation of the primary value
      color = color.shade500;
    }

    // Convert to RGB format and create a hex string for other colors
    final r = color.toString().substring(10, 12);
    final g = color.toString().substring(12, 14);
    final b = color.toString().substring(14, 16);

    return '#$r$g$b';
  }

  @override
  State<CheckboxWidget> createState() => _CheckboxWidgetState();
}

class _CheckboxWidgetState extends State<CheckboxWidget> {
  late bool _isChecked;
  late List<String> _selectedValues;

  @override
  void initState() {
    super.initState();

    // Initialize single checkbox state
    if (widget.testValue != null) {
      _isChecked = widget.testValue!;
    } else {
      _isChecked = widget.initialValue;
    }

    // Initialize multiple checkbox state
    _selectedValues = widget.initialSelectedValues ?? [];
  }

  // Helper method to check if an option is selected
  bool _isOptionSelected(String value) {
    return _selectedValues.contains(value);
  }

  // Helper method to toggle option selection
  void _toggleOption(String value, bool isSelected) {
    setState(() {
      if (isSelected) {
        if (!_selectedValues.contains(value)) {
          _selectedValues.add(value);
        }
      } else {
        _selectedValues.remove(value);
      }
    });

    // Trigger callback for multiple selections
    if (widget.onMultipleChanged != null) {
      widget.onMultipleChanged!(_selectedValues);
    }
  }

  double _getResponsiveFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 16.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 14.0; // Large
    } else if (screenWidth >= 1280) {
      return 12.0; // Medium
    } else {
      return 12.0; // Default for very small screens
    }
  }

  double _getResponsiveValueFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 18.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 16.0; // Large
    } else if (screenWidth >= 1280) {
      return 14.0; // Medium
    } else {
      return 14.0; // Default for very small screens
    }
  }

  @override
  Widget build(BuildContext context) {
    // Check if we should render multiple checkboxes
    if (widget.options != null && widget.options!.isNotEmpty) {
      return _buildMultipleCheckboxes(context);
    } else {
      return _buildSingleCheckbox(context);
    }
  }

  // Build single checkbox (original functionality)
  Widget _buildSingleCheckbox(BuildContext context) {
    final checkbox = Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.displayName ?? 'Checkbox',
          style: TextStyle(
            fontSize: _getResponsiveFontSize(context),
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
            color: widget.isDisabled ? Colors.grey : Colors.black87,
          ),
        ),
        SizedBox(height: _getResponsiveBoxsize(context)),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(
              width: widget.checkBoxWidth,
              height: widget.size,
              child: Checkbox(
                value: _isChecked,
                onChanged:
                    widget.isDisabled
                        ? null
                        : (bool? newValue) {
                          setState(() {
                            _isChecked = newValue!;
                          });
                          if (widget.onChanged != null) {
                            widget.onChanged!(_isChecked);
                          }
                        },
                checkColor: widget.checkColor,
                activeColor: widget.activeColor,
                side: BorderSide(color: widget.checkOutlineColor, width: 1.5),
                shape:
                    widget.isRounded
                        ? RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(3),
                        )
                        : null,
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
            SizedBox(width: 5),
            Text(
              widget.label ?? 'Option',
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: _getResponsiveValueFontSize(context),
                fontWeight: FontWeight.w500,
                color: widget.isDisabled ? Colors.grey : Colors.black87,
              ),
            ),
          ],
        ),
      ],
    );

    if (!widget.showLabel) {
      return Padding(padding: widget.padding, child: checkbox);
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [checkbox],
    );
  }

  // Build multiple checkboxes
  Widget _buildMultipleCheckboxes(BuildContext context) {
    return Padding(
      padding: widget.padding,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Display name for the group
          if (widget.showLabel && widget.displayName != null)
            Padding(
              padding: EdgeInsets.only(bottom: _getResponsiveBoxsize(context)),
              child: Text(
                widget.displayName!,
                style: TextStyle(
                  fontSize: _getResponsiveFontSize(context),
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w500,
                  color: widget.isDisabled ? Colors.grey : Colors.black87,
                ),
              ),
            ),
          // List of checkboxes
          ...widget.options!
              .map((option) => _buildCheckboxOption(context, option))
              .toList(),
        ],
      ),
    );
  }

  // Build individual checkbox option
  Widget _buildCheckboxOption(
    BuildContext context,
    Map<String, dynamic> option,
  ) {
    final String value = option['value']?.toString() ?? '';
    final String label = option['label']?.toString() ?? value;
    final bool isOptionDisabled = option['isDisabled'] ?? widget.isDisabled;
    final bool isSelected = _isOptionSelected(value);

    return Padding(
      padding: EdgeInsets.only(bottom: _getResponsiveBoxsize(context) / 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          SizedBox(
            width: widget.checkBoxWidth,
            height: widget.size,
            child: Checkbox(
              value: isSelected,
              onChanged:
                  isOptionDisabled
                      ? null
                      : (bool? newValue) {
                        _toggleOption(value, newValue ?? false);
                      },
              checkColor: widget.checkColor,
              activeColor: widget.activeColor,
              side: BorderSide(color: widget.checkOutlineColor, width: 1.5),
              shape:
                  widget.isRounded
                      ? RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(3),
                      )
                      : null,
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
          ),
          SizedBox(width: 5),
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: _getResponsiveValueFontSize(context),
                fontWeight: FontWeight.w500,
                color: isOptionDisabled ? Colors.grey : Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  double _getResponsiveBoxsize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 8.0; // Extra Large (>1920px)
    } else if (screenWidth >= 1440) {
      return 8.0; // Large (1440-1920px)
    } else if (screenWidth >= 1280) {
      return 6.0; // Medium (1280-1366px)
    } else if (screenWidth >= 768) {
      return 4.0; // Small (768-1024px)
    } else {
      return 4.0; // Default for very small screens
    }
  }
}
